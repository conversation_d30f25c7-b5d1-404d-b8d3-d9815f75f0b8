#map $http_origin $cors_header {
#    default "https://carco.bg";
#    "~^https?:\/\/([a-zA-z]+.)?carco.bg(:[0-9]+)?$" "$http_origin";
#}

map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

upstream magento_backend {
    server magento:9000;
}

upstream frontend_server {
    server frontend:3000;
}

upstream api_backend {
    server graphapi:9420;
}

log_format main '$remote_addr - $remote_user [$time_local] "$request" ' '--> "$request_filename" ' '$status $body_bytes_sent "$http_referer" ' '"$http_user_agent" "$http_x_forwarded_for"';

server {
    listen 80;
    server_name _;

    root /magento/;
    index index.php;

    add_header X-Content-Type-Options "nosniff";
    add_header Access-Control-Allow-Origin "*";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Frame-Options "SAMEORIGIN";
    add_header Access-Control-Allow-Methods "GET, POST";

    access_log /var/log/nginx/access.log main;
    #access_log off;
    error_log /var/log/nginx/error.log debug;

    location ~ /\. {
        return 418;
    }

    # dummy location to not-rewrite media files
    location ~* "/media/js/.*\d{10}\.(js|css).*$" {
    try_files $uri $uri/ =404;
    }

    location ~* ^/media/catalog/product/cache/(.+) {
        return 404;
    }

    location ~ ^/_health_check {
        return 200;
    }

    location ~ ^/_next {
        proxy_pass http://frontend_server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
    }

    # location to rewrite /js/ files to use timestamp
    location ~* "/js/.*\d{10}\.(js|css).*$" {
        rewrite "^/(.*)\.(\d{10})\.(js|css)$" /$1.$3 last;
    }

    # location to rewrite /skin/ files to use timestamp
    location ~* "/skin/.*\d{10}\.(js|css).*" {
        rewrite "^/(.*)\.(\d{10})\.(js|css)$" /$1.$3 last;
    }

    location ~ ^/(skin|js|media)/ {
        location ~ \.php$ {
            return 418;
        }

        try_files $uri $uri/ =404;

        expires 1d;
    }

    # graphapi endpoint
    location ~ ^/graphql {
        proxy_pass http://api_backend;
    }

    location ~ ^/go-api {
        proxy_pass http://api_backend;
    }

    location = /sitemap.xml {
        return 301 /sitemap_index_default.xml;
    }

    # nextjs with graceful fallback to magento
    location / {
        location ~* \.xml$ {
            try_files $uri $uri/ =404;
        }

        # Try frontend first, fallback to magento on error
        proxy_pass http://frontend_server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;

        # Graceful fallback to Magento when frontend is unavailable
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        error_page 502 503 504 = @magento_fallback;
    }

    # Fallback location for Magento
    location @magento_fallback {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # Fronet end
    # pfg_borica/response - PFG Card payment callback
    # theme_api - API to Magento
    location ~ ^/(theme_api|pfg_borica/response) {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # magento admin
    location ~ ^/(ecomin|index\.php/ecomin|index\.php/stenik_sync|index\.php/clnews(/adminhtml_(news|comments|category))?) {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # PHP entry point for magento
    location ~ ^/index\.php$ {
        try_files $uri =404;

        fastcgi_param  HTTPS off;

        fastcgi_pass   magento_backend;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;

        fastcgi_read_timeout 60s;
        fastcgi_connect_timeout 60s;

        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }
}
