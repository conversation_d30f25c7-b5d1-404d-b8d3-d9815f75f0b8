<?php
    $shop = $this->getShop();
    $shopCategories = $this->getShopCategories();
    $fourthLevelCategories = Mage::helper('pfg_shop/catalog')->getNthLevelCategoriesByCollection($shopCategories, 4);
?>
<section class="container-wide">

</section>
<section class="shopsContainer text-center text-sm-left container">
    <div class="full-width my-3">
        <div class="shop-slider-wrapper">
            <?php $images = $shop->getMediaGalleryImages() ?>
            <?php if (count($images)): ?>
                <div class="shop-slider owl-carousel">
                    <?php foreach ($images as $image): ?>
                        <img  class="item" src="<?php echo Mage::helper('pfg_shop/image')->init($shop, 'image', $image->getFile());?>" alt="<?php echo $this->escapeHtml($image->getLabel() ? $image->getLabel() : $shop->getName()); ?>">
                    <?php endforeach ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="row">
        <div class="col my-3">
	        <?php echo $this->getChildHtml('shop.message'); ?>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-sm-6">
            <div class="row">
                <div class="col-12 col-sm-6">
	                <h2><?php echo $this->escapeHtml(Mage::helper('pfg_shop/shop')->getNamePrefix() . $shop->getName()) ?></h2>
                    <p itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
                        <span itemprop="streetAddress"><?php echo $shop->getAddress(); ?></span>
                    </p>
	                <?php if ($shop->getPhone()): ?>
                        <p itemprop="telephone"><?php echo $this->__('Phone:') ?> <?php echo $shop->getPhone(); ?></p>
	                <?php endif ?>
	                <?php if ($shop->getMobilePhone()): ?>
                        <p>
                            <?php echo $this->__('Mobile:') ?>
                            <?php echo $shop->getMobilePhone(); ?>
                        </p>
	                <?php endif ?>
	                <?php if ($shop->getEmail()): ?>
                        <p itemprop="email"><a class="text-dark" href="mailto:<?php echo $shop->getEmail(); ?>"><?php echo $shop->getEmail(); ?></a></p>
	                <?php endif ?>
	                <?php if ($shopWorkTime = $shop->getBusinessHoursAsText()): ?>
                        <div class="workTime mb-1"><?php echo $shopWorkTime ?></div>
	                <?php endif ?>
	                <?php $workDays = $shop->getBusinessHours(); ?>
                </div>
                <div class="col-12 col-sm-6">
	                <?php if($workDays): ?>
                        <div class="row">
                            <div class="schedule-wrapper col text-right">
                                <p class="h3"><?php echo $this->__('Work hours'); ?></p>
                                <table class="table table-sm">
                                    <tbody>
					                <?php foreach($workDays as $weekday => $workhours): ?>
                                        <tr>
                                            <td class="border-0"><?php echo $this->__($weekday); ?></td>
                                            <td class="border-0">
								                <?php foreach ($workhours as $label => $hour): ?>
									                <?php echo $hour; ?>
									                <?php echo ($label === 'from')? '-' : '' ?>
								                <?php endforeach; ?>
								                <?php echo $this->__('hours'); ?>
                                            </td>
                                        </tr>
					                <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
	                <?php endif; ?>
                </div>
                <div class="col-12 mb-4">
                    <div class="shopMapContainer h-100">
                        <div id="map-canvas"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6">
            <div class="shopMapContainer h-100">
				<?php if ($shop->getVirtualTour()): ?>
                    <div class="position-relative">
                        <iframe src="<?php echo $shop->getVirtualTour() ?>" width="100%" height="590" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
				<?php endif; ?>
                <div class="text-right my-4">
                    <a href="<?php echo Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_WEB).'shops'; ?>" class="btn btn-primary py-2 px-5 rounded-0 viewStoreLink"><?php echo $this->__('Back to all stores') ?></a>
                </div>
            </div>
        </div>
    </div>
</section>

<script type="text/javascript" src="<?php echo Mage::helper('praktis_catalog/google')->getGoogleMapsAPIUrl(); ?>"></script>
<script type="text/javascript">
    function initialize() {
        var styles = [
            {"featureType":"all","elementType":"labels","stylers":[{"visibility":"on"}]},
            {"featureType":"all","elementType":"labels.text.fill","stylers":[{"saturation":36},{"color":"#000000"},{"lightness":40}]},
            {"featureType":"all","elementType":"labels.text.stroke","stylers":[{"visibility":"on"},{"color":"#000000"},{"lightness":16}]},
            {"featureType":"all","elementType":"labels.icon","stylers":[{"visibility":"on"}]},
            {"featureType":"administrative","elementType":"geometry.fill","stylers":[{"color":"#000000"},{"lightness":20}]},
            {"featureType":"administrative","elementType":"geometry.stroke","stylers":[{"color":"#000000"},{"lightness":17},{"weight":1.2}]},
            {"featureType":"administrative.country","elementType":"labels.text.fill","stylers":[{"color":"#ed5929"}]},
            {"featureType":"administrative.locality","elementType":"labels.text.fill","stylers":[{"color":"#c4c4c4"}]},
            {"featureType":"administrative.neighborhood","elementType":"labels.text.fill","stylers":[{"color":"#ed5929"}]},
            {"featureType":"landscape","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":20}]},
            {"featureType":"poi","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":21},{"visibility":"on"}]},
            {"featureType":"poi.business","elementType":"geometry","stylers":[{"visibility":"on"}]},
            {"featureType":"road.highway","elementType":"geometry.fill","stylers":[{"color":"#ed5929"},{"lightness":"0"}]},
            {"featureType":"road.highway","elementType":"geometry.stroke","stylers":[{"visibility":"off"}]},
            {"featureType":"road.highway","elementType":"labels.text.fill","stylers":[{"color":"#ffffff"}]},
            {"featureType":"road.highway","elementType":"labels.text.stroke","stylers":[{"color":"#ed5929"}]},
            {"featureType":"road.arterial","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":18}]},
            {"featureType":"road.arterial","elementType":"geometry.fill","stylers":[{"color":"#575757"}]},
            {"featureType":"road.arterial","elementType":"labels.text.fill","stylers":[{"color":"#ffffff"}]},
            {"featureType":"road.arterial","elementType":"labels.text.stroke","stylers":[{"color":"#2c2c2c"}]},
            {"featureType":"road.local","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":16}]},
            {"featureType":"road.local","elementType":"labels.text.fill","stylers":[{"color":"#999999"}]},
            {"featureType":"transit","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":19}]},
            {"featureType":"water","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":17}]}
        ];
        var myLatLng = new google.maps.LatLng(<?php echo $shop->getLatitude() . ',' . $shop->getLongitude() ?>);
        var mapOptions = {
            center: myLatLng,
            zoom: 17,
            mapTypeControlOptions: {
                mapTypeIds: [google.maps.MapTypeId.ROADMAP, 'map_style']
            }
        };
        var styledMap = new google.maps.StyledMapType(styles, { name: "Styled Map" });
        var map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);
        var image = '<?php echo $this->getSkinUrl('images/mapPin.png') ?>';
        var marker = new google.maps.Marker({
            position: myLatLng,
            map: map,
            icon: image
        });
        map.mapTypes.set('map_style', styledMap);
        map.setMapTypeId('map_style');
    }
    google.maps.event.addDomListener(window, 'load', initialize);
</script>
<script>
	jQuery(function($){
		$('.shop-slider').owlCarousel({
			loop: true,
			autoHeight:true,
			nav: true,
			navText: false,
			autoplay: false,
			addClassActive: true,
			dots: false,
			margin:10,
			items:4,
			responsive:{
				0:{
					items:1
				},
                600:{
					items:2
				},
				1000:{
					items:4
				}
			}
		});
	});
</script>


