<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */

class Apptrian_FacebookPixel_Model_Observer_Customerinit
{
    /**
     * Execute method.
     *
     * @param Varien_Event_Observer $observer
     * @return Apptrian_FacebookPixel_Model_Observer_Customerinit
     */
    public function fireEventCustomerInit(Varien_Event_Observer $observer)
    {
        $customer        = null;
        $customerId      = 0;
        $customerSession = $observer->getEvent()->getCustomerSession();
        
        if ($customerSession) {
            $customer = $customerSession->getCustomer();
            if ($customer) {
                $customerId = $customer->getId();
            }
        }
        
        // You need to unregister first just in case it is already registrated
        Mage::unregister('apptrian_facebookpixel_customer_id');
        
        Mage::register('apptrian_facebookpixel_customer_id', $customerId);
        
        return $this;
    }
}
