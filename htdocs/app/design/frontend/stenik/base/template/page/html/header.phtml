<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var Mage_Page_Block_Html_Header $this
 */
?>

<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<header>
    <div class="wide-area top-line">
        <div class="container">
            <div class="row">
                <?php echo $this->getChildHtml('store_language') ?>
                <?php echo $this->getChildHtml('currency') ?>
                <div class="info-box phone" data-tooltip="<?php echo Mage::getStoreConfig('general/store_information/hours'); ?>">
                    <svg aria-hidden="true" class="icon-svg phone">
                        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#phone' ?>"></use>
                    </svg>
                    <span class="text"><?php echo Mage::getStoreConfig('general/store_information/phone'); ?></span>
                </div>                
                <?php if ($cmsBlockHeaderDelivery = $this->getChildHtml('cms_block.header_delivery')): ?>
                    <div class="info-box delivery absolute-center">
                        <svg aria-hidden="true" class="icon-svg delivery">
                            <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#delivery' ?>"></use>
                        </svg>
                        <?php echo $cmsBlockHeaderDelivery; ?>
                    </div>
                <?php endif ?>
                <div class="right">
                    <?php echo $this->getChildHtml('header_customer'); ?>
                </div>
            </div>
        </div>
    </div>            
    <div class="wide-area header-logo-cart">
        <div class="container">
            <div class="logo-wrapper" itemscope itemtype="http://schema.org/Organization">
                <a href="<?php echo $this->getUrl('') ?>" class="logo" itemprop="url" rel="home">
                    <img itemprop="logo" src="<?php echo $this->getLogoSrc() ?>" alt="<?php echo $this->getLogoAlt() ?>">
                </a>
            </div>
            <?php echo $this->getChildHtml('topSearch') ?>
            <?php echo $this->getChildHtml('header_cart'); ?>
        </div>
    </div>
    <?php echo $this->getChildHtml('stenik.catalog.topnav') ?>            
</header>


