<?php
/** @var Stenik_Widget_Block_Widget_DiscountProductList $this */
?>
<?php foreach ($this->getProductCollection()->getItems() as $_product): ?>
    <a href="<?php echo $_product->getProductUrl(); ?>">
        <div class="formatBox">
            <div class="formatBoxInfo">
                <img src="<?php echo (string)$this->helper('catalog/image')->init($_product, 'thumbnail')->resize(80) ?>" alt="<?php echo $this->getImageLabel($_product, 'thumbnail'); ?>">
                <div class="price-box">
                    <?php if(!$_product->getSpecialPrice()): ?>
                        <span class="regular-price">
                                    <span class="price"><?php echo Mage::helper('core')->formatPrice($_product->getPrice()); ?></span>
                            </span>
                    <?php else: ?>
                        <span class="special-price">
                                    <span class="price"><?php echo Mage::helper('core')->formatPrice($_product->getSpecialPrice()); ?></span>
                            </span>
                        <span class="old-price">
                                    <span class="price"><?php echo Mage::helper('core')->formatPrice($_product->getPrice()); ?></span>
                            </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </a>
<?php endforeach; ?>