<?php
    /**
     * Available helper functions to use:
     *
     * echo $this->helper('stenik_gdprcompliance')->getTermsCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageContent();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageContent();
     */
?>
<?php $helperTermsAndPrivacy = $this->helper('stenik_gdprcompliance/agreement_termsAndPrivacy'); ?>
<?php if ($helperTermsAndPrivacy->isEnabled()): ?>
<li>
    <p class="agree">
        <input type="checkbox" id="agreement-gdpr_terms_and_privacy" name="agreement[gdpr_terms_and_privacy]" value="1" title="<?php echo $this->htmlEscape(strip_tags($helperTermsAndPrivacy->getAgreement())); ?>" class="checkbox required-entry" /><label for="agreement-gdpr_terms_and_privacy"><?php echo $helperTermsAndPrivacy->getAgreement(); ?></label>
    </p>
</li>
<?php endif ?>

<?php $helperPrivacyPolicy = $this->helper('stenik_gdprcompliance/agreement_privacyPolicy'); ?>
<?php if ($helperPrivacyPolicy->isEnabled()): ?>
<li>
    <p class="agree">
        <input type="checkbox" id="agreement-privacy_policy" name="agreement[privacy_policy]" value="1" title="<?php echo $this->htmlEscape(strip_tags($helperPrivacyPolicy->getAgreement())); ?>" class="checkbox required-entry" /><label for="agreement-privacy_policy"><?php echo $helperPrivacyPolicy->getAgreement(); ?></label>
    </p>
    <?php if ($helperPrivacyPolicy->getAdditionalInformation()): ?>
        <div class="agreement-content">
            <p><?php echo $helperPrivacyPolicy->getAdditionalInformation(); ?></p>
        </div>
    <?php endif; ?>
</li>
<?php else: ?>
    <?php if ($helperPrivacyPolicy->getAdditionalInformation()): ?>
        <li>
            <div class="agreement-content">
                <p><?php echo $helperPrivacyPolicy->getAdditionalInformation(); ?></p>
            <div class="agreement-content">
        </li>
    <?php endif; ?>
<?php endif ?>

<?php $helperAge = $this->helper('stenik_gdprcompliance/agreement_age'); ?>
<?php if ($helperAge->isEnabled()): ?>
<li>
    <p class="agree">
        <input type="checkbox" id="agreement-gdpr_age" name="agreement[gdpr_age]" value="1" title="<?php echo $this->htmlEscape(strip_tags($helperAge->getAgreement())); ?>" class="checkbox" /><label for="agreement-gdpr_age"><?php echo $helperAge->getAgreement(); ?></label>
    </p>
    <?php if ($helperAge->getAdditionalInformation()): ?>
        <div class="agreement-content">
            <p><?php echo $helperAge->getAdditionalInformation(); ?></p>
        </div>
    <?php endif; ?>
</li>
<?php else: ?>
    <?php if ($helperAge->getAdditionalInformation()): ?>
        <li>
            <div class="agreement-content">
                <p><?php echo $helperAge->getAdditionalInformation(); ?></p>
            <div class="agreement-content">
        </li>
    <?php endif; ?>
<?php endif ?>

<?php $helperNewsletter = $this->helper('stenik_gdprcompliance/agreement_newsletter'); ?>
<?php if ($this->getDisplayNewsletterCheckbox() && $helperNewsletter->isEnabled()): ?>
<li>
    <p class="agree">
        <input type="checkbox" id="is_subscribed" name="is_subscribed" value="1" title="<?php echo $this->htmlEscape(strip_tags($helperNewsletter->getAgreement())); ?>" class="checkbox" /><label for="is_subscribed"><?php echo $helperNewsletter->getAgreement(); ?></label>
    </p>
</li>
<?php endif; ?>