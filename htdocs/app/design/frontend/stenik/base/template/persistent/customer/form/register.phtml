<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Create account form template
 *
 * @see app/design/frontend/base/default/template/customer/form/register.phtml
 */
/** @var $this Mage_Customer_Block_Form_Register */
?>


<?php echo $this->getChildHtml('form_fields_before')?>
<?php echo $this->getMessagesBlock()->toHtml() ?>

<h1><?php echo $this->__('Registration') ?></h1>

<div class="clearH"></div>

<div class="col-xs-6 account-create">

    <?php echo $this->getChildHtml('inchoo_socialconnect_register')?>

    <form action="<?php echo $this->getPostActionUrl() ?>" method="post" id="form-validate" class="registration-form">

        <input type="hidden" name="success_url" value="<?php echo $this->getSuccessUrl() ?>" />
        <input type="hidden" name="error_url" value="<?php echo $this->getErrorUrl() ?>" />
        <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey() ?>" />

        <?php echo $this->getLayout()->createBlock('customer/widget_name')->setObject($this->getFormData())->setForceUseCustomerAttributes(true)->toHtml() ?>

        <label for="email_address">* <?php echo $this->__('E-mail') ?></label>
        <input type="email" name="email" id="email_address" value="<?php echo $this->escapeHtml($this->getFormData()->getEmail()) ?>" title="<?php echo $this->__('E-mail') ?>" class="input-text validate-email required-entry" />

        <?php $_dob = $this->getLayout()->createBlock('customer/widget_dob') ?>
        <?php if ($_dob->isEnabled()): ?>
            <?php echo $_dob->setDate($this->getFormData()->getDob())->toHtml() ?>
        <?php endif ?>

        <?php if($this->getShowAddressFields()): ?>
            <div class="fieldset">
                <input type="hidden" name="create_address" value="1" />
                <h2 class="legend"><?php echo $this->__('Address Information') ?></h2>
                <ul class="form-list">
                    <li class="fields">
                        <div class="field">
                            <label for="company"><?php echo $this->__('Company') ?></label>
                            <div class="input-box">
                                <input type="text" name="company" id="company" value="<?php echo $this->escapeHtml($this->getFormData()->getCompany()) ?>" title="<?php echo $this->__('Company') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('company') ?>" />
                            </div>
                        </div>
                        <div class="field">
                            <label for="telephone" class="required"><em>*</em><?php echo $this->__('Telephone') ?></label>
                            <div class="input-box">
                                <input type="tel" name="telephone" id="telephone" value="<?php echo $this->escapeHtml($this->getFormData()->getTelephone()) ?>" title="<?php echo $this->__('Telephone') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('telephone') ?>" />
                            </div>
                        </div>
                    </li>
                <?php $_streetValidationClass = $this->helper('customer/address')->getAttributeValidationClass('street'); ?>
                    <li class="wide">
                        <label for="street_1" class="required"><em>*</em><?php echo $this->__('Street Address') ?></label>
                        <div class="input-box">
                            <input type="text" name="street[]" value="<?php echo $this->escapeHtml($this->getFormData()->getStreet(1)) ?>" title="<?php echo $this->__('Street Address') ?>" id="street_1" class="input-text <?php echo $_streetValidationClass ?>" />
                        </div>
                    </li>
                <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                <?php for ($_i = 2, $_n = $this->helper('customer/address')->getStreetLines(); $_i <= $_n; $_i++): ?>
                    <li class="wide">
                        <div class="input-box">
                            <input type="text" name="street[]" value="<?php echo $this->escapeHtml($this->getFormData()->getStreet($_i)) ?>" title="<?php echo $this->__('Street Address %s', $_i) ?>" id="street_<?php echo $_i ?>" class="input-text <?php echo $_streetValidationClass ?>" />
                        </div>
                    </li>
                <?php endfor; ?>
                    <li class="fields">
                        <div class="field">
                            <label for="city" class="required"><em>*</em><?php echo $this->__('City') ?></label>
                            <div class="input-box">
                                <input type="text" name="city" value="<?php echo $this->escapeHtml($this->getFormData()->getCity()) ?>" title="<?php echo $this->__('City') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('city') ?>" id="city" />
                            </div>
                        </div>
                        <div class="field">
                            <label for="region_id" class="required"><em>*</em><?php echo $this->__('State/Province') ?></label>
                            <div class="input-box">
                                <select id="region_id" name="region_id" title="<?php echo $this->__('State/Province') ?>" class="validate-select" style="display:none;">
                                    <option value=""><?php echo $this->__('Please select region, state or province') ?></option>
                                </select>
                                <script type="text/javascript">
                                //<![CDATA[
                                    $('region_id').setAttribute('defaultValue', "<?php echo $this->getFormData()->getRegionId() ?>");
                                //]]>
                                </script>
                                <input type="text" id="region" name="region" value="<?php echo $this->escapeHtml($this->getRegion()) ?>" title="<?php echo $this->__('State/Province') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('region') ?>" style="display:none;" />
                            </div>
                        </div>
                    </li>
                    <li class="fields">
                        <div class="field">
                            <label for="zip" class="required"><em>*</em><?php echo $this->__('Zip/Postal Code') ?></label>
                            <div class="input-box">
                                <input type="text" name="postcode" value="<?php echo $this->escapeHtml($this->getFormData()->getPostcode()) ?>" title="<?php echo $this->__('Zip/Postal Code') ?>" id="zip" class="input-text validate-zip-international <?php echo $this->helper('customer/address')->getAttributeValidationClass('postcode') ?>" />
                            </div>
                        </div>
                        <div class="field">
                            <label for="country" class="required"><em>*</em><?php echo $this->__('Country') ?></label>
                            <div class="input-box">
                                <?php echo $this->getCountryHtmlSelect() ?>
                            </div>
                        </div>
                    </li>
                </ul>
                <input type="hidden" name="default_billing" value="1" />
                <input type="hidden" name="default_shipping" value="1" />
            </div>
        <?php endif; ?>

        <label for="password">* <?php echo $this->__('Password') ?></label>
        <input type="password" name="password" id="password" title="<?php echo $this->__('Password') ?>" class="input-text required-entry validate-password" />

        <label for="confirmation">* <?php echo $this->__('Confirm Password') ?></label>
        <input type="password" name="confirmation" title="<?php echo $this->__('Confirm Password') ?>" id="confirmation" class="input-text required-entry validate-cpassword" />

        <?php echo $this->getChildHtml('persistent.remember.me'); ?>

        <?php echo $this->getChildHtml('persistent.remember.me.tooltip'); ?>

        <?php $_gender = $this->getLayout()->createBlock('customer/widget_gender') ?>
        <?php if ($_gender->isEnabled()): ?>
            <?php echo $_gender->setGender($this->getFormData()->getGender())->toHtml() ?>
        <?php endif ?>

        <div class="clearH"></div>
        <?php $_taxvat = $this->getLayout()->createBlock('customer/widget_taxvat') ?>
        <?php if ($_taxvat->isEnabled()): ?>
            <?php echo $_taxvat->setTaxvat($this->getFormData()->getTaxvat())->toHtml() ?>
        <?php endif ?>


        <div class="checkbox-content">
            <?php if (Mage::helper('core')->isModuleEnabled('Stenik_GdprCompliance') && $this->helper('stenik_gdprcompliance')->isEnabled()): ?>
                <?php echo $this->getLayout()->createBlock('stenik_gdprcompliance/widget_customer_register')->setFormData($this->getFormData())->setDisplayNewsletterCheckbox(true)->toHtml() ?>
            <?php else: ?>
                <?php if ($this->isNewsletterEnabled()): ?>
                    <input type="checkbox" name="is_subscribed" title="<?php echo $this->__('Sign Up for Newsletter') ?>" value="1" id="is_subscribed" class="checkbox" checked="checked" />
                    <label for="is_subscribed"><?php echo $this->__('Sign Up for Newsletter') ?></label>
                    <div class="clear"></div>
                <?php endif ?>
                <input type="checkbox" class="checkbox required-entry" id="terms">
                <label for="terms">
                    <?php echo $this->__('I read <a href="%s" class="%s">terms and conditions</a> and I accept them.', '#termsPopUp', 'termsOpen');?>
                </label>
                <div style="display:none;">
                    <div id="termsPopUp" class="text-page terms-popup">
                        <?php
                            $page = Mage::getModel('cms/page')->getCollection()
                                  ->addStoreFilter(Mage::app()->getStore()->getId())
                                  ->addFieldToFilter('identifier','terms')->getFirstItem();
                            $html = Mage::helper('cms')->getPageTemplateProcessor()->filter($page->getContent());
                        ?>
                        <h1><?php echo $page->getTitle() ?></h1>
                        <?php echo $this->getMessagesBlock()->toHtml() . $html ?>
                    </div>
                </div>
                <script>
                    jQuery(function(){
                        jQuery('.termsOpen').colorbox({inline:true});
                    });
                </script>
            <?php endif ?>
        </div>

        <div class="clearH2"></div>

        <?php echo $this->getChildHtml('form.additional.info'); ?>

        <div class="clearH"></div>

        <button type="submit" title="<?php echo $this->__('Register Now') ?>" class="button"><?php echo $this->__('Register Now') ?></button>

        <?php if (Mage::helper('checkout')->isContextCheckout()): ?>
            <input name="context" type="hidden" value="checkout" />
        <?php endif; ?>
    </form>

</div>

<?php if ($cmsBlockCustomerRegistration = $this->getChildHtml('cms_block.customer_registration')): ?>
    <div class="col-xs-6 registration-banner">
        <div class="widget-box wide">
            <?php echo $cmsBlockCustomerRegistration; ?>
        </div>
    </div>
<?php endif ?>

<script>
    var dataForm = new VarienForm('form-validate', true);
    <?php if($this->getShowAddressFields()): ?>
    new RegionUpdater('country', 'region', 'region_id', <?php echo $this->helper('directory')->getRegionJson() ?>, undefined, 'zip');
    <?php endif; ?>
</script>


