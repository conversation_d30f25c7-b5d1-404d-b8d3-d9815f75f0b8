<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var $this Mage_Tax_Block_Checkout_Tax
 * @see Mage_Tax_Block_Checkout_Tax
 */
?>
<?php
    $_value = $this->getTotal()->getValue();
    $_style = $this->getTotal()->getStyle();
?>
<?php global $taxIter; $taxIter++; ?>

<?php if ($this->helper('tax')->displayFullSummary() && $_value!=0): ?>
    
    <?php if (0): ?>
        <?php $isTop = 1; ?>
        <?php foreach ($this->getTotal()->getFullInfo() as $info): ?>
            <?php if (isset($info['hidden']) && $info['hidden']) continue; ?>
            <?php $percent = $info['percent']; ?>
            <?php $amount = $info['amount']; ?>
            <?php $rates = $info['rates']; ?>
            <?php $isFirst = 1; ?>

            <?php foreach ($rates as $rate): ?>
            <tr class="summary-details-<?php echo $taxIter; ?> summary-details<?php if ($isTop): echo ' summary-details-first'; endif; ?>" style="display:none;">
                <th>
                    <?php echo $this->escapeHtml($rate['title']); ?>
                    <?php if (!is_null($rate['percent'])): ?>
                        (<?php echo (float)$rate['percent']; ?>%)
                    <?php endif; ?>
                </th>
                <?php if ($isFirst): ?>
                    <td>
                        <?php echo $this->helper('checkout')->formatPrice($amount); ?>
                    </td>
                <?php endif; ?>
            </tr>
            <?php $isFirst = 0; ?>
            <?php $isTop = 0; ?>
            <?php endforeach; ?>
        <?php endforeach; ?>

        <?php $weees = $this->getAllWeee(); ?>
        <?php foreach ($weees as $weeeTitle => $weeeAmount): ?>
            <tr class="summary-details-<?php echo $taxIter; ?> summary-details<?php if ($isTop): echo ' summary-details-first'; endif; ?>" style="display:none;">
                <th>
                    <?php echo $this->escapeHtml($weeeTitle); ?>
                </th>
                <td>
                    <?php echo $this->helper('checkout')->formatPrice($weeeAmount); ?>
                </td>
            </tr>
        <?php endforeach; ?>
    <?php endif ?>

<?php endif;?>

<?php if (0): ?>
    <tr <?php if ($this->helper('tax')->displayFullSummary() && $_value!=0): ?> class="summary-total" onclick="expandDetails(this, '.summary-details-<?php echo $taxIter;?>')"<?php endif; ?>>
        <th>
            <?php if ($this->helper('tax')->displayFullSummary()): ?>
                <div class="summary-collapse"><?php echo $this->getTotal()->getTitle() ?></div>
            <?php else: ?>
                <?php echo $this->getTotal()->getTitle() ?>
            <?php endif;?>
        </th>
        <td><?php echo $this->helper('checkout')->formatPrice($_value) ?></td>
    </tr>
<?php endif ?>
