<?php
/**
 * Pager template
 *
 * @see Mage_Page_Block_Html_Pager
 */
?>
<?php if($this->getCollection()->getSize()): ?>
    <?php if($this->getLastPageNum()>1): ?>
        <div class="pager">
            <div class="pages">
                <a class="prev<?php if ($this->isFirstPage()): ?> disable<?php endif;?>" href="<?php if ($this->isFirstPage()): ?>javascript:;<?php else: ?><?php if ($this->isPageCurrent(2)) echo $this->getPagerUrl(array($this->getPageVarName() => null)); else echo $this->getPreviousPageUrl() ?><?php endif;?>" title="<?php echo $this->__('Previous page') ?>"></a>

                <?php if ($this->canShowFirst()): ?>
                    <a class="first" href="<?php echo $this->getFirstPageUrl() ?>">1</a>
                <?php endif;?>
                <?php if ($this->canShowPreviousJump()): ?>
                    <a class="previous_jump" title="" href="<?php echo $this->getPreviousJumpUrl() ?>">...</a>
                <?php endif;?>
                <?php foreach ($this->getFramePages() as $_page): ?>
                    <?php if ($this->isPageCurrent($_page)): ?>
                        <a class="selected" href="<?php echo $this->getPageUrl($_page) ?>"><?php echo $_page ?></a>
                    <?php else: ?>
                        <?php if ($_page == 1): // needed in order to generate url without ?p=1 parameter?>
                        <a href="<?php echo $this->getPagerUrl(array($this->getPageVarName() => null)); ?>"><?php echo $_page ?></a>
                        <?php else: ?>
                        <a href="<?php echo $this->getPageUrl($_page) ?>"><?php echo $_page ?></a>
                        <?php endif; ?>
                    <?php endif;?>
                <?php endforeach;?>
                <?php if ($this->canShowNextJump()): ?>
                    <a class="next_jump" title="" href="<?php echo $this->getNextJumpUrl() ?>">...</a>
                <?php endif;?>
                <?php if ($this->canShowLast()): ?>
                  <a class="last" href="<?php echo $this->getLastPageUrl() ?>"><?php echo $this->getLastPageNum() ?></a>
                <?php endif;?>

                <a class="next<?php if ($this->isLastPage()): ?> disable<?php endif;?>" href="<?php if ($this->isLastPage()): ?>javascript:;<?php else: ?><?php echo $this->getNextPageUrl() ?><?php endif;?>" title="<?php echo $this->__('Next page') ?>"></a>
            </div>
        </div>
    <?php endif; ?>
<?php endif ?>
