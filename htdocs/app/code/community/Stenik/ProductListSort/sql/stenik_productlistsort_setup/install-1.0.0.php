<?php
/**
 * @package Stenik_ProductListSort
 * <AUTHOR> Magento Team <<EMAIL>>
 */
$installer = new Mage_Catalog_Model_Resource_Setup('core_setup');
$installer->startSetup();

$installer->addAttribute(Mage_Catalog_Model_Product::ENTITY, Stenik_ProductListSort_Helper_Data::ATTR_CODE_SORT_BY_BEST_DISCOUNT,
    array(
        'label'            => 'Best Discount (Percent)',
        'type'             => 'int',
        'used_for_sort_by' => true,
        'visible'          => false,
        'required'         => false,
        'system'           => true,
    )
);

$installer->addAttribute(Mage_Catalog_Model_Product::ENTITY, Stenik_ProductListSort_Helper_Data::ATTR_CODE_SORT_RECENTLY_ADDED,
    array(
        'label'            => 'Recently Added',
        'type'             => 'int',
        'used_for_sort_by' => true,
        'visible'          => false,
        'required'         => false,
        'system'           => true,
    )
);

$installer->addAttribute(Mage_Catalog_Model_Product::ENTITY, Stenik_ProductListSort_Helper_Data::ATTR_CODE_SORT_BEST_SELLERS,
    array(
        'label'            => 'Best Sellers',
        'type'             => 'int',
        'used_for_sort_by' => true,
        'visible'          => false,
        'required'         => false,
        'system'           => true,
    )
);

$installer->addAttribute(Mage_Catalog_Model_Product::ENTITY, Stenik_ProductListSort_Helper_Data::ATTR_CODE_SORT_MOST_REVIEWED,
    array(
        'label'            => 'Most Reviewed',
        'type'             => 'int',
        'used_for_sort_by' => true,
        'visible'          => false,
        'required'         => false,
        'system'           => true,
    )
);