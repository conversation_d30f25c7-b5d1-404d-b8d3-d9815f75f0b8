<?php
/**
 * Observer
 *
 * @package Stenik_CustomerInvoiceFields
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_CustomerInvoiceFields_Model_Observer extends Mage_Core_Model_Abstract
{
    /**
     * Latest transport object
     *
     *
     * @var null|Varien_Object
     */
    protected static $_latestTransportObject = null;

    /**
     * Sales quote save before
     *
     * @event sales_quote_save_before
     *
     * @param  Varien_Event_Observer
     * @return void
     */
    public function salesQuoteSaveBefore(Varien_Event_Observer $observer)
    {
        $quote = $observer->getEvent()->getQuote();

        $invoicePostData = Mage::app()->getRequest()->getPost('quote');

        if ($invoicePostData && isset($invoicePostData['customer_invoice_update']) && $invoicePostData['customer_invoice_update']) {
            $invoiceData = array(
                'customer_invoice'               => null,
                'customer_invoice_type'          => null,
                'customer_invoice_company_name'  => null,
                'customer_invoice_company_pic'   => null,
                'customer_invoice_company_city'  => null,
                'customer_invoice_company_addr'  => null,
                'customer_invoice_company_urn'   => null,
                'customer_invoice_company_vat'   => null,
                'customer_invoice_personal_name' => null,
                'customer_invoice_personal_pin'  => null,
                'customer_invoice_personal_city' => null,
                'customer_invoice_personal_addr' => null,
                'taxvat'                         => null,
            );

            $transportObject = new Varien_Object(array(
                'invoice_data'      => $invoiceData,
                'invoice_post_data' => $invoicePostData,
            ));
            Mage::dispatchEvent('stenik_customerinvoicefields_quote_save_before_prepare_invoice_data', array(
                'transport_object' => $transportObject,
                'quote'            => $quote,
            ));

            $invoiceData     = $transportObject->getInvoiceData();
            $invoicePostData = $transportObject->getInvoicePostData();

            if (isset($invoicePostData['customer_invoice']) && $invoicePostData['customer_invoice']) {
                $invoiceData = array_merge($invoiceData, array_intersect_key($invoicePostData, $invoiceData));
            }

            Mage::getSingleton('checkout/session')->setData('stenik_customerinvoicefields_quote_invoice_parsed', $quote->getId());
            $quote->addData($invoiceData);
        }
    }

    /**
     * Customer save before
     *
     * @event customer_save_before
     *
     * @param  Varien_Event_Observer
     * @return void
     */
    public function customerSaveBefore(Varien_Event_Observer $observer)
    {
        if ($observer->getEvent()->getCustomer() instanceof Mage_Customer_Model_Customer) {
            $customer = $observer->getEvent()->getCustomer();

            $invoicePostData = Mage::app()->getRequest()->getPost();

            if ($invoicePostData && isset($invoicePostData['customer_invoice_update']) && $invoicePostData['customer_invoice_update']) {
                $invoiceData = array(
                    'customer_invoice'               => null,
                    'customer_invoice_type'          => null,
                    'customer_invoice_company_name'  => null,
                    'customer_invoice_company_pic'   => null,
                    'customer_invoice_company_city'  => null,
                    'customer_invoice_company_addr'  => null,
                    'customer_invoice_company_urn'   => null,
                    'customer_invoice_company_vat'   => null,
                    'customer_invoice_personal_name' => null,
                    'customer_invoice_personal_pin'  => null,
                    'customer_invoice_personal_city' => null,
                    'customer_invoice_personal_addr' => null,
                    'taxvat'                         => null,
                );

                $transportObject = new Varien_Object(array(
                    'invoice_data'      => $invoiceData,
                    'invoice_post_data' => $invoicePostData,
                ));
                Mage::dispatchEvent('stenik_customerinvoicefields_customer_save_before_prepare_invoice_data', array(
                    'transport_object' => $transportObject,
                    'customer'         => $customer,
                ));

                $invoiceData     = $transportObject->getInvoiceData();
                $invoicePostData = $transportObject->getInvoicePostData();

                if (isset($invoicePostData['customer_invoice']) && $invoicePostData['customer_invoice']) {
                    $invoiceData = array_merge($invoiceData, array_intersect_key($invoicePostData, $invoiceData));
                }

                $customer->addData($invoiceData);
            }
        }
    }

    /**
     * Frontend - Core block abstract to html after
     *
     * @event core_block_abstract_to_html_after
     *
     * @param  Varien_Event_Observer
     * @return void
     */
    public function frontendCoreBlockAbstractToHtmlAfter(Varien_Event_Observer $observer)
    {
        Varien_Profiler::start(__METHOD__);
        $block = $observer->getEvent()->getBlock();
        $transport = $observer->getEvent()->getTransport();

        self::$_latestTransportObject = $transport;

        if ($this->_canAppendInvoiceProgressBlock($block)) {
            $transport = clone $transport;
            $this->_appendInvoiceProgressBlock($block, $transport);
            self::$_latestTransportObject->setData($transport->getData());
        }
        Varien_Profiler::stop(__METHOD__);
    }

    /**
     * Checkout submit all after
     *
     * @event checkout_submit_all_after
     *
     * @param  Varien_Event_Observer
     * @return void
     */
    public function checkoutSubmitAllAfter(Varien_Event_Observer $observer)
    {
        $order = $observer->getEvent()->getOrder();

        if ($order->getData('customer_invoice') &&
            $order->getCustomer() &&
            $order->getCustomer()->getId()
        ) {
            $customer = $order->getCustomer();

            $invoiceFields = array(
                // to customer                   => from order
                'customer_invoice'               => 'customer_invoice',
                'customer_invoice_type'          => 'customer_invoice_type',
                'customer_invoice_company_name'  => 'customer_invoice_company_name',
                'customer_invoice_company_pic'   => 'customer_invoice_company_pic',
                'customer_invoice_company_city'  => 'customer_invoice_company_city',
                'customer_invoice_company_addr'  => 'customer_invoice_company_addr',
                'customer_invoice_company_urn'   => 'customer_invoice_company_urn',
                'customer_invoice_company_vat'   => 'customer_invoice_company_vat',
                'customer_invoice_personal_name' => 'customer_invoice_personal_name',
                'customer_invoice_personal_pin'  => 'customer_invoice_personal_pin',
                'customer_invoice_personal_city' => 'customer_invoice_personal_city',
                'customer_invoice_personal_addr' => 'customer_invoice_personal_addr',
                'taxvat'                         => 'customer_invoice_company_vat',
            );

            $transportObject = new Varien_Object(array('invoice_fields' => $invoiceFields));
            Mage::dispatchEvent('stenik_customerinvoicefields_checkout_submit_all_after_prepare_invoice_fields', array(
                'transport_object' => $transportObject,
                'customer'         => $customer,
                'order'            => $order,
            ));

            $invoiceFields = $transportObject->getInvoiceFields();

            foreach ($invoiceFields as $toField => $fromField) {
                $customer->setData($toField, $order->getData($fromField));
            }

            $customer->save();
        }

        Mage::getSingleton('checkout/session')->getData('stenik_customerinvoicefields_quote_invoice_parsed', false);
    }

    /**
     * Retrieve if invoice progress block can be appended
     *
     * @param  Varien_Object $afterBlock
     * @return boolean
     */
    protected function _canAppendInvoiceProgressBlock(Varien_Object $afterBlock)
    {
        if (!($afterBlock instanceof Mage_Checkout_Block_Onepage_Progress)) {
            return false;
        }

        if ($afterBlock->getNameInLayout() == 'billing.progress') {
            return true;
        }

        if ($afterBlock->getNameInLayout() == 'root' && in_array('checkout_onepage_progress_billing', $afterBlock->getLayout()->getUpdate()->getHandles())) {
            return true;
        }

        return false;
    }

    /**
     * Append invoice progress block
     *
     * @param Varien_Object $block
     * @param Varien_Object $transport
     */
    protected function _appendInvoiceProgressBlock(Varien_Object $afterBlock, Varien_Object $transport)
    {
        $invoiceProgressBlock = $afterBlock->getLayout()->createBlock('checkout/onepage_progress')->setTemplate('stenik/customerinvoicefields/checkout/onepage/progress/invoice.phtml');
        $transport->setHtml($transport->getHtml() . $invoiceProgressBlock->toHtml());
    }
}