<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_item = $this->getItem()?>

<div class="review-item">

    <span class="cart-img-wrapper">
        <img src="<?php echo $this->getProductThumbnail()->resize(75,75) ?>" alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"/>
    </span>

    <div class="review-item-info">

        <span class="item-title"><?php echo $this->escapeHtml($this->getProductName()) ?></span>

        <?php if ($_options = $this->getOptionList()):?>
            <dl class="item-options">
                <?php foreach ($_options as $_option) : ?>
                <?php $_formatedOptionValue = $this->getFormatedOptionValue($_option) ?>
                <dt><?php echo $this->escapeHtml($_option['label']) ?>:</dt>
                <dd<?php if (isset($_formatedOptionValue['full_view'])): ?> class="truncated"<?php endif; ?>><?php echo $_formatedOptionValue['value'] ?>
                    <?php if (isset($_formatedOptionValue['full_view'])): ?>
                    <div class="truncated_full_value">
                        <dl class="item-options">
                            <dt><?php echo $this->escapeHtml($_option['label']) ?></dt>
                            <dd><?php echo $_formatedOptionValue['full_view'] ?></dd>
                        </dl>
                    </div>
                    <?php endif; ?>
                </dd>
                <?php endforeach; ?>
            </dl>
        <?php endif;?>

        <?php if ($addtInfoBlock = $this->getProductAdditionalInformationBlock()):?>
            <?php echo $addtInfoBlock->setItem($_item)->toHtml() ?>
        <?php endif;?>

        <span class="item-qty"><?php echo $this->__('Item: '); ?><?php echo $_item->getQty() ?></span>

        <?php //echo Mage::helper('stenik_themebase/checkout_cart')->getItemPriceRenderer($_item)->setShowAsRowTotal(true)->toHtml(); ?>
        <?php echo Mage::helper('stenik_themebase/checkout_cart')->getItemPriceRenderer($_item)->toHtml(); ?>

    </div>

</div>

