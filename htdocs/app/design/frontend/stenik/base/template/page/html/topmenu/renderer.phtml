<?php
/**
 * Top menu for store
 *
 * @see Mage_Page_Block_Html_Topmenu::getHtml
 */

$currentCategory = Mage::registry('current_category') ? : Mage::getModel('catalog/category');
?>

<?php if ($menuTree = $this->getMenuTree()): ?>

    <?php foreach ($menuTree->getChildren() as $menuNode): ?>

        <?php $categoryId = str_replace('category-node-', '', $menuNode->getId()); ?>

        <?php if ($menuNode->hasChildren()): ?>  
            <li class="has-sub">
                <a href="<?php echo $menuNode->getUrl() ?>">
                    <?php echo $this->escapeHtml($menuNode->getName()) ?>
                </a>
                <div class="sub-nav">

                    <?php $splitMenuItem = ceil($menuNode->getChildren()->count() / 3) ?>
                    <?php $i = 0; ?>

                    <div class="sub-nav-col">
                        <?php foreach ($menuNode->getChildren() as $menuSubNode): ?>
                            <?php $i++; ?>
                            <ul>
                                <li>
                                    <a href="<?php echo $menuSubNode->getUrl() ?>" class="sub-cat-name ">
                                        <?php echo $this->escapeHtml($menuSubNode->getName()) ?>
                                    </a>
                                    <?php if ($menuSubNode->hasChildren()): ?> 
                                        <ul class="sub-sub-list">
                                            <?php foreach ($menuSubNode->getChildren() as $menuSubSubNode): ?>
                                                <li>
                                                    <a href="<?php echo $menuSubSubNode->getUrl() ?>">
                                                        <?php echo $this->escapeHtml($menuSubSubNode->getName()) ?>  
                                                    </a>
                                                </li>
                                            <?php endforeach ?>
                                        </ul>
                                    <?php endif ?>
                                </li>
                            </ul>
                            <?php if ($i != $menuNode->getChildren()->count() && $i%$splitMenuItem   == 0): ?>
                                </div>
                                <div class="sub-nav-col"> 
                            <?php endif ?>
                        <?php endforeach ?>
                    </div>

                    <div class="sub-nav-col banner">
                        <?php echo $this->getLayout()->createBlock('cms/block')->setBlockId('mainmenu_' . $categoryId)->toHtml(); ?>
                    </div>
                </div>
            </li>
        <?php else: ?>
            <li>
                <a href="<?php echo $menuNode->getUrl() ?>">
                    <?php echo $this->escapeHtml($menuNode->getName()) ?>
                </a>
            </li>
        <?php endif ?>
    <?php endforeach ?>

<?php endif ?>
