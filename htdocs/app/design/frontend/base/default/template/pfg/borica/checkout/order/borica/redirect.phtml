<?php
    /** @var $this PFG_Borica_Block_Checkout_Order_Borica_Redirect */
    $order = $this->getOrder();
?>
<?php if (is_object($order) || !$order->getId()): ?>
<h1><?php echo $this->__('You will be redirected to Borica\'s website to complete your payment.') ?></h1>
<div style="display:none;">
    <?php echo $this->getFormHtml(); ?>
</div>
    <?php $formFields = $this->getFormFields() ?>
    <?php if (is_array($formFields) && !empty($formFields)): ?>
    <h2><?php echo $this->__('Your order details') ?>:</h2>
        <?php if ($amount = $formFields['AMOUNT'] ?? false): ?>
    <div>
        <span class="borica-field-label"><?php echo $this->__('Order Amount') ?></span>
        <span class="borica-field-value"><?php echo $amount ?></span>
    </div>
        <?php endif; ?>

    <?php endif;?>
    <script type="text/javascript">
        window.setTimeout(function () {
            document.getElementById('boricaForm').submit();
        }, 100);
    </script>
<?php else: ?>
<h2><?php echo $this->__('An error has occurred. You will be redirected to the home page.') ?></h2>
<script type="text/javascript">
    setTimeout(function(){window.location.href = '<?php echo Mage::getBaseUrl() ?>'}, 100);
</script>
<?php endif; ?>
