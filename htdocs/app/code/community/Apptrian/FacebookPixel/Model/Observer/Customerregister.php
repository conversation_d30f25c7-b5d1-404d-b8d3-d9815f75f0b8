<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */

class Apptrian_FacebookPixel_Model_Observer_Customerregister
{
    /**
     * @var Apptrian_FacebookPixel_Helper_Data
     */
    public $helper;
    
    /**
     * Execute method.
     *
     * @param Varien_Event_Observer $observer
     * @return Apptrian_FacebookPixel_Model_Observer_Customerregister
     */
    public function fireEventCustomerRegister(Varien_Event_Observer $observer)
    {
        $this->helper = Mage::helper('apptrian_facebookpixel');
        
        $firingMode = $this->helper->getFiringMode();
        
        if ($firingMode != 2) {
            return $this;
        }
        
        $data = array();
        
        $customerId = $observer->getCustomer()->getId();
        
        if ($customerId) {
            $data = $this->helper->getDataForServerCompleteRegistrationEvent($customerId);
        }
        
        if (empty($data)) {
            return $this;
        }
        
        $this->helper->fireServerEvent($data);
        
        return $this;
    }
}
