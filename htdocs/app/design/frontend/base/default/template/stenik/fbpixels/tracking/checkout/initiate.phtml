<?php
    $helper = $this->helper('stenik_fbpixels');

    if($helper->isEventHidden('initiate_checkout'))
        return;

    if(!$_quote = Mage::getModel('checkout/cart')->getQuote())
        return;

    $productSkus = '';
    foreach ($_quote->getAllVisibleItems() as $_item) {
        if($productSkus != '')
            $productSkus .= ',';

        $productSkus .= '"'.$_item->getProduct()->getData('sku').'"';
    }
/**
 * Other Parameters
 *     value, currency, content_name, content_category, content_ids, num_items
 *
 * content_name and content_category are skipped because of reasons
 */
?>
<script type="text/javascript">

document.observe("dom:loaded", function() {
    var cartData = {
        ids: [<?php echo $productSkus; ?>],
        value: '<?php echo $_quote->getGrandTotal(); ?>',
        currency: '<?php echo $_quote->getQuoteCurrencyCode(); ?>',
        numItems: '<?php echo $_quote->getItemsCount(); ?>'
    };

    $$('.btn-proceed-checkout').invoke('observe', 'click', function() {
        sFbTrackInitCheckout(cartData);
    });
});
</script>