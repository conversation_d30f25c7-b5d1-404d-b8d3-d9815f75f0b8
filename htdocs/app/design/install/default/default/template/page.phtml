<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Template for Mage_Page_Block_Html
 */
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?php echo Mage::helper('install')->__('Magento Installation Wizard') ?></title>

<link rel="icon" href="<?php echo $this->getSkinUrl('favicon.ico') ?>" type="image/x-icon" />
<link rel="shortcut icon" href="<?php echo $this->getSkinUrl('favicon.ico') ?>" type="image/x-icon" />

<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>prototype/prototype.js"></script>
<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>prototype/validation.js"></script>
<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>scriptaculous/effects.js"></script>
<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>mage/translate.js" ></script>
<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>varien/js.js"></script>
<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>varien/form.js"></script>
<link rel="stylesheet" href="<?php echo $this->getSkinUrl('css/reset.css') ?>" type="text/css" media="all" />
<link rel="stylesheet" href="<?php echo $this->getSkinUrl('css/boxes.css') ?>" type="text/css" media="all" />
<link rel="stylesheet" href="<?php echo $this->getSkinUrl('css/clears.css') ?>" type="text/css" media="all" />
<!--[if IE]><link rel="stylesheet" href="<?php echo $this->getSkinUrl('css/iestyles.css') ?>" type="text/css" media="all" /><![endif]-->
<!--[if lt IE 7]><link rel="stylesheet" href="<?php echo $this->getSkinUrl('css/ie7minus.css') ?>" type="text/css" media="all" /><![endif]-->

<?php echo $this->helper('core/js')->getTranslatorScript() ?>

</head>

<body>
<!-- [start] header -->
    <div class="header">
        <div class="header-top-container">
            <div class="header-top">
                <h1 id="logo">
                    <a href="<?php echo $this->getUrl('') ?>"><img src="<?php echo $this->getSkinUrl('images/logo.gif') ?>" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('install')->__('Magento')) ?>" alt="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('install')->__('Magento')) ?>" /></a>
                </h1>

                <?php echo $this->getChildHtml('topSearch') ?>

                <div class="quick-access">
                    <div class="account-access">
                       <strong>&nbsp;<?php echo $this->getWelcome() ?></strong> <?php echo $this->getChildHtml('topLeftLinks') ?>
                    </div>
                    <div class="shop-access">
                        <?php echo $this->getChildHtml('topRightLinks') ?>
                    </div>
                </div>

            </div>
        </div>
        <?php echo $this->getChildHtml('topMenu') ?>
    </div>
<!-- [end] header -->

        <!-- [start] middle -->
       <?php echo $this->getChildHtml('store') ?>
    <div class="middle-container">
        <div class="middle col-2-left-layout">
            <?php echo $this->getChildHtml('breadcrumbs') ?>

            <!-- [start] left -->
            <div class="col-left side-col">
                <?php echo $this->getChildHtml('left') ?>
            </div>
            <div id="main" class="col-main">
            <!-- [start] global messages -->
                <?php echo $this->getChildHtml('global_messages') ?>
            <!-- [end] global messages -->
            <!-- [start] content -->
                <?php echo $this->getChildHtml('content') ?>
            <!-- [end] content -->
            </div>
        </div>
            <!-- [end] center -->
    </div>
        <!-- [end] middle -->

        <!-- [start] footer -->
    <div class="footer-container">
        <div class="footer">
        <p class="legality">
            <?php echo Mage::helper('install')->__('Help Us to Keep Magento Healthy') ?> - <a href="http://www.magentocommerce.com/bug-tracking" id="bug_tracking_link"><strong><?php echo Mage::helper('install')->__('Report All Bugs') ?></strong></a> <?php echo Mage::helper('install')->__('(ver. %s)', Mage::getVersion()) ?><br/>
<script type="text/javascript">
//<![CDATA[
    $('bug_tracking_link').target = "varien_external";
//]]>
</script>
            <?php echo Mage::helper('install')->__('Magento is a trademark of Magento Inc. Copyright &copy; %s Magento Inc.', date('Y')) ?>
        </p>
        </div>
    </div>
        <!-- [end] footer -->
    </body>
</html>
