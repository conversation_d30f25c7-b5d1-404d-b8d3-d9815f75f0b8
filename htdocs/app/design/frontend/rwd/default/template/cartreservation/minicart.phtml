<?php
/*

Plumrocket Inc.

NOTICE OF LICENSE

This source file is subject to the End-user License Agreement
that is available through the world-wide-web at this URL:
http://wiki.plumrocket.net/wiki/EULA
If you are unable to obtain it through the world-wide-web, please
send an <NAME_EMAIL> so we can send you a copy immediately.

@package    Plumrocket_Cart_Reservation-v1.5.x
@copyright  Copyright (c) 2013 Plumrocket Inc. (http://www.plumrocket.com)
@license    http://wiki.plumrocket.net/wiki/EULA  End-user License Agreement
 
*/
?>

<?php
$_cartQty = $this->getSummaryCount();
if(empty($_cartQty)) {
    $_cartQty = 0;
}
?>
<div id="minicart-error-message" class="minicart-message"></div>
<div id="minicart-success-message" class="minicart-message"></div>

<div class="minicart-wrapper">

    <?php
        echo $this->getLayout()->createBlock('cartreservation/cart')
            ->setTemplate('cartreservation/cart.phtml')
            ->toHtml();
    ?>

    <p class="block-subtitle">
        <?php echo $this->__('Recently added item(s)') ?>
        <a class="close skip-link-close" href="#" title="<?php echo $this->quoteEscape($this->__('Close')); ?>">&times;</a>
    </p>

    <?php $_items = $this->getRecentItems() ?>
    <?php $countItems = count($_items); ?>
    <?php if($countItems): ?>
        <div>
            <ul id="cart-sidebar" class="mini-products-list">
                <?php foreach($_items as $_item): ?>
                    <?php echo $this->getItemHtml($_item) ?>
                <?php endforeach; ?>
            </ul>
        </div>
        <script type="text/javascript">
            truncateOptions();
            decorateList('cart-sidebar', 'none-recursive');
            $j('document').ready(function() {
                var minicartOptions  = {
                    formKey:           "<?php echo $this->getFormKey();?>"
                }
                var Mini = new Minicart(minicartOptions);
                Mini.init();
            });
        </script>

        <div id="minicart-widgets">
            <?php echo $this->getChildHtml('cart_promotion') ?>
        </div>
        <div class="block-content">
            <p class="subtotal">
                <?php if ($this->canApplyMsrp()): ?>
                    <span class="map-cart-sidebar-total"><?php echo $this->__('ORDER TOTAL WILL BE DISPLAYED BEFORE YOU SUBMIT THE ORDER'); ?></span>
                <?php else: ?>
                    <span class="label"><?php echo $this->__('Cart Subtotal:') ?></span> <?php echo Mage::helper('checkout')->formatPrice($this->getSubtotal()) ?>
                    <?php if ($_subtotalInclTax = $this->getSubtotalInclTax()): ?>
                        <br />(<?php echo Mage::helper('checkout')->formatPrice($_subtotalInclTax) ?> <?php echo Mage::helper('tax')->getIncExcText(true) ?>)
                    <?php endif; ?>
                <?php endif; ?>
            </p>
        </div>


        <div class="minicart-actions">
            <?php if ($_cartQty) : // && $this->isPossibleOnepageCheckout()): ?>
                <ul class="checkout-types minicart">
                    <?php echo $this->getChildHtml('extra_actions') ?>
                    <li>
                        <a title="<?php echo $this->quoteEscape($this->__('Checkout')) ?>" class="button checkout-button" href="<?php echo $this->getCheckoutUrl() ?>">
                            <?php echo $this->__('Checkout') ?>
                        </a>
                    </li>
                </ul>
            <?php endif ?>
            <a class="cart-link" href="<?php echo $this->getUrl('checkout/cart'); ?>">
                <?php echo $this->__('View Shopping Cart'); ?>
            </a>
        </div>

    <?php else: ?>
        <p class="empty"><?php echo $this->__('You have no items in your shopping cart.') ?></p>

    <?php endif ?>
</div>
