<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */
class Apptrian_FacebookPixel_Model_Config_Orderid
    extends Mage_Core_Model_Config_Data
{
    public function _beforeSave()
    {
        $result = $this->validate();
        
        if ($result !== true) {
            Mage::throwException(implode("\n", $result));
        }
        
        return parent::_beforeSave();
    }
    
    public function validate()
    {
        $errors    = array();
        $helper    = Mage::helper('apptrian_facebookpixel');
        $value     = $this->getValue();
        $validator = Zend_Validate::is(
            $value,
            'Regex',
            array('pattern' => '/^[\p{L}\p{N}_,;:!&#\+\*\$\?\|\'\.\-]*$/iu')
        );
        
        if (!$validator) {
            $errors[] = $helper->__(
                'The parameter name for Magento order ID is not valid.'
            );
        }
        
        if (empty($errors)) {
            return true;
        }
        
        return $errors;
    }
}
