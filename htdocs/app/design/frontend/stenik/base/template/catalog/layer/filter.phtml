<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Template for filter items block
 *
 * @see Mage_Catalog_Block_Layer_Filter
 */
?>


<?php if (count($items = $this->getItems())): ?>
    <?php
        $firstItem = reset($items);
        $count = count($items = $this->getItems());
        $counter = 0;
    ?>
    <ul class="sub-options">
        <?php foreach ($items as $_item): ?>
            <?php $counter++ ?>
            <li <?php if ($counter == $count): ?>class="last"<?php endif; ?>>
                <?php if ($_item->getCount() > 0): ?>
                    <a href="<?php echo $this->urlEscape($_item->getUrl()) ?>" title="<?php echo $this->stripTags($_item->getLabel()); ?>">
                        <?php echo $_item->getLabel() ?>
                    </a>
                <?php else: echo $_item->getLabel() ?>
                <?php endif; ?>
            </li>
        <?php endforeach ?>
    </ul>
<?php endif; ?>

