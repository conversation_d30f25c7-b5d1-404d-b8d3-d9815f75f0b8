<?php
class Bitware_SpeedengineDeferJS_Model_Observer
{

    public function bottomInlineJS(Varien_Event_Observer $observer)
    {
        Varien_Profiler::start('BitwareSpeedengineDeferJS');

       
        $helper = Mage::helper('bitware_speedengineDeferJS');
        if (!$helper->isEnabled()) {
            Varien_Profiler::stop('BitwareSpeedengineDeferJS');
            return $this;
        }
		$transport = $observer->getTransport();
		$block = $observer->getBlock();

        if (in_array($block->getNameInLayout(), $helper->getBlocksToSkip())) {
            $transport->setHtml($helper->addJsToExclude($transport->getHtml()));
        }

        if (Mage::app()->getRequest()->getModuleName() == 'pagecache') {
            $transport->setHtml($helper->moveJs($transport->getHtml()));
            Varien_Profiler::stop('BitwareSpeedengineDeferJS');
            return $this;
        }

        if (!is_null($block->getParentBlock())) {
            Varien_Profiler::stop('BitwareSpeedengineDeferJS');
            return $this;
        }

        $transport->setHtml($helper->moveJsToAfterBody($transport->getHtml()));
		Varien_Profiler::stop('BitwareSpeedengineDeferJS');
		return $this;
    }


}
