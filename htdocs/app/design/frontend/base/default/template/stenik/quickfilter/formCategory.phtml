<?php
/**
 * @package Stenik_QuickFilter
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php
    $attributes = $this->getAttributes();
    $currentCategory = Mage::registry('current_category');
?>

<?php if ($this->isPageOpenedFromQuickFilterSearch()): ?>
    <?php 
        $clearUrl = $this->getLayout()->getBlock('catalog.leftnav')->getClearUrl();
        $clearUrl = preg_replace('/fromssq=1(&amp;|&)?/', '', $clearUrl);
    ?>
    <div class="tyresSearchContent quickFilterWrapper">
        <span class="quickFilterSmallTitle"><?php echo $this->__('You аre reviewing filtered selection. To view all the products in this category, please use the filters on the left or click clear button');?></span>
        <a href="<?php echo $clearUrl ?>" class="clearFilters"><?php echo $this->__('Clear all filters');?></a>
    </div>

    <script>
        jQuery(function($) {
            $('html, body').animate({
                scrollTop: $('.mainContent').offset().top - 150
            }, 1000);
        });
    </script>
<?php else: ?>
    <?php if (count($attributes)): ?>
    <div class="tyresSearchContent quickFilterWrapper">
        <span class="quickFilterTitle"><?php echo $this->__('Search for tyres');?></span>
        <form action="<?php echo $this->getUrl('stenik_quickfilter/form/submit') ?>" class="tyresSearchBox" method="post">
            <?php if ($currentCategory && $currentCategory->getId()): ?>
                <div class="choseKindOfVehicle categories">
                    <span class="no-display quickFilterCategorySlider">
                        <input type="radio"
                               name="filter[category]"
                               value="<?php echo $currentCategory->getId() ?>"
                               id="quick_filter_category_<?php echo $currentCategory->getId() ?>"
                               data-filter-code="category"
                               class="hideInput"
                               checked="checked"
                        >
                    </span>
                </div>
            <?php endif ?>

            <?php if (count($attributes)): ?>
                <div class="filtersContent">
                    <?php
                        $selectAttributeCounter = 0;
                        $selectAttributeCount   = 0;
                        $attributeCounter       = 0;
                        $attributeCount         = count($attributes);

                        foreach ($attributes as $attribute) {
                            if (!in_array($attribute->getAttributeCode(), array('season'))) {
                                $selectAttributeCount++;
                            }
                        }

                        $colDivOpened = false;

                    ?>

                    <?php foreach ($attributes as $attribute): ?>
                        <?php
                            $attributeCounter++;

                            $attributeCode = $attribute->getAttributeCode();
                            $options = array();
                            try {
                                $source = $attribute->getSource();
                                if ($source) {
                                    $options = $source->getAllOptions(false);
                                }
                            } catch (Exception $e) {
                                Mage::logException($e);
                            }
                        ?>

                        <?php if (in_array($attributeCode, array('season'))): ?>
                            <div class="seasonChose">
                                <span class="seasonTitle"><?php echo $this->__('Season') ?></span>
                                <ul class="seasonChoseList fields">
                                    <?php foreach ($options as $option): ?>
                                        <?php if ($option['value'] == '') continue; ?>
                                        <li class="quickFilterItem">
                                            <label for="quick_filter_<?php echo $attributeCode ?>_<?php echo $option['value'] ?>" class="label-<?php echo $attributeCode ?> <?php echo $attributeCode ?>-<?php echo $option['value'] ?>" >
                                                <span class="icon"></span>
                                                <?php echo $this->escapeHtml($option['label']) ?>
                                            </label>
                                            <input type="radio" name="filter[<?php echo $attributeCode ?>]" id="quick_filter_<?php echo $attributeCode ?>_<?php echo $option['value'] ?>" value="<?php echo $option['value'] ?>" class="limitFilters hideInput" >
                                        </li>
                                    <?php endforeach ?>
                                </ul>
                            </div>
                        <?php else: ?>
                            <?php $selectAttributeCounter++ ?>
                            <?php if ($selectAttributeCounter == 1): ?>
                                <?php if ($colDivOpened): ?>
                                    </div>
                                <?php endif ?>

                                <?php $colDivOpened = true ?>
                                <div class="filtersCol col<?php if ($selectAttributeCounter == $selectAttributeCount-1): ?> noMarginR<?php endif ?>">
                                <span class="categoryQuickFitlerTitle"><?php echo $this->__('Sizes') ?></span>
                            <?php endif ?>

                            <?php // Select attributes as checkboxes selecting the first option ?>
                            <?php if (in_array($attributeCode, array('run_flat'))): ?>
                                <div class="runFlatCheckBox">
                                    <span class="inputWrapper">
                                        <?php if (count($options)): ?>
                                            <?php foreach ($options as $option): ?>
                                                <input
                                                    type="checkbox"
                                                    class="limitFilters"
                                                    id="quick_filter_<?php echo $attributeCode ?>"
                                                    name="filter[<?php echo $attributeCode ?>]"
                                                    value="<?php echo $option['value'] ?>"
                                                >
                                                <?php break; ?>
                                            <?php endforeach ?>
                                        <?php endif ?>
                                    </span>
                                    <label for="quick_filter_<?php echo $attributeCode ?>">
                                        <?php echo $this->escapeHtml($attribute->getStoreLabel()) ?>
                                    </label>
                                </div>
                            <?php else: ?>
                                <div class="filterBox filterBox-<?php echo $attributeCode ?> openonclick" data-select-id="quick_filter_<?php echo $attributeCode ?>">
                                    <a class="openFilters" href="javascript:;" data-select-selected></a>
                                    <ul class="subOptions" data-select-option-wrapper>
                                        <li data-select-template-option><a href="" data-select-template-option-label></a></li>
                                    </ul>
                                </div>

                                <div style="display:none;">
                                    <select name="filter[<?php echo $attributeCode ?>]" id="quick_filter_<?php echo $attributeCode ?>" data-filter-code="<?php echo $attributeCode ?>" class="filterToBeLimited limitFilters">
                                        <option value="" selected="selected"><?php echo $this->escapeHtml($attribute->getStoreLabel()) ?></option>
                                        <?php if (count($options)): ?>
                                            <?php foreach ($options as $option): ?>
                                                <option value="<?php echo $option['value'] ?>"><?php echo $this->escapeHtml($option['label']) ?></option>
                                            <?php endforeach ?>
                                        <?php endif ?>
                                    </select>
                                </div>
                            <?php endif ?>
                        <?php endif ?>
                    <?php endforeach ?>

                    <?php if ($colDivOpened): ?>
                            <a href="#compatibilityCalculator" class="calcLink"><strong>Калкулатор</strong> за съвместимост</a>
                            <button class="showResults"><?php echo $this->__('Search');?></button>
                        </div>
                    <?php else: ?>
                        <a href="#compatibilityCalculator" class="calcLink"><strong>Калкулатор</strong> за съвместимост</a>
                        <button class="showResults"><?php echo $this->__('Search');?></button>
                    <?php endif ?>
                </div>
            <?php else: ?>
                <a href="#compatibilityCalculator" class="calcLink"><strong>Калкулатор</strong> за съвместимост</a>
                <button class="showResults"><?php echo $this->__('Search');?></button>
            <?php endif ?>
        </form>
    </div>
    <?php endif ?>
<?php endif ?>

<script>
    jQuery(function() {
        jQuery('.tyresSearchContent.quickFilterWrapper .seasonChose li').click(function(e) {
            if (!jQuery(e.target).is('input')) {
                if (jQuery(this).find('input').is(':checked')) {
                    jQuery(this).find('input').prop('checked', false);
                    jQuery(this).find('input').attr('checked', false);
                    jQuery(this).find('input').change();
                    return false;
                }
            }
        });

        var onInputChange = function() {
            jQuery(this).parents('.fields').first().find('.quickFilterItem').removeClass('active');
            if (jQuery(this).is(':checked')) {
                jQuery(this).parents('.quickFilterItem').first().addClass('active');
            }
        };

        jQuery('.tyresSearchContent.quickFilterWrapper .seasonChose li input').change(onInputChange);
        jQuery('.tyresSearchContent .quickFilterCategorySlider').on('change', '.quickFilterItem input', onInputChange);


        var currentlyFiltering = false;

        jQuery('.tyresSearchContent .limitFilters').change(function() {
            if (currentlyFiltering) {
                return;
            }

            var filtersSerialized = jQuery('.tyresSearchContent .limitFilters').serializeArray();
            var filters = {};
            for (var i = filtersSerialized.length - 1; i >= 0; i--) {
                if (filtersSerialized[i].value) {
                    filters[filtersSerialized[i].name] = filtersSerialized[i].value;
                }
            }

            var filtersToFetch = [];
            jQuery('.tyresSearchContent .filterToBeLimited').each(function() {
                if (jQuery(this).data('filter-code')) {
                    filtersToFetch.push(jQuery(this).data('filter-code'));
                }
            });

            jQuery.ajax({
                url: <?php echo json_encode($this->getUrl('stenik_quickfilter/form/getFilters')) ?>,
                type: 'post',
                dataType: 'json',
                data: jQuery.extend(filters, {filters_to_fetch: filtersToFetch})
            }).done(function(response) {
                currentlyFiltering = true;
                jQuery('.tyresSearchContent .filterToBeLimited').each(function() {
                    if (jQuery(this).is('select')) {
                        var fireChangeEvent = false;
                        jQuery(this).find('option').each(function() {
                            if (jQuery(this).is(':disabled') && jQuery(this).data('disabled_by_filter')) {
                                jQuery(this).prop('disabled', false);
                                jQuery(this).data('disabled_by_filter', false);
                                fireChangeEvent = true;
                            }
                        });
                        if (fireChangeEvent) {
                            jQuery(this).change();
                        }
                    }
                })
                if (response) {
                    for (key in response) {
                        if (response[key].length) {
                            jQuery('.tyresSearchContent .filterToBeLimited[data-filter-code="' + key + '"]').each(function() {
                                var fireChangeEvent = false;
                                if (jQuery(this).is('select')) {
                                    jQuery(this).find('option').each(function() {
                                        var $option = jQuery(this);
                                        if (!$option.is('disabled') && $option.attr('value') != '') {
                                            if (response[key].indexOf($option.attr('value')) == -1) {
                                                $option.prop('disabled', true);
                                                $option.data('disabled_by_filter', true);
                                                fireChangeEvent = true;
                                            }
                                        }
                                    });
                                }

                                if (fireChangeEvent) {
                                    jQuery(this).change();
                                }
                            });
                        }
                    }
                }
                currentlyFiltering = false;
            });
        });

        Stenik.fancySelect('.tyresSearchContent.quickFilterWrapper .filterBox');
    });
</script>
