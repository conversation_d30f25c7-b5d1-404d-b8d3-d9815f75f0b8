<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */

class Apptrian_FacebookPixel_Model_Observer_Fpcpage
{
    /**
     * @var Apptrian_FacebookPixel_Helper_Data
     */
    public $helper;
    
    /**
     * Execute method.
     *
     * @param Varien_Event_Observer $observer
     * @return Apptrian_FacebookPixel_Model_Observer_Fpcpage
     */
    public function fireEventFpcPage(Varien_Event_Observer $observer)
    {
        $observer = null;
        $this->helper = Mage::helper('apptrian_facebookpixel');
        
        $firingMode = $this->helper->getFiringMode();
        
        if ($firingMode != 2) {
            return $this;
        }
        
        $customerId = Mage::registry('apptrian_facebookpixel_customer_id');
        
        $response = Mage::app()->getResponse();
        
        if (!$response) {
            return $this;
        }
        
        $content = $response->getBody();
        
        $categoryId = $this->getCategoryId($content);
        $productId  = $this->getProductId($content);
        $search     = $this->getSearch($content);
        $pageUrl    = $this->getPageUrl($content);
        
        if ($productId) {
            $data = $this->helper->getProductDataForServer($productId, $customerId);
        } elseif ($categoryId) {
            $data = $this->helper->getCategoryDataForServer($categoryId, $customerId);
        } elseif ($search) {
            $data = $this->helper->getSearchDataForServer($customerId);
        } elseif ($pageUrl) {
            $data = $this->helper->getDataForServerPageViewEvent($customerId);
        } else {
            $data = '';
        }
        
        if (empty($data)) {
            return $this;
        }
        
        $this->helper->fireServerEvent($data);
        
        return $this;
    }
    
    /**
     * Returns product ID from cached content.
     *
     * @param string $content
     * @return number|string
     */
    public function getProductId($content)
    {
        $productId = 0;
        
        $indexProduct  = preg_match('/apptrianFacebookPixelProductId=([0-9]+);/', $content, $matches);
        
        if ($indexProduct) {
            $productId = $matches[$indexProduct];
        }
        
        return $productId;
    }
    
    /**
     * Returns category ID from cached content.
     *
     * @param string $content
     * @return number|string
     */
    public function getCategoryId($content)
    {
        $categoryId = 0;
        
        $indexCategory = preg_match('/apptrianFacebookPixelCategoryId=([0-9]+);/', $content, $matches);
        if ($indexCategory) {
            $categoryId = $matches[$indexCategory];
        }
        
        return $categoryId;
    }
    
    /**
     * Returns search flag from cached content.
     *
     * @param string $content
     * @return number|string
     */
    public function getSearch($content)
    {
        $search = 0;
        
        if (strpos($content, 'apptrianFacebookPixelSearch=1;') !== false) {
            $search = 1;
        }
        
        return $search;
    }
    
    /**
     * Returns url from cached content.
     *
     * @param string $content
     * @return string
     */
    public function getPageUrl($content)
    {
        $url = 0;
        
        $indexUrl = preg_match('/apptrianFacebookPixelUrl="(.*)";/', $content, $matches);
        if ($indexUrl) {
            $url = $matches[$indexUrl];
        }
        
        return $url;
    }
}
