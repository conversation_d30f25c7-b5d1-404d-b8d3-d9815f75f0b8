<div class="page-title">
    <h1><?php echo $this->helper('stenik_gdprcompliance')->getCustomerAccountNavigationTitle(); ?></h1>
</div>
<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>

<div class="box-account">
    <div class="box-head">
        <h2><?php echo $this->helper('stenik_gdprcompliance')->getReviewTitle(); ?></h2>
    </div>
    <div class="col1-set">
        <?php echo $this->helper('stenik_gdprcompliance')->getReviewContent(); ?>
    </div>
</div>

<form action="<?php echo $this->getUrl('stenik_gdprcompliance/customer/reviewPost') ?>" method="post" id="form-validate" autocomplete="off">
    <div class="fieldset">
        <?php echo $this->getBlockHtml('formkey')?>
        <h2 class="legend"><?php echo $this->__('Information') ?></h2>
        <ul class="form-list">
            <?php echo $this->getLayout()->createBlock('stenik_gdprcompliance/widget_customer_register')->setFormData($this->getCustomer())->toHtml(); ?>
            <?php if ($this->isNewsletterEnabled()): ?>
            <?php $helper = $this->helper('stenik_gdprcompliance/agreement_newsletter'); ?>
            <li class="control">
                <div class="input-box">
                    <input type="checkbox" name="is_subscribed" title="<?php echo $this->htmlEscape(strip_tags($helper->getAgreement())); ?>" value="1" id="is_subscribed"<?php if ($this->getIsSubscribed() && $this->helper('customer')->isLoggedIn() && $this->helper('stenik_gdprcompliance')->hasConsent(Mage::getModel('customer/session')->getCustomer())): ?> checked="checked"<?php endif; ?> class="checkbox" />
                </div>
                <label for="is_subscribed"><?php echo $helper->getAgreement(); ?></label>
            </li>
            <?php endif ?>
        </ul>
    </div>
    <div class="buttons-set">
        <p class="required"><?php echo $this->__('* Required Fields') ?></p>
        <p class="back-link"><a href="<?php echo Mage::getUrl('stenik_gdprcompliance/customer/dashboard'); ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
        <button type="submit" title="<?php echo $this->__('Save') ?>" class="button"><span><span><?php echo $this->__('Save') ?></span></span></button>
    </div>
    <div class="buttons-set">
        <p class="required">
            <a href="<?php echo $this->getUrl('customer/account/logout') ?>" class="button-transparent"><?php echo $this->__('I\'ll think later') ?></a>
            <a href="<?php echo Mage::getUrl('stenik_gdprcompliance/request/forget'); ?>" class="link-remove"><?php echo $this->__('I wish my profile to be deleted') ?></a>
        </p>
    </div>
</form>
