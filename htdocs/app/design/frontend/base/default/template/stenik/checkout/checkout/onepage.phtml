<?php

?>
<div class="page-title test-class">
    <h1><?php echo $this->__('Checkout') ?></h1>
</div>

<script type="text/javascript" src="<?php echo $this->getJsUrl('varien/accordion.js') ?>"></script>
<script type="text/javascript" src="<?php echo $this->getSkinUrl('js/stenik/checkout/opcheckout.js') ?>"></script>
<script>
    var stenikOnepageCheckout = new window.Stenik.Checkout('<?php echo $this->getUrl('checkout/onepage/saveAll', array('form_key' => Mage::getSingleton('core/session')->getFormKey())) ?>');

    countryRegions = <?php echo $this->helper('directory')->getRegionJson() ?>
</script>

<div class="stenik-checkout">
    <div class="row stenik-checkout-steps-title">
        <?php $i=0; foreach($this->getSteps() as $_stepId => $_stepInfo): ?>
        <?php if (!$this->getChild($_stepId) || !$this->getChild($_stepId)->isShow()): continue; endif; $i++ ?>
            <div id="stenik-checkout-step-title-<?php echo $_stepId ?>" data-step-code="<?php echo $_stepId ?>" class="stenik-checkout-step-title step-tab-<?php echo $_stepId ?> section<?php if ($i == 1): ?> current<?php endif; ?>">
                <div class="checkout-progress-item text-outer">
                    <a href="javascript:;" class="checkout-progress-item text icon-red-<?php echo $i ?>">
                        <span class="icon"><?php echo $i ?>.</span>
                        <?php echo $_stepInfo['label'] ?>
                    </a>
                </div>
            </div>
        <?php endforeach ?>
    </div>
    <div class="steps-titles-line clear"><div></div></div>

    <?php if (!$this->isCustomerLoggedIn()): ?>
        <?php echo $this->getChildHtml('login'); ?>
    <?php endif ?>

    <div style="display: none">
        <?php // dummy elements ?>
        <div id="shipping-method-please-wait"></div>
        <div id="shipping-method-buttons-container"><button class="button"></button></div>
    </div>

    <div class="stenik-checkout-steps-content" id="stenik-checkout-steps-content">
        <?php $i=0; foreach($this->getSteps() as $_stepId => $_stepInfo) : ?>
            <?php if (!$this->getChild($_stepId) || !$this->getChild($_stepId)->isShow()): continue; endif; $i++ ?>
            <?php echo $this->getChildHtml($_stepId) ?>
        <?php endforeach ?>
    </div>
</div>

<script>
    (function($) {
        $('.stenik-checkout-step-title').click(function() {
            var $this = $(this);
            if ($this.hasClass('done') && $this.data('step-code')) {
                stenikOnepageCheckout.gotoStep($this.data('step-code'));
            }
        });
    })(jQuery);
</script>