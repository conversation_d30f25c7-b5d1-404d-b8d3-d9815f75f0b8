<div class="page-title">
    <h1><?php echo $this->helper('stenik_gdprcompliance')->getCustomerAccountNavigationTitle(); ?></h1>
</div>
<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>

<?php $helper = $this->helper('stenik_gdprcompliance/request_forget'); ?>

<form action="<?php echo $this->getUrl('stenik_gdprcompliance/request/forgetPost') ?>" method="post" id="form-validate" autocomplete="off">

    <?php echo $this->getBlockHtml('formkey')?>
    <h2 class="legend"><?php echo $this->__('Request to be forgotten') ?></h2>
	<?php echo $helper->getAdditionalInformation(); ?>

    <div class="clearH"></div>

    <div class="checkbox-content">
        <input type="checkbox" name="gdpr_forget" id="gdpr_forget" value="1" title="<?php echo $this->htmlEscape(strip_tags($helper->getAgreement())); ?>" class="checkbox required-entry" />
        <label for="gdpr_forget" class="long-label"><?php echo $helper->getAgreement(); ?></label>
    </div>

    <div class="clearH"></div>

    <div class="buttons-set">
        <button type="submit" title="<?php echo $this->__('Send') ?>" class="button"><?php echo $this->__('Send') ?></button>
        <div class="clearH2"></div>
        <p class="back-link"><a href="<?php echo Mage::getUrl('stenik_gdprcompliance/customer/dashboard'); ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
    </div>
</form>

<div class="clearH2"></div>

<script>
    var dataForm = new VarienForm('form-validate', true);
</script>