<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php echo $this->getChildHtml('items_before'); ?>
<div id="checkout-review-table-wrapper">
    <table class="data-table" id="checkout-review-table">
        <?php if ($this->helper('tax')->displayCartBothPrices()): $colspan = $rowspan = 2; else: $colspan = $rowspan = 1; endif; ?>
        <col />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
        <col width="1" />
        <col width="1" />
        <?php endif; ?>
        <thead>
            <tr>
                <th rowspan="<?php echo $rowspan ?>"><?php echo $this->__('Product Name') ?></th>
                <th colspan="<?php echo $colspan ?>" class="a-center"><?php echo $this->__('Price') ?></th>
                <th rowspan="<?php echo $rowspan ?>" class="a-center"><?php echo $this->__('Qty') ?></th>
                <th colspan="<?php echo $colspan ?>" class="a-center"><?php echo $this->__('Subtotal') ?></th>
            </tr>
            <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
                <tr>
                    <th class="a-right"><?php echo $this->helper('tax')->getIncExcTaxLabel(false) ?></th>
                    <th><?php echo $this->helper('tax')->getIncExcTaxLabel(true) ?></th>
                    <th class="a-right"><?php echo $this->helper('tax')->getIncExcTaxLabel(false) ?></th>
                    <th><?php echo $this->helper('tax')->getIncExcTaxLabel(true) ?></th>
                </tr>
            <?php endif; ?>
        </thead>
        <tbody>
        <?php foreach($this->getItems() as $_item): ?>
            <?php echo $this->getItemHtml($_item)?>
        <?php endforeach ?>
        </tbody>
    </table>
    <table class="data-table totals">
        <?php echo $this->getChildHtml('totals'); ?>
    </table>
</div>
<?php echo $this->getChildHtml('items_after'); ?>
<script type="text/javascript">
//<![CDATA[
    decorateTable('checkout-review-table');
    truncateOptions();
//]]>
</script>
<div id="checkout-review-submit">
    <?php echo $this->getChildHtml('agreements') ?>
    <div class="buttons-set" id="review-buttons-container" style="display: none;">
        <p class="f-left"><?php echo $this->__('Forgot an Item?') ?> <a href="<?php echo $this->getUrl('checkout/cart') ?>"><?php echo $this->__('Edit Your Cart') ?></a></p>
        <?php echo $this->getChildHtml('button') ?>
        <span class="please-wait" id="review-please-wait" style="display:none;">
            <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif') ?>" alt="<?php echo $this->__('Submitting order information...') ?>" title="<?php echo $this->__('Submitting order information...') ?>" class="v-middle" /> <?php echo $this->__('Submitting order information...') ?>
        </span>
    </div>
</div>
