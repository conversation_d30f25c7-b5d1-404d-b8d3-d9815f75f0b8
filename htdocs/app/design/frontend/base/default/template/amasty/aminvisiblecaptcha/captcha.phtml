<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2018 Amasty (https://www.amasty.com)
 * @package Amasty_InvisibleCaptcha
 */
?>
<?php if (Mage::getStoreConfig('aminvisiblecaptcha/general/enabled')): ?>
    <script type="text/javascript">
        var formsToProtectOnPage = [];
        var currentForm;
        var currentValidationForm;
        var checkedForms = [];
        var hasFormsChanged = false;

        const onSubmit = function (token) {
            if (currentValidationForm && currentValidationForm.validator
                && currentValidationForm.validator.validate()
            ) {
                currentForm.querySelector('[name="amasty_invisible_token"]').setAttribute('value', token);
                if (typeof(recaptchaObject) !== 'undefined') {
                    recaptchaObject.submit();
                } else {
                    currentForm.submit();
                }
            } else {
                grecaptcha.reset();
            }
        };

        function checkForms() {
            formsToProtect = <?php echo json_encode(Mage::getModel('aminvisiblecaptcha/captcha')->getSelectors()) ?>;
            formsToProtect.forEach(function (item) {
                var continueWorking = true;
                formsSearchedBySelector = $$(item);
                checkedForms.forEach(function(element) {
                    if (element[0] == formsSearchedBySelector[0]) {
                        continueWorking = false;
                        return;
                    }
                })
                if (formsSearchedBySelector.length != 0 && continueWorking) {
                    checkedForms.push(formsSearchedBySelector);
                    formsSearchedBySelector.forEach(function (formToProtect) {
                        formsToProtectOnPage.push(formToProtect);
                        hasFormsChanged = true;
                    });
                }
            });

            if (hasFormsChanged) {
                for (var index in formsToProtectOnPage) {
                    if (formsToProtectOnPage.hasOwnProperty(index)) {
                        var formToProtectOnPage = formsToProtectOnPage[index];
                        if ('form' !== formToProtectOnPage.tagName.toLowerCase()) {
                            formToProtectOnPage = formToProtectOnPage.getElementsByTagName('form');
                            if (0 < formToProtectOnPage.length) {
                                formToProtectOnPage = formToProtectOnPage[0];
                            } else {
                                continue;
                            }
                        }

                        var recaptchaBlock = document.createElement('div');
                        recaptchaBlock.className = 'amasty_recaptcha';
                        formToProtectOnPage.appendChild(recaptchaBlock);

                        var tokenInput = document.createElement('input');
                        tokenInput.type = 'hidden';
                        tokenInput.name = 'amasty_invisible_token';
                        tokenInput.value = '';
                        formToProtectOnPage.appendChild(tokenInput);

                        formToProtectOnPage.onsubmit = function submitProtectedForm(event) {
                            currentForm = event.target;
                            currentValidationForm = new VarienForm(currentForm.id, false);
                            recaptchaBlock = currentForm.querySelector(".amasty_recaptcha");
                            if ('' == recaptchaBlock.innerHTML) {
                                recaptcha = grecaptcha.render(recaptchaBlock, {
                                    'sitekey': '<?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/captcha_key'); ?>',
                                    'callback': onSubmit,
                                    'size': 'invisible',
                                    'theme': '<?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/badge_theme'); ?>',
                                    'badge': '<?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/badge_position'); ?>'
                                });
                            }
                            grecaptcha.reset(recaptcha);
                            grecaptcha.execute(recaptcha);
                            return false;
                        }
                    }
                }
            }
        }

        jQuery(document).ready(function() {
            var formsCount = 0;
            setInterval(function () {
                var formLength = $$('form').length;
                if (formsCount != formLength) {
                    formsCount = formLength;
                    checkForms();
                }
            }, 1000);
        });
    </script>
    <?php
        if ('inline' == Mage::getStoreConfig('aminvisiblecaptcha/general/badge_position')
            && Mage::getStoreConfig('aminvisiblecaptcha/general/badge_styles')
        ):
    ?>
        <style>
            .grecaptcha-badge {
                <?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/badge_styles') ?>
            }
        </style>
    <?php endif; ?>
    <?php $language = Mage::getStoreConfig('aminvisiblecaptcha/general/language'); ?>
    <script type="text/javascript">
		jQuery(document).ready(function(){
			checkForms();
			if (formsToProtectOnPage.length > 0) {
				jQuery(formsToProtectOnPage).each(function(protectedFormIndex, protectedFormSelector){
					jQuery(protectedFormSelector).find('input').on('focus', function(){
						loadRecaptchaScripts('<?= $language ?>');
					})
				})
			}
		});
    </script>
<?php endif; ?>
