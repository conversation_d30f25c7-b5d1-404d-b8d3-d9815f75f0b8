<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_AjaxAddToCart_Block_Result
 */
?>
<div class="stenik-ajaxAddToCart-result stenik-ajaxAddToCart-success">
    <?php if ($this->getMessage()): ?>
        <div class="success-message">
            <?php echo $this->getMessage() ?>
        </div>
    <?php endif ?>

    <?php echo $this->getItemHtml() ?>

    <div class="ajax-cart-total">
        <?php echo $this->getChildHtml('ajaxcartTotal') ?>
        <div class="clear"></div>
        <a href="<?php echo Mage::helper('checkout/cart')->getCartUrl() ?>" class="button checkout-button checkout-color">
            <?php echo $this->__('View shopping Cart'); ?>
        </a>
    </div>

    <?php echo $this->getChildHtml('crosssell') ?>

    <a href="javascript:;" class="close-popup closeAddToCartResult"></a>
</div>

<span class="close-popup-area closeAddToCartResult"></span>