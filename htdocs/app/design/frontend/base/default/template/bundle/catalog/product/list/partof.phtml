<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2018 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if($this->getItemCollection()->getSize()): ?>
<div class="box-collateral box-up-sell">
    <h2><?php echo $this->__('This product is also part of bundle(s)') ?></h2>
    <table class="products-grid" id="bundle-product-table">
    <?php // $this->setColumnCount(4); // uncomment this line if you want to have another number of columns ?>
    <?php $this->resetItemsIterator() ?>
    <?php for($_i=0;$_i<$this->getRowCount();$_i++): ?>
        <tr>
        <?php for($_j=0;$_j<$this->getColumnCount();$_j++): ?>
            <?php if($_bundle=$this->getIterableItem()): ?>
            <td>
                <a href="<?php echo $_bundle->getProductUrl() ?>" title="<?php echo $this->escapeHtml($_bundle->getName()) ?>" class="product-image"><img src="<?php echo $this->helper('catalog/image')->init($_bundle, 'small_image')->resize(125) ?>" width="125" height="125" alt="<?php echo $this->escapeHtml($_bundle->getName()) ?>" /></a>
                <h3 class="product-name"><a href="<?php echo $_bundle->getProductUrl() ?>"><?php echo $this->escapeHtml($_bundle->getName()) ?></a></h3>
                <?php echo $this->getPriceHtml($_bundle, true) ?>
                <?php echo $this->getReviewsSummaryHtml($_bundle) ?>
            </td>
            <?php else: ?>
            <td class="empty">&nbsp;</td>
            <?php endif; ?>
        <?php endfor; ?>
        </tr>
    <?php endfor; ?>
    </table>
    <script type="text/javascript">decorateTable('bundle-product-table')</script>
</div>
<?php endif ?>
