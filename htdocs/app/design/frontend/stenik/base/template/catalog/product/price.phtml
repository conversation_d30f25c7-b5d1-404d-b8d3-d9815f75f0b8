<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
/**
 * Template for displaying product price in different places (products grid, product view page etc)
 *
 * @see Mage_Catalog_Block_Product_Abstract
 */
?>
<?php
$_coreHelper = $this->helper('core');
$_weeeHelper = $this->helper('weee');
$_taxHelper = $this->helper('tax');
/* @var $_coreHelper Mage_Core_Helper_Data */
/* @var $_weeeHelper Mage_Weee_Helper_Data */
/* @var $_taxHelper Mage_Tax_Helper_Data */

$_product = $this->getProduct();
$_storeId = $_product->getStoreId();
$_store = $_product->getStore();
$_id = $_product->getId();
$_weeeSeparator = '';
$_simplePricesTax = ($_taxHelper->displayPriceIncludingTax() || $_taxHelper->displayBothPrices());
$_minimalPriceValue = $_product->getMinimalPrice();
$_minimalPriceValue = $_store->roundPrice($_store->convertPrice($_minimalPriceValue));
$_minimalPrice = $_taxHelper->getPrice($_product, $_minimalPriceValue, $_simplePricesTax);
$_convertedFinalPrice = $_store->roundPrice($_store->convertPrice($_product->getFinalPrice()));
$_specialPriceStoreLabel = $this->getProductAttribute('special_price')->getStoreLabel();
?>

<?php if (!$_product->isGrouped()): ?>
    <?php $_weeeTaxAmount = $_weeeHelper->getAmountForDisplay($_product); ?>
    <?php $_weeeTaxAttributes = $_weeeHelper->getProductWeeeAttributesForRenderer($_product, null, null, null, true); ?>
    <?php $_weeeTaxAmountInclTaxes = $_weeeTaxAmount; ?>
    <?php if ($_weeeHelper->isTaxable()): ?>
        <?php $_weeeTaxAmountInclTaxes = $_weeeHelper->getAmountInclTaxes($_weeeTaxAttributes); ?>
    <?php endif; ?>
    <?php $_weeeTaxAmount = $_store->roundPrice($_store->convertPrice($_weeeTaxAmount)); ?>
    <?php $_weeeTaxAmountInclTaxes = $_store->roundPrice($_store->convertPrice($_weeeTaxAmountInclTaxes)); ?>

    <div class="price-box">
    <?php $_convertedPrice = $_store->roundPrice($_store->convertPrice($_product->getPrice())); ?>
    <?php $_price = $_taxHelper->getPrice($_product, $_convertedPrice); ?>
    <?php $_regularPrice = $_taxHelper->getPrice($_product, $_convertedPrice, $_simplePricesTax); ?>
    <?php $_finalPrice = $_taxHelper->getPrice($_product, $_convertedFinalPrice) ?>
    <?php $_finalPriceInclTax = $_taxHelper->getPrice($_product, $_convertedFinalPrice, true) ?>
    <?php $_weeeDisplayType = $_weeeHelper->getPriceDisplayType(); ?>
    <?php if ($_finalPrice >= $_price): ?>
        <?php if ($_taxHelper->displayBothPrices()): ?>
            <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 0)): // including ?>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_price + $_weeeTaxAmount, false) ?>
                    </span>
                </span>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                    </span>
                </span>
            <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 1)): // incl. + weee ?>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_price + $_weeeTaxAmount, false) ?>
                    </span>
                </span>
                <span class="weee">(
                    <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                        <?php echo $_weeeSeparator; ?>
                        <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount(), true, true); ?>
                        <?php $_weeeSeparator = ' + '; ?>
                    <?php endforeach; ?>
                    )</span>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                    </span>
                </span>
            <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 4)): // incl. + weee ?>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_price + $_weeeTaxAmount, false) ?>
                    </span>
                </span>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                    </span>
                    <span class="weee">(
                        <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                            <?php echo $_weeeSeparator; ?>
                            <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount() + $_weeeTaxAttribute->getTaxAmount(), true, true); ?>
                            <?php $_weeeSeparator = ' + '; ?>
                        <?php endforeach; ?>
                        )</span>
                </span>
            <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 2)): // excl. + weee + final ?>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_price, false) ?>
                    </span>
                </span>
                <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                    <span class="weee">
                        <?php echo $_weeeTaxAttribute->getName(); ?>
                        : <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount() + $_weeeTaxAttribute->getTaxAmount(), true, true); ?>
                    </span>
                <?php endforeach; ?>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                    </span>
                </span>
            <?php else: ?>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php if ($_finalPrice == $_price): ?>
                            <?php echo $_coreHelper->formatPrice($_price, false) ?>
                        <?php else: ?>
                            <?php echo $_coreHelper->formatPrice($_finalPrice, false) ?>
                        <?php endif; ?>
                    </span>
                </span>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax, false) ?>
                    </span>
                </span>
            <?php endif; ?>
        <?php else: ?>
            <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, array(0, 1))): // including ?>
                <?php $weeeAmountToDisplay = $_taxHelper->displayPriceIncludingTax() ? $_weeeTaxAmountInclTaxes : $_weeeTaxAmount ?>
                <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                     <?php echo $_coreHelper->currency($_price + $weeeAmountToDisplay, true, true) ?>
                </span>

                <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 1)): // show description ?>
                    <span class="weee">(
                        <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                            <?php echo $_weeeSeparator; ?>
                            <?php echo $_weeeTaxAttribute->getName(); ?>
                            : <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount() + ($_taxHelper->displayPriceIncludingTax() ? $_weeeTaxAttribute->getTaxAmount() : 0), true, true); ?>
                            <?php $_weeeSeparator = ' + '; ?>
                        <?php endforeach; ?>
                        )</span>
                <?php endif; ?>
            <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 4)): // incl. + weee ?>
                <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_price + $_weeeTaxAmount, true) ?>
                </span>
                <span class="weee">(
                    <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                        <?php echo $_weeeSeparator; ?>
                        <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount() + $_weeeTaxAttribute->getTaxAmount(), true, true); ?>
                        <?php $_weeeSeparator = ' + '; ?>
                    <?php endforeach; ?>
                    )</span>
            <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 2)): // excl. + weee + final ?>
                <span class="regular-price"><?php echo $_coreHelper->formatPrice($_price, true) ?></span><br/>
                <?php $weeeAmountToDisplay = $_taxHelper->displayPriceIncludingTax() ? $_weeeTaxAmountInclTaxes : $_weeeTaxAmount ?>
                <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                    <span class="weee">
                       <?php echo $_weeeTaxAttribute->getName(); ?>
                        : <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount() + ($_taxHelper->displayPriceIncludingTax() ? $_weeeTaxAttribute->getTaxAmount() : 0), true, true); ?>
                    </span>
                <?php endforeach; ?>
                <span class="regular-price"
                      id="product-price-weee-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currency($_price + $weeeAmountToDisplay, true, true) ?>
                </span>
            <?php else: ?>
                <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php if ($_finalPrice == $_price): ?>
                        <?php echo $_coreHelper->formatPrice($_price, true) ?>
                    <?php else: ?>
                        <?php echo $_coreHelper->formatPrice($_finalPrice, true) ?>
                    <?php endif; ?>
                </span>
            <?php endif; ?>
        <?php endif; ?>
    <?php else: /* if ($_finalPrice == $_price): */ ?>
        <?php $_originalWeeeTaxAmount = $_weeeHelper->getOriginalAmount($_product); ?>
        <?php $_originalWeeeTaxAmount = $_store->roundPrice($_store->convertPrice($_originalWeeeTaxAmount)) ?>

        <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 0)): // including ?>
            <p class="old-price">
                <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
                <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_regularPrice + $_originalWeeeTaxAmount, false) ?>
                </span>
            </p>

            <?php if ($_taxHelper->displayBothPrices()): ?>
                <p class="special-price">
                    <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                    <span class="price-excluding-tax">
                        <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                        <span class="price"
                              id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                            <?php echo $_coreHelper->formatPrice($_finalPrice + $_weeeTaxAmount, false) ?>
                        </span>
                    </span>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                    </span>
                </span>
                </p>
            <?php else: ?>
                <p class="special-price">
                    <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                <span class="price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_finalPrice + $_weeeTaxAmountInclTaxes, false) ?>
                </span>
                </p>
            <?php endif; ?>

        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 1)): // incl. + weee ?>
            <p class="old-price">
                <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
                <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_regularPrice + $_originalWeeeTaxAmount, false) ?>
                </span>
            </p>

            <p class="special-price">
            <?php if ($_taxHelper->displayBothPrices()): ?>
                <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                      <?php echo $_coreHelper->formatPrice($_finalPrice + $_weeeTaxAmount, false) ?>
                    </span>
                </span>
                <span class="weee">(
                    <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                        <?php echo $_weeeSeparator; ?>
                        <?php echo $_weeeTaxAttribute->getName(); ?>
                        : <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount(), true, true); ?>
                        <?php $_weeeSeparator = ' + '; ?>
                    <?php endforeach; ?>
                    )</span>
                <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                </span>
                </span>
            <?php else: ?>
                <p class="special-price">
                    <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
                <span class="price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_finalPrice + $_weeeTaxAmountInclTaxes, false) ?>
                </span>
                </p>
                <span class="weee">(
                    <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                        <?php echo $_weeeSeparator; ?>
                        <?php echo $_weeeTaxAttribute->getName(); ?>
                        : <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount(), true, true); ?>
                        <?php $_weeeSeparator = ' + '; ?>
                    <?php endforeach; ?>
                    )</span>
            <?php endif; ?>
            </p>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 4)): // incl. + weee ?>
            <p class="old-price">
                <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
                <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_regularPrice + $_originalWeeeTaxAmount, false) ?>
                </span>
            </p>

            <p class="special-price">
                <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPrice + $_weeeTaxAmount, false) ?>
                    </span>
                </span>
            <span class="weee">(
                <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                    <?php echo $_weeeSeparator; ?>
                    <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount() + $_weeeTaxAttribute->getTaxAmount(), true, true); ?>
                    <?php $_weeeSeparator = ' + '; ?>
                <?php endforeach; ?>
                )</span>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                </span>
            </span>
            </p>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 2)): // excl. + weee + final ?>
            <p class="old-price">
                <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
                <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_regularPrice, false) ?>
                </span>
            </p>

            <p class="special-price">
                <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPrice, false) ?>
                    </span>
                </span>
                <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                    <span class="weee">
                        <?php echo $_weeeTaxAttribute->getName(); ?>
                        : <?php echo $_coreHelper->currency($_weeeTaxAttribute->getAmount(), true, true); ?>
                    </span>
                <?php endforeach; ?>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->formatPrice($_finalPriceInclTax + $_weeeTaxAmountInclTaxes, false) ?>
                    </span>
                </span>
            </p>
        <?php else: // excl. ?>
            <p class="old-price">
                <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
                <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_regularPrice, false) ?>
                </span>
            </p>

            <?php if ($_taxHelper->displayBothPrices()): ?>
                <p class="special-price">
                    <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                    <span class="price-excluding-tax">
                        <span class="price-label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                        <span class="price"
                              id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                            <?php echo $_coreHelper->formatPrice($_finalPrice, false) ?>
                        </span>
                    </span>
                    <span class="price-including-tax">
                        <span class="price-label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                        <span class="price"
                              id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                            <?php echo $_coreHelper->formatPrice($_finalPriceInclTax, false) ?>
                        </span>
                    </span>
                </p>
            <?php else: ?>
                <p class="special-price">
                    <span class="price-label"><?php echo $_specialPriceStoreLabel ?></span>
                <span class="price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_finalPrice, false) ?>
                </span>
                </p>
            <?php endif; ?>
        <?php endif; ?>

    <?php endif; /* if ($_finalPrice == $_price): */ ?>

    <?php if ($this->getDisplayMinimalPrice() && $_minimalPriceValue && $_minimalPriceValue < $_convertedFinalPrice): ?>

        <?php $_minimalPriceDisplayValue = $_minimalPrice; ?>
        <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, array(0, 1, 4))): ?>
            <?php $_minimalPriceDisplayValue = $_minimalPrice + $_weeeTaxAmount; ?>
        <?php endif; ?>

        <?php if ($this->getUseLinkForAsLowAs()): ?>
            <span onclick="window.location='<?php echo $_product->getProductUrl(); ?>';" class="minimal-price-link">
        <?php else: ?>
            <span class="minimal-price-link">
        <?php endif ?>
        <span class="price-label"><?php echo $this->__('As low as:') ?></span>
        <span class="price" id="product-minimal-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->formatPrice($_minimalPriceDisplayValue, false) ?>
            </span>
        <?php if ($this->getUseLinkForAsLowAs()): ?>
            </span>
        <?php else: ?>
            </span>
        <?php endif ?>
    <?php endif; /* if ($this->getDisplayMinimalPrice() && $_minimalPrice && $_minimalPrice < $_finalPrice): */ ?>
    </div>

<?php else: /* if (!$_product->isGrouped()): */ ?>
    <?php
    $showMinPrice = $this->getDisplayMinimalPrice();
    if ($showMinPrice && $_minimalPriceValue) {
        $_exclTax = $_taxHelper->getPrice($_product, $_minimalPriceValue);
        $_inclTax = $_taxHelper->getPrice($_product, $_minimalPriceValue, true);
        $price = $showMinPrice ? $_minimalPriceValue : 0;
    } else {
        $price = $_convertedFinalPrice;
        $_exclTax = $_taxHelper->getPrice($_product, $price);
        $_inclTax = $_taxHelper->getPrice($_product, $price, true);
    }
    ?>
    <?php if ($price): ?>
        <div class="price-box">
            <p<?php if ($showMinPrice): ?> class="minimal-price"<?php endif ?>>
                <?php if ($showMinPrice): ?>
                    <span class="price-label"><?php echo $this->__('Starting at:') ?></span>
                <?php endif ?>
                <?php if ($_taxHelper->displayBothPrices()): ?>
                    <span class="price-excluding-tax">
                        <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                        <span class="price"
                              id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                            <?php echo $_coreHelper->formatPrice($_exclTax, false) ?>
                        </span>
                    </span>
                    <span class="price-including-tax">
                        <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                        <span class="price"
                              id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                            <?php echo $_coreHelper->formatPrice($_inclTax, false) ?>
                        </span>
                    </span>
                <?php else: ?>
                    <?php
                    $_showPrice = $_inclTax;
                    if (!$_taxHelper->displayPriceIncludingTax()) {
                        $_showPrice = $_exclTax;
                    }
                    ?>
                    <span class="price" id="product-minimal-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->formatPrice($_showPrice, false) ?>
                </span>
                <?php endif; ?>
            </p>
        </div>
    <?php endif; /* if ($this->getDisplayMinimalPrice() && $_minimalPrice): */ ?>
<?php endif; /* if (!$_product->isGrouped()): */ ?>
