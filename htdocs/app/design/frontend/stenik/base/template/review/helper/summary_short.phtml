<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2010 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if ($this->getReviewsCount()): ?>
    <div class="rating-box-wrapper" itemprop="aggregateRating" itemscope itemtype="http://schema.org/AggregateRating">
        <meta itemprop="worstRating" content="1">
        <meta itemprop="ratingValue" content="<?php echo number_format(5*$this->getRatingSummary()/100, 1, '.', ''); ?>">
        <meta itemprop="ratingCount" content="<?php echo $this->getReviewsCount(); ?>">
        <meta itemprop="bestRating" content="5">
        <span class="rating-box"><span style="width:<?php echo $this->getRatingSummary() ?>%" class="rating"></span></span>
        <a href="javascript:;" class="link view-rating"><?php echo $this->__('View reviews');?></a>
    </div>
<?php elseif ($this->getDisplayIfEmpty()): ?>
    <div class="rating-box-wrapper">
        <span class="rating-box"><span style="width:<?php echo $this->getRatingSummary() ?>%" class="rating"></span></span>
        <a href="javascript:;" class="link view-rating"><?php echo $this->__('Leave reviews');?></a>
    </div>
<?php endif; ?>
