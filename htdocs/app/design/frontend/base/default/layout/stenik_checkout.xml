<?xml version="1.0"?>
<layout>
    <checkout_onepage_index_customer_must_be_logged>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <remove name="right" />

        <reference name="content">
            <block type="page/html_wrapper" name="stenik_checkout.login.checkout">
                <action method="setElementClass"><class>stenik-checkout must-login-checkout</class></action>

                <block type="checkout/onepage_login" name="checkout.onepage.login.form" template="stenik/checkout/checkout/onepage/loginForm.phtml"/>

                <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.review" template="stenik/checkout/checkout/onepage/section.phtml">
                    <action method="setSectionGroup"><section>review</section></action>
                    <action method="setSectionCode"><section>review</section></action>

                    <block type="checkout/onepage_review" name="checkout.onepage.review" template="stenik/checkout/checkout/onepage/review.phtml">
                        <block type="checkout/onepage_review_info" name="checkout.onepage.review.info" template="stenik/checkout/checkout/onepage/review/info.phtml">
                            <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
                            <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/onepage/review/item.phtml</template></action>
                            <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/onepage/review/item.phtml</template></action>
                            <block type="checkout/cart_totals" name="checkout.onepage.review.info.totals" as="totals" template="checkout/onepage/review/totals.phtml"/>
                            <block type="core/text_list" name="checkout.onepage.review.info.items.before" as="items_before" translate="label">
                                <label>Items Before</label>
                            </block>
                            <block type="core/text_list" name="checkout.onepage.review.info.items.after" as="items_after" translate="label">
                                <label>Items After</label>
                            </block>
                            <block type="checkout/agreements" name="checkout.onepage.agreements" as="agreements" template="stenik/checkout/checkout/onepage/agreements.phtml"/>
                        </block>
                    </block>
                </block>
            </block>
        </reference>
    </checkout_onepage_index_customer_must_be_logged>

    <checkout_onepage_index>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <remove name="right" />
        <remove name="checkout.onepage"></remove>

        <reference name="content">
            <block type="checkout/onepage_login" name="checkout.onepage.login.form" template="stenik/checkout/checkout/onepage/loginForm.phtml"/>

            <block type="stenik_checkout/onepage" name="checkout.onepage.mergedsteps" template="stenik/checkout/checkout/onepage.phtml">
                <block type="stenik_checkout/onepage_step" name="checkout.onepage.step.address_and_shipping" as="address_and_shipping" template="stenik/checkout/checkout/onepage/step/default.phtml">
                    <action method="setStepTitle" translate="title"><title>Delivery Information</title></action>

                    <block type="checkout/onepage_login" name="checkout.onepage.login.link" template="stenik/checkout/checkout/onepage/loginLink.phtml"/>

                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.billing" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>billing</section></action>
                        <action method="setSectionCode"><section>billing</section></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <shipping_method/>
                                <payment_method/>
                                <review/>
                            </sections_groups>
                        </action>

                        <block type="checkout/onepage_billing" name="checkout.onepage.billing" as="billing" template="stenik/checkout/checkout/onepage/billing.phtml">
                            <block type="core/text_list" name="checkout.onepage.billing.additional_address_fields" as="additional_address_fields"/>
                        </block>
                        <block type="core/template" name="checkout.onepage.billing.scripts" as="scripts" template="stenik/checkout/checkout/onepage/billing/scripts.phtml"/>
                    </block>

                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.shipping_method" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>shipping_method</section></action>
                        <action method="setSectionCode"><section>shipping_method</section></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <payment_method/>
                                <review/>
                            </sections_groups>
                        </action>

                        <block type="checkout/onepage_shipping_method" name="checkout.onepage.shipping_method" as="shipping_method" template="stenik/checkout/checkout/onepage/shipping_method.phtml">
                            <block type="stenik_checkout/onepage_shipping_method_available" name="checkout.onepage.shipping_method.available" as="available" template="checkout/onepage/shipping_method/available.phtml"/>
                            <block type="checkout/onepage_shipping_method_additional" name="checkout.onepage.shipping_method.additional" as="additional" template="checkout/onepage/shipping_method/additional.phtml"/>
                        </block>
                    </block>
                </block>

                <block type="stenik_checkout/onepage_step" name="checkout.onepage.payment_and_review" as="payment_and_review" template="stenik/checkout/checkout/onepage/step/default.phtml">
                    <action method="setStepTitle" translate="title"><title>Payment &amp; Review</title></action>
                    <action method="setIsLastStep"><val>1</val></action>

                    <!-- <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.coupon" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>coupon</section></action>
                        <action method="setSectionCode"><section>coupon</section></action>
                        <action method="setSectionHtmlClass"><class>coupon</class></action>
                        <action method="setSectionDisableAutosubmit"><flag>1</flag></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <coupon/>
                                <review/>
                                <comment_agreements/>
                            </sections_groups>
                        </action>
                        <block type="checkout/cart_coupon" name="checkout.onepage.coupon" template="stenik/checkout/checkout/onepage/coupon.phtml"/>
                    </block> -->

                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.payment_method" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>payment_method</section></action>
                        <action method="setSectionCode"><section>payment_method</section></action>
                        <action method="setUpdateSectionGroupsOnChange">
                            <sections_groups>
                                <review/>
                            </sections_groups>
                        </action>

                        <block type="checkout/onepage_payment_methods" name="checkout.payment.methods" as="methods" template="stenik/checkout/checkout/onepage/payment/methods.phtml">
                            <action method="setMethodFormTemplate"><method>purchaseorder</method><template>payment/form/purchaseorder.phtml</template></action>
                            <block type="core/template" name="checkout.onepage.payment.methods.scripts" as="scripts" />
                            <block type="core/template" name="checkout.onepage.payment.methods.additional" as="additional" />
                        </block>
                    </block>

                    <block type="stenik_checkout/onepage_section" name="checkout.onepage.section.review" template="stenik/checkout/checkout/onepage/section.phtml">
                        <action method="setSectionGroup"><section>review</section></action>
                        <action method="setSectionCode"><section>review</section></action>

                        <block type="checkout/onepage_review" name="checkout.onepage.review" template="stenik/checkout/checkout/onepage/review.phtml">
                            <block type="checkout/onepage_review_info" name="checkout.onepage.review.info" template="stenik/checkout/checkout/onepage/review/info.phtml">
                                <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
                                <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/onepage/review/item.phtml</template></action>
                                <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/onepage/review/item.phtml</template></action>
                                <block type="checkout/cart_totals" name="checkout.onepage.review.info.totals" as="totals" template="checkout/onepage/review/totals.phtml"/>
                                <block type="core/text_list" name="checkout.onepage.review.info.items.before" as="items_before" translate="label">
                                    <label>Items Before</label>
                                </block>
                                <block type="core/text_list" name="checkout.onepage.review.info.items.after" as="items_after" translate="label">
                                    <label>Items After</label>
                                </block>
                                <block type="checkout/agreements" name="checkout.onepage.agreements" as="agreements" template="stenik/checkout/checkout/onepage/agreements.phtml"/>
                            </block>
                        </block>
                    </block>
                </block>

                <block type="stenik_checkout/onepage_step" name="checkout.onepage.success" as="success" template="stenik/checkout/checkout/onepage/step/default.phtml">
                    <action method="setStepTitle" translate="title"><title>Successful Order!</title></action>
                </block>

                <action method="addStep"><child>address_and_shipping</child></action>
                <action method="addStep"><child>payment_and_review</child></action>
                <action method="addStep"><child>success</child></action>
            </block>
        </reference>

        <block type="core/text_list" name="checkout.onepage.billing.shipping_methods" as="shipping_methods">
            <block type="stenik_checkout/onepage_shipping_method_prechoose" name="checkout.onepage.shipping_method.prechoose" template="stenik/checkout/checkout/onepage/shipping_method/prechoose.phtml"/>
            <block type="checkout/onepage_shipping_method_additional" name="checkout.onepage.shipping_method.additional" as="additional" template="checkout/onepage/shipping_method/additional.phtml"/>
        </block>
    </checkout_onepage_index>

    <stenik_checkout_checkout_shipping_method_prechoose>
        <reference name="checkout.onepage.billing">
            <action method="setTemplate"><template>stenik/checkout/checkout/onepage/shipping_method/prechoose/billing.phtml</template></action>
            <action method="append"><block>checkout.onepage.billing.shipping_methods</block><alias>shipping_methods</alias></action>
        </reference>

        <reference name="checkout.onepage.shipping_method.available">
            <action method="setTemplate"><template>stenik/checkout/checkout/onepage/shipping_method/prechoose/available.phtml</template></action>
        </reference>
    </stenik_checkout_checkout_shipping_method_prechoose>

    <customer_address_form>
        <block type="stenik_checkout/onepage_shipping_method_prechoose" name="customer.address.edit.shipping_methods" as="shipping_methods" template="stenik/checkout/checkout/onepage/shipping_method/prechoose.phtml"/>
        <block type="core/text_list" name="customer.address.edit.custom_address_fields" as="custom_address_fields"/>
    </customer_address_form>

    <stenik_checkout_customer_address_prechoose>
        <reference name="customer_address_edit">
            <action method="setTemplate"><template>stenik/checkout/customer/shipping_method/prechoose/address/edit.phtml</template></action>
            <action method="append"><block>customer.address.edit.shipping_methods</block><alias>shipping_methods</alias></action>
            <action method="append"><block>customer.address.edit.custom_address_fields</block><alias>custom_address_fields</alias></action>
        </reference>
    </stenik_checkout_customer_address_prechoose>

    <stenik_checkout_partial_billing_address_update>
        <reference name="checkout.onepage.section.billing">
            <action method="addUpdateSectionGroupsOnFilledFields">
                <fields>
                    <address_select>billing-address-select</address_select>
                    <billing_country_id>billing:country_id</billing_country_id>
                    <billing_region_id>billing:region_id</billing_region_id>
                    <billing_region>billing:region</billing_region>
                    <billing_city>billing:city</billing_city>
                    <billing_postcode>billing:postcode</billing_postcode>
                    <!-- <billing_street_one>billing:street1</billing_street_one> -->
                </fields>
            </action>
        </reference>
    </stenik_checkout_partial_billing_address_update>

    <paypal_express_review>
        <reference name="paypal.express.review">
            <action method="setTemplate"><template>stenik/checkout/paypal/express/review.phtml</template></action>
            <action method="setUseAjax"><flag>0</flag></action>
        </reference>

        <reference name="express.review.billing">
            <action method="setTemplate"><template>stenik/checkout/paypal/express/review/address.phtml</template></action>
        </reference>

        <reference name="express.review.shipping">
            <action method="setTemplate"><template>stenik/checkout/paypal/express/review/address.phtml</template></action>
        </reference>

        <reference name="paypal.express.review.details.agreements">
            <action method="setTemplate"><template>stenik/checkout/checkout/onepage/agreements.phtml</template></action>
        </reference>
    </paypal_express_review>
</layout>