
<?php $shopCollection = $this->getShopCollection() ?>
<?php $shopCount      = $shopCollection->getSize() ?>



<div class="footerShops">
	<?php echo $this->getChildHtml('footerShopsText') ?>
    <div class="fakeSelect">
        <a href="javascript:;" class="openSelect"><?php echo $this->__('Choose store') ?></a>
        <ul>
            <?php foreach ($shopCollection as $shop): ?>
            	<li>
            		<a data-shop="<?php echo $shop->getId(); ?>" href="<?php echo Mage::helper('pfg_shop')->getShopListUrl() . '#shop' . $shop->getId() ?>"><?php echo $this->escapeHtml(Mage::helper('pfg_shop/shop')->getNamePrefix() . $shop->getName()) ?></a>
            	</li>
            <?php endforeach; ?>
        </ul>
    </div>                                
    <button class="submitButton"><?php echo $this->__('Show');?></button>
</div>