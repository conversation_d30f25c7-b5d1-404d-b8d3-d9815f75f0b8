<?php
/**
 * @package Stenik_AdminPromoCatalogApply
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_AdminPromoCatalogApply_Model_Cronjob extends Mage_Core_Model_Abstract
{

    /**
     * [applyAllRules description]
     * @param  [type] $observer [description]
     * @return [type]           [description]
     */
    public function applyAllRules($observer)
    {
        Mage::getModel('catalogrule/rule')->applyAll();
        Mage::getModel('catalogrule/flag')->loadSelf()
            ->setState(0)
            ->save();
        return $this;
    }
}
