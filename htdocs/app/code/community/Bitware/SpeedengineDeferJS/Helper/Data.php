<?php
//here code for defining helper methods - to get values from sys->configuration
  
class Bitware_SpeedengineDeferJS_Helper_Data extends Mage_Core_Helper_Abstract
{
	
    const REGEX_JS = '#(\s*<!--(\[if[^\n]*>)?\s*(<script.*</script>)+\s*(<!\[endif\])?-->)|(\s*<script.*</script>)#isU';
    const REGEX_DOCUMENT_END  = '#</body>\s*</html>#isU';
	const XML_CONFIGURATION_JS_ENABLED = 'bitware/bitware_group/bitware_enabled';
    const XML_CONFIGURATION_JS_EXCLUDED_BLOCKS = 'bitware/bitware_group/bitware_excluded_blocks';
    const XML_CONFIGURATION_JS_EXCLUDED_LINKS = 'bitware/bitware_group/bitware_excluded_links';
	
    const EXCLUDE_FLAG = 'data-footer-js-skip="true"';
    const EXCLUDE_FLAG_PATTERN = 'data-footer-js-skip';

    
    protected $_blocksToExclude;
	protected $skippedFilesRegex;

    public function isEnabled($store = null)
    {
        return Mage::getStoreConfigFlag(self::XML_CONFIGURATION_JS_ENABLED, $store);
    }

    public function moveJs($html)
    {
        $patterns = array(
            'js' => self::REGEX_JS
        );

        foreach($patterns as $pattern) {
            $matches = array();

            $success = preg_match_all($pattern, $html, $matches);
            if ($success) {
                foreach ($matches[0] as $key => $js) {
                    if (strpos($js, self::EXCLUDE_FLAG_PATTERN) !== false) {
                        unset($matches[0][$key]);
                    }
                }
                $html = str_replace($matches[0], '', $html);
            }
        }

        return $html;
    }

    public function moveJsToAfterBody($html)
    {
        $patterns = array(
            'js'             => self::REGEX_JS,
            'document_end'   => self::REGEX_DOCUMENT_END
        );

        foreach($patterns as $pattern) {
            $matches = array();

            $success = preg_match_all($pattern, $html, $matches);
            if ($success) {
                // Strip excluded files
                if ($this->getSkippedFilesRegex() !== false) {
                    $matches[0] = preg_grep($this->getSkippedFilesRegex(), $matches[0], PREG_GREP_INVERT);
                }
                foreach ($matches[0] as $key => $js) {
                    if (strpos($js, self::EXCLUDE_FLAG_PATTERN) !== false) {
                        unset($matches[0][$key]);
                    }
                }
                $text = implode('', $matches[0]);
                $html = str_replace($matches[0], '', $html);
                $html = $html . $text;
            }
        }

        return $html;
    }

    public function getSkippedFilesRegex()
    {
        if ($this->skippedFilesRegex === null) {
            $skipConfig = trim(Mage::getStoreConfig(self::XML_CONFIGURATION_JS_EXCLUDED_LINKS));
            if ($skipConfig !== '') {
                $skippedFiles = preg_replace('/\s*,\s*/', '|', $skipConfig);
                $this->skippedFilesRegex = sprintf("@src=.*?(%s)@", $skippedFiles);
            } else {
                $this->skippedFilesRegex = false;
            }
        }
        return $this->skippedFilesRegex;
    }

    
    public function addJsToExclude($html)
    {
        return str_replace('<script', '<script ' . self::EXCLUDE_FLAG, $html);
    }

   // Get list of block names (in layout) to exclude their JS from moving to footer
    
    public function getBlocksToSkip()
    {
        if (is_null($this->_blocksToExclude)) 
		{
			
            $string = Mage::getStoreConfig(self::XML_CONFIGURATION_JS_EXCLUDED_BLOCKS);
            $exludedBlocks = explode(',', $string);
            foreach ($exludedBlocks as $key => $blockName) 
			{
                $exludedBlocks[$key] = trim($blockName); 
				
				 if ((strpos($exludedBlocks[$key], "\n") !== false)) 
				{
                     Mage::log('Missing comma in setting "' . self::XML_CONFIGURATION_JS_EXCLUDED_BLOCKS . '"', Zend_Log::ERR, null, true); 
					
					//echo "new line error";
                }
				if ((strpos($exludedBlocks[$key], ' ') !== false)) 
				{
                    Mage::log('Missing comma in setting "' . self::XML_CONFIGURATION_JS_EXCLUDED_BLOCKS . '"', Zend_Log::ERR, null, true); 
					
					//echo "space error";
                }
				
            }
            $this->_blocksToExclude = array_filter($exludedBlocks);
        }
        return $this->_blocksToExclude;
    }
}
