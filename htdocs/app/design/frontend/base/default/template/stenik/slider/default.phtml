
<?php $slides = $this->getSlides(); ?>
<?php if (count($slides)): ?>

    <?php $sliderId = 'slider' . (uniqid(true)) ?>
    <div id="<?php echo $sliderId ?>" class="touchcarousel <?php echo $this->getAdditionalClasses() ?>">
        <ul class="touchcarousel-container">
            <?php foreach ($slides as $slide): ?>
                <?php $imageSize = getimagesize(Mage::getBaseDir('media') . DS . $slide->getImage()); ?>
                <?php if ($slide->getLink()): ?>
                    <li class="touchcarousel-item">
                        <a href="<?php echo $slide->getLink() ?>" <?php if ($slide->getLinkOpenInNewWindow()): ?> data-rel="blank"<?php endif; ?>>
                            <img <?= $imageSize ? 'height="' . $imageSize[1] . '"' : ''; ?> src="<?php echo $slide->getImageUrl() ?>" alt="global brands">
                            <?php if ($slide->getFrontendTitle() || $slide->getFrontendSubtitle()): ?>
                                <span class="fade">
                                    <span class="title"><?php echo $slide->getFrontendTitle() ?></span>
                                    <span class="subTitle"><?php echo $slide->getFrontendSubtitle() ?></span>
                                </span>
                            <?php endif ?>
                        </a>
                    </li>
                <?php elseif ($slide->getLinkLeft() || $slide->getLinkRight()): ?>
                    <li class="touchcarousel-item">
                        <span class="leftRightLinks">
                            <?php if ($slide->getLinkLeft()): ?>
                                <a href="<?php echo $slide->getLinkLeft() ?>" <?php if ($slide->getLinkOpenInNewWindow()): ?> data-rel="blank"<?php endif; ?> class="leftLink"></a>
                            <?php endif ?>
                            <?php if ($slide->getLinkRight()): ?>
                                <a href="<?php echo $slide->getLinkRight() ?>" <?php if ($slide->getLinkOpenInNewWindow()): ?> data-rel="blank"<?php endif; ?> class="rightLink"></a>
                            <?php endif ?>
                            <img <?= $imageSize ? 'height="' . $imageSize[1] . '"' : ''; ?> src="<?php echo $slide->getImageUrl() ?>" alt="global brands">
                            <?php if ($slide->getFrontendTitle() || $slide->getFrontendSubtitle()): ?>
                                <span class="fade">
                                    <span class="title"><?php echo $slide->getFrontendTitle() ?></span>
                                    <span class="subTitle"><?php echo $slide->getFrontendSubtitle() ?></span>
                                </span>
                            <?php endif ?>
                        </span>
                    </li>
                <?php else: ?>
                    <li class="touchcarousel-item">
                        <img <?= $imageSize ? 'height="' . $imageSize[1] . '"' : ''; ?> src="<?php echo $slide->getImageUrl() ?>" alt="global brands">
                        <?php if ($slide->getFrontendTitle() || $slide->getFrontendSubtitle()): ?>
                            <span class="fade">
                                <span class="title"><?php echo $slide->getFrontendTitle() ?></span>
                                <span class="subTitle"><?php echo $slide->getFrontendSubtitle() ?></span>
                            </span>
                        <?php endif ?>
                    </li>
                <?php endif; ?>
            <?php endforeach ?>
        </ul>
    </div>

   <script>
        jQuery(function($){
            $("#<?php echo $sliderId ?>").touchCarousel({
                itemsPerPage: 1,
                itemsPerMove: 1,
                scrollbar: false,
                scrollbarAutoHide: true,
                pagingNav: <?php if(count($slides) > 1) : ?>true<?php else: ?>false<?php endif; ?>,
                snapToItems: true,
                scrollToLast: false,
                useWebkit3d: true,
                loopItems: true,
                autoplay: true,
                autoplayDelay: 5000,
                dragUsingMouse: <?php if(count($slides) > 1) : ?>true<?php else: ?>false<?php endif; ?>,
                directionNav: <?php if(count($slides) > 1) : ?>true<?php else: ?>false<?php endif; ?>
            });

        });
    </script>
<?php endif ?>
