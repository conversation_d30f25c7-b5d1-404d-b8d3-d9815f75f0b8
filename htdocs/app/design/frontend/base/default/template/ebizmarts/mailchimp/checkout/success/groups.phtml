<?php
$block = $this;
$interest = $block->getInterest();
$helper = $block->getMailChimpHelper();
?>
<p>
    <?php if($helper->isInterestGroupEnabled($this->_getStoreId())): ?>
    <?php echo $this->quoteEscape($block->getMessageBefore()) ?>
<form class="form" action="<?php echo $this->quoteEscape($block->getFormUrl()) ?>" method="get" id="mailchimp-groups">
    <fieldset class="fieldset">
        <?php foreach($interest as $i): ?>
            <div class="field">
                <label class="label" for="<?php echo $this->quoteEscape($i['interest']['id']) ?>">
                    <span><?php echo $this->quoteEscape($i['interest']['title']) ?></span>
                </label>
                <div class="control">
                    <div class="fields">
                        <?php echo $this->getLayout()->createBlock(
                            'mailchimp/group_type',
                            'mailchimp.group.type',
                            array('interests' => $i)
                        )->toHtml(); ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit"
                    class="action primary button"
                    title="<?php echo $block->escapeHtml(__('Subscribe to more detailed newsletters')) ?>"
                    style="margin-top: 10px; margin-bottom: 10px;"
            >
                <span><?php echo $this->escapeHtml(__('Subscribe to more detailed newsletters')) ?></span>
            </button>
        </div>
    </div>
</form>
<?php echo $this->escapeHtml($block->getMessageAfter()) ?>
    <?php endif; ?>

</p>
