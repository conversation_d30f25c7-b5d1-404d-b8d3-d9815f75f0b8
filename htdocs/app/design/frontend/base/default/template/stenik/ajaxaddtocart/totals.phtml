
<?php $itemCount = $this->getSummaryCount(); ?>
<span class="cart-summary-count">
    <?php if($itemCount == 1): ?>
        <?php echo $this->__('You have <strong>%s product</strong> in your Cart', $itemCount);?>
    <?php else: ?>
        <?php echo $this->__('You have <strong>%s products</strong> in your Cart', $itemCount);?>
    <?php endif ?>
</span>
<span class="itermediate-price">
    <span class="price-label"><?php echo $this->__('Intermediate price');?></span>: <?php echo Mage::helper('checkout')->formatPrice($this->getSubtotal(false)) ?>
</span>


