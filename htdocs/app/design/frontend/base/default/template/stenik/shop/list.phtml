<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>
<?php $shopCollection = $this->getShopCollection(); ?>
<?php $shopCount      = $shopCollection->getSize() ?>
<div class = "storeListingWrapper">
    <?php if ($shopCount): ?>
        <h1><?php echo $this->__('Shops') ?></h1>

        <?php echo $this->getToolbarHtml(); ?>

        <section class="storeListing">
            <?php foreach ($shopCollection as $shop): ?>
                <div class="storeBox" data-shop="<?php echo $shop->getId(); ?>">
                    <img src="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'logo_image')->resize(130, 130); ?>" alt="<?php echo $this->escapeHtml(Mage::helper('stenik_shop/shop')->getNamePrefix() . $shop->getName()) ?>">
                    <a class="storeHoverInfo" href="<?php echo $shop->getUrl() ?>">
                        <div class="titleStore"><?php echo $this->escapeHtml(Mage::helper('stenik_shop/shop')->getNamePrefix() . $shop->getName()) ?></div>
                        <span class="seeMore"><?php echo $this->__('See more') ?></span>
                        <span class="bottomLine"></span>
                    </a>
                </div>
            <?php endforeach ?>
        </section>
        <div class="noShopsFound" style="display:none"><?php echo $this->__('No shops found.') ?></div>
    <?php else: ?>
        <div class="noShopsFound"><?php echo $this->__('No shops found.') ?></div>
    <?php endif; ?>
</div>


<?php if ($shopCount): ?>
    <script>
        (function(){
            var container = jQuery('.storeListingWrapper');
            var storeListingWidth = container.find('.storeListing').first().width();
            var storeBoxOuterWidth = container.find('.storeListing .storeBox').first().outerWidth();
            var maxStoreBoxesPerRow = Math.floor(storeListingWidth/storeBoxOuterWidth);

            var currentFilters = {};

            function storeBoxShowFromList(jqElements, filterCode) {

                var elementsToHide = container.find('.storeListing .storeBox:visible').not(jqElements);
                if (elementsToHide.length) {
                    jQuery.when(
                        elementsToHide.css({opacity: 0, visibility: 'hidden'}).delay(350-10).hide(0)
                    ).then(function(){
                        jQuery.when(
                            jqElements.show(0).css({opacity: 1, visibility: 'visible'}).delay(10)
                        ).then(function() {
                            storeBoxesRecalcBorders();
                            if (jqElements.length)
                                container.find('.noShopsFound').hide();
                            else container.find('.noShopsFound').show();
                        });
                    });
                } else {
                    jQuery.when(
                        jqElements.show(0).css({opacity: 1, visibility: 'visible'})
                    ).then(function(){
                        storeBoxesRecalcBorders();
                        if (jqElements.length)
                            container.find('.noShopsFound').hide();
                        else container.find('.noShopsFound').show();
                    });
                }

            }

            function storeBoxesRecalcBorders() {
                var elements = container.find('.storeListing .storeBox:visible');
                var count = elements.length;
                var counter = 0;
                elements.removeClass('noBorderR')
                        .removeClass('noBorderB')
                        .each(function(){
                            counter++;
                            if (counter%maxStoreBoxesPerRow == 0)
                                jQuery(this).addClass('noBorderR');

                            var modulo = count%maxStoreBoxesPerRow;
                            if (counter > count - ((modulo == 0 || modulo == count) ? maxStoreBoxesPerRow : modulo))
                                jQuery(this).addClass('noBorderB');
                        });
            }

            jQuery(function(){
                storeBoxesRecalcBorders();
                container.find('.toolbar a.filter').click(function(){
                    if (jQuery(this).hasClass('disabled'))
                        return false;

                    filterCode = jQuery(this).data('shop-filter-code');

                    container.find('a.filter').each(function(){
                        if (jQuery(this).data('shop-filter-code') == filterCode)
                            jQuery(this).removeClass('selected').removeClass('active');
                    });

                    jQuery(this).addClass('active').addClass('selected');

                    if (jQuery(this).data('shops')) {
                        currentFilters[filterCode] = jQuery(this).data('shops').toString().split(',');
                        var jqSelectors            = [];
                        var shops                  = false;

                        for (var code in currentFilters) {
                            if (currentFilters[code].indexOf('all') != -1) {
                                continue;
                            }

                            if (currentFilters[code].length == 0) {
                                shops = [];
                                break;
                            }

                            if (shops === false)
                                shops = currentFilters[code];
                            else {
                                // intersect shops with currentFilters[code]
                                shops = shops.filter(function(shop){
                                    return currentFilters[code].indexOf(shop) != -1;
                                });
                            }
                        }

                        if (shops === false) {
                            jqSelectors.push('.storeListing .storeBox');
                        } else {
                            for (var i = shops.length - 1; i >= 0; i--) {
                                jqSelectors.push('.storeListing .storeBox[data-shop="' + shops[i] + '"]');
                            };
                        }
                        storeBoxShowFromList(container.find(jqSelectors.join(',')), filterCode);
                    }
                });
            });
        })();
    </script>
<?php endif; ?>