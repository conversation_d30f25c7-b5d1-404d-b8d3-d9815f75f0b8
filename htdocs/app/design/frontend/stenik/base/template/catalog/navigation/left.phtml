<?php
/**
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Catalog_Block_Navigation
 */
?>

<?php
    if (!Mage::registry('current_category')) {
        return;
    }

    $currentCategory = Mage::registry('current_category');
    $categories = $this->getCurrentChildCategories();
    $count = count($categories);
    if (!$count) {
        $parentCategory = $currentCategory->getParentCategory();
        $categories = $parentCategory->getChildrenCategories();

        $productCollection = Mage::getResourceModel('catalog/product_collection');
        Mage::getSingleton('catalog/layer')->prepareProductCollection($productCollection);
        $productCollection->addCountToCategories($categories);

        $count = count($categories);
    }
?>

<?php if($count > 0): ?>
    <div class="drop-down categories">
        <a class="open-item"><?php echo $this->__('Categories');?></a>
        <ul class="sub-options">
            <?php
                $count = count($categories);
                $counter = 0;
            ?>
            <?php foreach ($categories as $category): ?>
            <?php if ($category->getId() == Mage::getStoreConfig('archive_id/settings/archive_cat_id')) {
                continue;
                } ?>
                <?php $counter++ ?>
                <li class="<?php if ($category->getId() == $currentCategory->getId()): ?>active<?php endif; ?><?php if ($counter == $count): ?> last<?php endif; ?>" >
                    <a href="<?php echo $this->getCategoryUrl($category); ?>" class="<?php if ($category->getId() == $currentCategory->getId()): ?>selected<?php endif; ?>">
                        <?php echo $this->escapeHtml($category->getName()); ?>
                    </a>
                </li>
            <?php endforeach ?>
        </ul>
    </div>
<?php endif ?>