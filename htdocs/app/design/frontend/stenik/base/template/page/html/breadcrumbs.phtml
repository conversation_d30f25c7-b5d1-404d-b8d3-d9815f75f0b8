<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php if($crumbs && is_array($crumbs)): ?>
    <div class="row">
        <div class="col-xs-12">
            <nav class="breadcrumbs">
                <ul itemscope itemtype="http://schema.org/BreadcrumbList">
                    <?php $i = 0; ?>
                    <?php foreach($crumbs as $_crumbName=>$_crumbInfo): ?>
                        <?php $i++; ?>
                        <?php
                            $_crumbInfo['label'] = mb_strtolower($_crumbInfo['label'], 'UTF-8');
                            $_crumbInfo['label'] = Mage::helper('stenik_base/text')->mb_ucfirst($_crumbInfo['label'], 'UTF-8');
                        ?>
                        <?php if ($_crumbName == 'home' || $_crumbName == 'Home'): ?>
                            <li class="home" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                                <a href="<?php echo $this->getUrl('') ?>" itemprop="item">
                                    <span itemprop="name"><?php echo $this->__('Home');?></span>
                                    <meta itemprop="position" content="<?php echo $i; ?>"/>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="<?php echo $_crumbName ?>"
                                itemprop="itemListElement"
                                itemtype="http://schema.org/ListItem"
                                itemscope
                            >
                                <?php if($_crumbInfo['link']): ?>
                                    <a
                                        href="<?php echo $_crumbInfo['link'] ?>"
                                        title="<?php echo $this->htmlEscape($_crumbInfo['title']) ?>"
                                        itemprop="item"
                                    >
                                        <span itemprop="name"><?php echo $this->htmlEscape($_crumbInfo['label']) ?></span>
                                        <meta itemprop="position" content="<?php echo $i; ?>" />
                                    </a>
                                <?php elseif($_crumbInfo['last']): ?>
                                    <span class="current" itemprop="item">
                                        <span itemprop="name"><?php echo $this->htmlEscape($_crumbInfo['label']) ?></span>
                                        <meta itemprop="position" content="<?php echo $i; ?>" />
                                    </span>
                                <?php else: ?>
                                    <span><?php echo $this->htmlEscape($_crumbInfo['label']) ?></span>
                                <?php endif; ?>
                            </li>
                        <?php endif ?>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </div>
    </div>
<?php endif; ?>