<?php
/**
 * @package     Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see         Mage_Checkout_Block_Cart
 */
?>
<div class="discount">
    <span class="title"><?php echo $this->__('You have a promo code? Enter it here');?>:</span>
    <form id="discount-coupon-form" action="<?php echo $this->getUrl('checkout/cart/couponPost') ?>" method="post" class="discount-form">
        <input type="hidden" name="remove" id="remove-coupone" value="0" />
        <input type="text" id="coupon_code" name="coupon_code" class="input-text" value="<?php echo $this->escapeHtml($this->getCouponCode()) ?>">
        <div class="validation-advice" id="advice-required-entry-coupon_code" style="display:none"><?php echo Mage::helper('core')->__('This is a required field.');?></div>
        <?php if(strlen($this->getCouponCode())): ?>
            <button class="discount-button" onclick="discountForm.submit(true)">
                <svg aria-hidden="true" class="icon-svg close">
                    <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#close' ?>"></use>
                </svg>
            </button>
        <?php else: ?>
            <button class="discount-button" onclick="discountForm.submit(false)">
                <svg aria-hidden="true" class="icon-svg check">
                    <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#check' ?>"></use>
                </svg>
            </button>
        <?php endif ?>
    </form>
</div>
<script>
    var discountForm = new VarienForm('discount-coupon-form');
    discountForm.submit = function (isRemove) {
        if (isRemove) {
            $('coupon_code').removeClassName('required-entry');
            $('remove-coupone').value = "1";
        } else {
            $('coupon_code').addClassName('required-entry');
            $('remove-coupone').value = "0";
        }
        return VarienForm.prototype.submit.bind(discountForm)();
    }
</script>
