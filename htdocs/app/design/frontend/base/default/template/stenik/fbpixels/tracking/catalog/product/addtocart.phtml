<?php
    $helper = $this->helper('stenik_fbpixels');

    if($helper->isEventHidden('add_to_cart'))
        return;

    if(!$btnClass = $helper->getAddToCartButtonClass())
        return;

    if(!$_product = $this->getProduct())
        return;

/**
 * Required Parameters
 *     content_ids, content_type
 * Other Parameters
 *     value, currency, content_name
 */
?>
<script type="text/javascript">
    var productData = {
        ids:'<?php echo $_product->getSku() ?>',
        value:'<?php echo $_product->getFinalPrice() ?>',
        currency:'<?php echo Mage::app()->getStore()->getCurrentCurrencyCode() ?>',
        name:'<?php echo $_product->getName() ?>'
    };

    jQuery(function($) {
        $.fn.stenikGetEvents = function() {
            if (typeof($._data) == 'function') {
                return $._data(this.get(0), 'events') || {};
            } else if (typeof(this.data) == 'function') { // older jQuery versions
                return this.data('events') || {};
            }

            return {};
        };

        $.fn.stenikPreBind = function(type, data, fn) {
            this.each(function () {
                var $this = $(this);

                $this.bind(type, data, fn);

                var currentBindings = $this.stenikGetEvents()[type];
                if ($.isArray(currentBindings)) {
                    currentBindings.unshift(currentBindings.pop());
                }
            });
            return this;
        };

        // Add fb track click event before the other click events
        $('.<?php echo $btnClass; ?>').stenikPreBind('click', function() {
            sFbTrackAddToCart(productData);
        });
    });
</script>