
<?php if ($this->helper('stenik_sitesettings')->getfreeshipping()): ?>
    <?php
        $grandTotal = $this->getQuote()->getGrandTotal();
        $toFreeDelivery = ($this->helper('stenik_sitesettings')->getfreeshipping() - $grandTotal);
    ?>
    <?php if ($toFreeDelivery <= 0): ?>
        <div class="delivery-price free-shipping">
            <svg aria-hidden="true" class="icon-svg delivery">
                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#delivery' ?>"></use>
            </svg>
            <?php echo $this->__('<strong>Free shipping</strong>');?>
        </div>
    <?php else: ?>
        <div class="delivery-price">
            <svg aria-hidden="true" class="icon-svg delivery">
                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#delivery' ?>"></use>
            </svg>
            <?php echo $this->__('Add items for');?>
            <?php echo $this->helper('checkout')->formatPrice($toFreeDelivery); ?>,
            <?php echo $this->__('to get <strong>free shipping</strong>');?>!
        </div>
    <?php endif ?>
<?php endif ?>


