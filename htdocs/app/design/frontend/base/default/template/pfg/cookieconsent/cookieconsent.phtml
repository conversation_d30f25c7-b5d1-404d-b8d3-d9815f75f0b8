<?php
/** @var $helper PFG_CookieConsent_Helper_Data */
$helper = Mage::helper('pfg_cookieconsent');
/** @var $trackingHelper PFG_Tracking_Helper_Data */
$trackingHelper = Mage::helper('pfg_tracking');
/** @var $groups PFG_CookieConsent_Model_Resource_CookieGroups_Collection */
$groups = Mage::getModel('pfg_cookieconsent/cookieGroups')->getCollection();
/** @var $aptrianHelp Apptrian_FacebookPixel_Helper_Data */
$aptrianHelp = Mage::helper('apptrian_facebookpixel');
$res = [];

function prepContent(string $string)
{
    $string = str_replace('`', '"', $string);
    $string = str_replace('"', "'", $string);

    return $string;
}

foreach ($groups as $group) {
    $cookies = $group->getCookiePattern();
    if ($cookies) {
        $cookies = array_map('trim', explode(',', $cookies));
    } else {
        $cookies = [];
    }

    /** @var $group PFG_CookieConsent_Model_CookieGroups */
    $res[$group->getId()] = [
        'id' => $group->getId(),
        'title' => $group->getTitle(),
        'content' => prepContent($group->getText()),
        'grants' => $group->getGrantsList(),
        'vendors' => $group->getVendorList(),
        'cookiePatterns' => $cookies,
    ];
}
?>
<?php if($helper->getFacebookPixelID()): ?>
<noscript>
  <img height="1" width="1" style="display:none"
       src="https://www.facebook.com/tr?id=<?= $helper->getFacebookPixelID() ?>&ev=PageView&noscript=1"/>
</noscript>
<?php endif; ?>

<div id="pfg-cookieconsent-overlay"></div>
<script type="text/javascript" data-notrigger="1">
    /* <![CDATA[ */
    pfgCookieConsent.InitCookieConsent({
      debug: <?= $helper->isDebugAllowed() ? "true" : "false" ?>,
      rootId: 'pfg-cookieconsent-overlay',
      pixelId: '<?= $helper->getFacebookPixelID() ?? "" ?>',
      gtagId: '<?= $helper->getGtagID() ?? "" ?>',
      clarityId: '<?= $helper->getClarityID() ?? "" ?>',
      modal: {
        title: `<?= $this->quoteEscape($helper->getPopupTitle()); ?>`,
        content: `<?= prepContent($helper->getPopupText()); ?>`,
        cookieGroups: <?= json_encode($res, JSON_UNESCAPED_SLASHES) ?>,
      }
    });
    /* ]]> */
</script>

