<?xml version="1.0"?>
<!--
/**
 * @package  Stenik_CustomerInvoiceFields
 * <AUTHOR> Magento Team <<EMAIL>>
 */
 -->

<config>
    <modules>
        <Stenik_CustomerInvoiceFields>
            <version>1.2.1</version>
        </Stenik_CustomerInvoiceFields>
    </modules>

    <global>
        <blocks>
            <stenik_customerinvoicefields>
                <class>Stenik_CustomerInvoiceFields_Block</class>
            </stenik_customerinvoicefields>

            <customer>
                <rewrite>
                    <widget_taxvat>Stenik_CustomerInvoiceFields_Block_Widget_Taxvat</widget_taxvat>
                </rewrite>
            </customer>
        </blocks>

        <helpers>
            <stenik_customerinvoicefields>
                <class>Stenik_CustomerInvoiceFields_Helper</class>
            </stenik_customerinvoicefields>
        </helpers>

        <models>
            <stenik_customerinvoicefields>
                <class>Stenik_CustomerInvoiceFields_Model</class>
            </stenik_customerinvoicefields>
            <sales>
                <rewrite>
                    <order_pdf_invoice>Stenik_CustomerInvoiceFields_Model_Sales_Order_Pdf_Invoice</order_pdf_invoice>
                </rewrite>
            </sales>
        </models>

        <resources>
            <stenik_customerinvoicefields_setup>
                <setup>
                    <module>Stenik_CustomerInvoiceFields</module>
                    <class>Mage_Eav_Model_Entity_Setup</class>
                </setup>
            </stenik_customerinvoicefields_setup>
        </resources>

        <events>
            <sales_quote_save_before>
                <observers>
                    <stenik_customerinvoicefields>
                        <class>stenik_customerinvoicefields/observer</class>
                        <method>salesQuoteSaveBefore</method>
                    </stenik_customerinvoicefields>
                </observers>
            </sales_quote_save_before>

            <checkout_submit_all_after>
                <observers>
                    <stenik_customerinvoicefields>
                        <class>stenik_customerinvoicefields/observer</class>
                        <method>checkoutSubmitAllAfter</method>
                    </stenik_customerinvoicefields>
                </observers>
            </checkout_submit_all_after>
        </events>

        <fieldsets>
            <customer_account>
                <customer_invoice><create>1</create><update>1</update></customer_invoice>
                <customer_invoice_type><create>1</create><update>1</update></customer_invoice_type>
                <customer_invoice_company_name><create>1</create><update>1</update></customer_invoice_company_name>
                <customer_invoice_company_pic><create>1</create><update>1</update></customer_invoice_company_pic>
                <customer_invoice_company_city><create>1</create><update>1</update></customer_invoice_company_city>
                <customer_invoice_company_addr><create>1</create><update>1</update></customer_invoice_company_addr>
                <customer_invoice_company_urn><create>1</create><update>1</update></customer_invoice_company_urn>
                <customer_invoice_company_vat><create>1</create><update>1</update></customer_invoice_company_vat>
                <customer_invoice_personal_name><create>1</create><update>1</update></customer_invoice_personal_name>
                <customer_invoice_personal_pin><create>1</create><update>1</update></customer_invoice_personal_pin>
                <customer_invoice_personal_city><create>1</create><update>1</update></customer_invoice_personal_city>
                <customer_invoice_personal_addr><create>1</create><update>1</update></customer_invoice_personal_addr>
            </customer_account>

            <checkout_onepage_quote>
                <customer_invoice><to_customer>*</to_customer></customer_invoice>
                <customer_invoice_type><to_customer>*</to_customer></customer_invoice_type>
                <customer_invoice_company_name><to_customer>*</to_customer></customer_invoice_company_name>
                <customer_invoice_company_pic><to_customer>*</to_customer></customer_invoice_company_pic>
                <customer_invoice_company_city><to_customer>*</to_customer></customer_invoice_company_city>
                <customer_invoice_company_addr><to_customer>*</to_customer></customer_invoice_company_addr>
                <customer_invoice_company_urn><to_customer>*</to_customer></customer_invoice_company_urn>
                <customer_invoice_company_vat><to_customer>*</to_customer></customer_invoice_company_vat>
                <customer_invoice_personal_name><to_customer>*</to_customer></customer_invoice_personal_name>
                <customer_invoice_personal_pin><to_customer>*</to_customer></customer_invoice_personal_pin>
                <customer_invoice_personal_city><to_customer>*</to_customer></customer_invoice_personal_city>
                <customer_invoice_personal_addr><to_customer>*</to_customer></customer_invoice_personal_addr>
            </checkout_onepage_quote>

            <sales_convert_quote>
                <customer_invoice><to_order>*</to_order></customer_invoice>
                <customer_invoice_type><to_order>*</to_order></customer_invoice_type>
                <customer_invoice_company_name><to_order>*</to_order></customer_invoice_company_name>
                <customer_invoice_company_pic><to_order>*</to_order></customer_invoice_company_pic>
                <customer_invoice_company_city><to_order>*</to_order></customer_invoice_company_city>
                <customer_invoice_company_addr><to_order>*</to_order></customer_invoice_company_addr>
                <customer_invoice_company_urn><to_order>*</to_order></customer_invoice_company_urn>
                <customer_invoice_company_vat><to_order>*</to_order></customer_invoice_company_vat>
                <customer_invoice_personal_name><to_order>*</to_order></customer_invoice_personal_name>
                <customer_invoice_personal_pin><to_order>*</to_order></customer_invoice_personal_pin>
                <customer_invoice_personal_city><to_order>*</to_order></customer_invoice_personal_city>
                <customer_invoice_personal_addr><to_order>*</to_order></customer_invoice_personal_addr>
            </sales_convert_quote>

            <sales_convert_order>
                <customer_invoice><to_quote>*</to_quote></customer_invoice>
                <customer_invoice_type><to_quote>*</to_quote></customer_invoice_type>
                <customer_invoice_company_name><to_quote>*</to_quote></customer_invoice_company_name>
                <customer_invoice_company_pic><to_quote>*</to_quote></customer_invoice_company_pic>
                <customer_invoice_company_city><to_quote>*</to_quote></customer_invoice_company_city>
                <customer_invoice_company_addr><to_quote>*</to_quote></customer_invoice_company_addr>
                <customer_invoice_company_urn><to_quote>*</to_quote></customer_invoice_company_urn>
                <customer_invoice_company_vat><to_quote>*</to_quote></customer_invoice_company_vat>
                <customer_invoice_personal_name><to_quote>*</to_quote></customer_invoice_personal_name>
                <customer_invoice_personal_pin><to_quote>*</to_quote></customer_invoice_personal_pin>
                <customer_invoice_personal_city><to_quote>*</to_quote></customer_invoice_personal_city>
                <customer_invoice_personal_addr><to_quote>*</to_quote></customer_invoice_personal_addr>
            </sales_convert_order>
        </fieldsets>
    </global>

    <adminhtml>
        <layout>
            <updates>
                <stenik_customerinvoicefields>
                    <file>stenik_customerinvoicefields.xml</file>
                </stenik_customerinvoicefields>
            </updates>
        </layout>

        <translate>
            <modules>
                <Stenik_CustomerInvoiceFields>
                    <files>
                        <default>Stenik_CustomerInvoiceFields.csv</default>
                    </files>
                </Stenik_CustomerInvoiceFields>
            </modules>
        </translate>
    </adminhtml>

    <frontend>
        <layout>
            <updates>
                <stenik_customerinvoicefields>
                    <file>stenik_customerinvoicefields.xml</file>
                </stenik_customerinvoicefields>
            </updates>
        </layout>

        <translate>
            <modules>
                <Stenik_CustomerInvoiceFields>
                    <files>
                        <default>Stenik_CustomerInvoiceFields.csv</default>
                    </files>
                </Stenik_CustomerInvoiceFields>
            </modules>
        </translate>

        <events>
            <core_block_abstract_to_html_after>
                <observers>
                    <stenik_customerinvoicefields>
                        <class>stenik_customerinvoicefields/observer</class>
                        <method>frontendCoreBlockAbstractToHtmlAfter</method>
                    </stenik_customerinvoicefields>
                </observers>
            </core_block_abstract_to_html_after>
            <customer_save_before>
                <observers>
                    <stenik_customerinvoicefields>
                        <class>stenik_customerinvoicefields/observer</class>
                        <method>customerSaveBefore</method>
                    </stenik_customerinvoicefields>
                </observers>
            </customer_save_before>
        </events>
    </frontend>
</config>
