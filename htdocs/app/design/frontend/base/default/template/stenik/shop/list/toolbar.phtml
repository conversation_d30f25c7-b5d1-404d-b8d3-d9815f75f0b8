<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>
<?php
    $shopCollection = $this->getCollection();

    /* Gather shops' first char information */
    $arrayRangeFromAToZ = range('A', 'Z');
    $arrayRangeFromZeroToNine = range(0, 9);

    $charGroupShopIds = array();
    foreach (array_merge($arrayRangeFromAToZ, array('0-9', 'А-Я')) as $charGroupKey) {
        $charGroupShopIds[$charGroupKey] = array();
    }

    foreach ($shopCollection as $shop) {
        $firstChar = mb_substr(($shop->getName()), 0, 1, 'UTF-8');

        if (in_array($firstChar, $arrayRangeFromAToZ)) {
            $charGroupShopIds[$firstChar][] = $shop->getId();
        } elseif (isset($arrayRangeFromZeroToNine[$firstChar])) {
            $charGroupShopIds['0-9'][] = $shop->getId();
        } else {
            $charGroupShopIds['А-Я'][] = $shop->getId();
        }
    }

    /* Gather shops' tags information */
    $tagShopIds = array();

    foreach ($shopCollection as $shop) {
        foreach ($shop->getTags() as $tag) {
            if (!isset($tagShopIds[$tag]))
                $tagShopIds[$tag] = array();

            $tagShopIds[$tag][] = $shop->getId();
        }
    }
?>
<div class="toolbar">
    <ul class="alphabet filterBox">
        <?php foreach ($charGroupShopIds as $charGroupKey => $shopIds): ?>
            <li>
                <a href="javascript:;"
                   data-shops="<?php echo $this->escapeHtml(implode(',', $shopIds)); ?>"
                   data-shop-filter-code="firstchar"
                   class="filter<?php if (!count($shopIds)): ?> disabled<?php endif; ?>"
                >
                    <?php echo $this->escapeHtml($charGroupKey); ?>
                </a>
            </li>
        <?php endforeach ?>
    </ul>
    <?php if ($tagShopIds): ?>
        <nav class="storeNav">
            <ul>
                <?php $tagShopIds = array_merge(array($this->__('All') => array('all')), $tagShopIds); ?>
                <?php foreach ($tagShopIds as $tag => $shopIds): ?>    
                    <li>
                        <a href="javascript:;"
                           data-shops="<?php echo $this->escapeHtml(implode(',', $shopIds)); ?>"
                           data-shop-filter-code="tag"
                           class="filter<?php if (!count($shopIds)): ?> disabled<?php endif; ?><?php if (in_array('all', $shopIds)): ?> active selected<?php endif; ?>"
                        >
                            <?php echo $this->escapeHtml($tag); ?>
                        </a>
                    </li>            
                <?php endforeach ?>
            </ul>
        </nav>
    <?php endif; ?>
</div>