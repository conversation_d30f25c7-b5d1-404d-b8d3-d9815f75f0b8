<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div style="border:1px solid #ccc; background:#f6f6f6;">
    <h2 style="margin-bottom:0; border-bottom:1px solid #ccc; padding:4px 10px; color:#3c5974; font-size:1.4em;"><?php echo $this->__('Installation') ?></h2>
<ol style="padding:10px; border-top:1px solid #fff;">
<?php foreach($this->getDownloaderSteps() as $step): ?>
    <li><?php echo $step ?></li>
<?php endforeach; ?>
    <li><?php echo $this->__('Download')?></li>
<?php foreach($steps as $_step): ?>
    <li <?php if($_step->getActive()): ?>style="color:green; font-weight:bold; "<?php endif ?>><?php echo $this->__($_step->getCode()) ?></li>
<?php endforeach ?>
</ol>
</div>

<br/>
<p>
    <?php echo $this->__('Having trouble installing Magento?') ?>
    <?php echo $this->__('Check out our') ?> <a href="http://www.magentocommerce.com/knowledge-base/entry/magento-installation-guide" id="installation_guide_link"><?php echo $this->__('Installation Guide') ?></a>
    <script type="text/javascript">
        $('installation_guide_link').target = "installation_guide";
    </script>
</p>
