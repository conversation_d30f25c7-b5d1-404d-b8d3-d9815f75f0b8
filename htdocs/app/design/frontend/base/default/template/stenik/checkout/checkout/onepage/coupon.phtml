<?php
/**
 * @package Stenik_Checkout
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<div class="stenikCheckoutCouponWrapper discount">
    <h2><?php echo $this->__('Discount Codes') ?></h2>
    <div class="discount-form">
        <label for="coupon_code"><?php echo $this->__('Enter your coupon code if you have one.') ?></label>
        <input type="hidden" name="coupon[remove]" id="remove-coupone" value="0" />
        <input type="hidden" name="coupon[rnd]" id="rnd-coupone" value="0" />
        <div class="input-box">
            <input class="input-text" id="coupon_code" name="coupon[coupon_code]" value="<?php echo $this->escapeHtml($this->getCouponCode()) ?>" />
        </div>
        <div class="buttons-set">
            <button type="button" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Apply Coupon')) ?>" class="button" onclick="stenikCheckoutSaveCoupon(false); return false;" value="<?php echo Mage::helper('core')->quoteEscape($this->__('Apply Coupon')) ?>"><span><span><?php echo $this->__('Apply Coupon') ?></span></span></button>
            <?php if(strlen($this->getCouponCode())): ?>
                &nbsp; <button type="button" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Cancel Coupon')) ?>" class="button" onclick="stenikCheckoutSaveCoupon(true); return false;" value="<?php echo Mage::helper('core')->quoteEscape($this->__('Cancel Coupon')) ?>"><span><span><?php echo $this->__('Cancel Coupon') ?></span></span></button>
            <?php endif;?>
        </div>
    </div>
</div>

<script>
    window.stenikCheckoutSaveCoupon = function(remove) {
        jQuery('#remove-coupone').val(remove ? 1 : 0);
        jQuery('#rnd-coupone').val(Date.now()); <?php // Force data save even if the prev data was the same ?>
        stenikOnepageCheckout.validateAndSaveSection('coupon');
    }
</script>