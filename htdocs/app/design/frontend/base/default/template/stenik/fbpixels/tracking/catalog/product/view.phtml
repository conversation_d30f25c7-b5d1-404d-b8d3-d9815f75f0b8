<?php
    $helper = $this->helper('stenik_fbpixels');

    if($helper->isEventHidden('view_content'))
        return;

    if(!Mage::registry('current_product'))
        return;

    $_product = Mage::registry('current_product');
/**
 * Required parameters
 *     content_type, content_ids
 * Other supported parameters
 *     value, currency, content_name
 *
 */
?>
<script>
fbq('track', 'ViewContent', {
    content_name: '<?php echo $_product->getName(); ?>',
    content_ids: ['<?php echo $_product->getSku() ?>'],
    content_type: 'product',
    value: '<?php echo $_product->getFinalPrice() ?>',
    currency: '<?php echo Mage::app()->getStore()->getCurrentCurrencyCode() ?>'
});
</script>
