<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<div class="gender-widget">
    
    <span class="gender-label"><?php if ($this->isRequired()):?>* <?php endif; ?><?php echo $this->__('Gender') ?></span>

    <?php
        $options = Mage::getResourceSingleton('customer/customer')->getAttribute('gender')->getSource()->getAllOptions();
        $value = $this->getGender();
        $_last = count($options);
        $_index = 0;
        $lastInputId = null;
        $inputIds = array();
    ?>
    <?php foreach ($options as $option):?>
        <?php if($option['value'] != ''): ?>
            <input
                id="<?php echo $this->getFieldId('gender')?>-<?php echo $option['value'] ?>"
                class="radio<?php if ($this->isRequired()):?> validate-one-required-by-name<?php endif; ?>"
                type="radio" title="<?php echo $this->__($option['label']) ?>"
                value="<?php echo $option['value'] ?>" name="<?php echo $this->getFieldName('gender')?>"
                <?php if ($option['value'] == $value) echo ' checked="checked"' ?>
            >
            <label for="<?php echo $this->getFieldId('gender')?>-<?php echo $option['value'] ?>" class="radio-label">
                <?php echo $this->__($option['label']) ?>
            </label>
            <?php $inputIds[] = $this->getFieldId('gender') . '-' . $option['value'] ?>
            <?php $lastInputId = $this->getFieldId('gender') . '-' . $option['value'] ?>
        <?php endif; ?>

        <?php $_index++; ?>
    <?php endforeach;?>

    <?php foreach ($inputIds as $inputId): ?>
        <?php if ($inputId == $lastInputId): ?>
            <div style="display: none" id="advice-validate-one-required-by-name-<?php echo $inputId ?>" class="validation-advice genderError">
                <?php echo $this->__('Please select one of the options.');?>
            </div>
        <?php else: ?>
            <div style="display: none">
                <div style="display: none" id="advice-validate-one-required-by-name-<?php echo $inputId ?>" class="validation-advice">
                    <?php echo $this->__('Please select one of the options.');?>
                </div>
            </div>
        <?php endif ?>
    <?php endforeach ?>

</div>