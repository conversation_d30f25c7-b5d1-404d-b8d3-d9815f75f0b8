<?php
/**
 * Incho<PERSON> is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
?>
<p><?php echo $this->__('Already have an account? Sign in to complete your order automatically with your data.') ?></p>

<div class="social-login clearfix">
    <?php if($this->_facebookEnabled()): ?>
        <?php echo $this->getChildHtml('inchoo_socialconnect_checkout_facebook_button'); ?>
    <?php endif; ?>
    <?php if($this->_googleEnabled()): ?>
        <?php echo $this->getChildHtml('inchoo_socialconnect_checkout_google_button'); ?>
    <?php endif; ?>
    <p class="or"><span><?php echo $this->__('or') ?></span></p>
</div>
