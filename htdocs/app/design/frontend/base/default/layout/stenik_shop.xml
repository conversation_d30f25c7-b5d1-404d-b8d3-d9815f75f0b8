<?xml version="1.0"?>
<layout version="0.1.0">
    <stenik_shop_shop_list>
        <update handle="stenik_jquery"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="stenik_shop/shop_listPage" name="shop.list" template="stenik/shop/list.phtml"/>
        </reference>
    </stenik_shop_shop_list>

    <stenik_shop_shop_view>
        <update handle="stenik_jquery"/>
        <update handle="stenik_jquery_colorbox"/>
        <update handle="stenik_jquery_royalslider2"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="stenik_shop/shop_view" name="shop.view" template="stenik/shop/view.phtml"/>
        </reference>
    </stenik_shop_shop_view>

    <stenik_shop_shop_typeview>
        <update handle="stenik_jquery"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="stenik_shop/shop_type_view" name="shop.typeView" template="stenik/shop/type/view.phtml">
                <block type="stenik_shop/shop_list" name="shop.list" template="stenik/shop/list.phtml"/>
            </block>
        </reference>
    </stenik_shop_shop_typeview>

    <!--
        Dynamic handles:
            STENIK_SHOP_SHOP_TYPEVIEW_<type>
    -->
</layout>