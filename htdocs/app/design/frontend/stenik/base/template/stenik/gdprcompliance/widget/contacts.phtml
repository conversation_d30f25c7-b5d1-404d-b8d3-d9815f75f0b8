<?php $helper = $this->helper('stenik_gdprcompliance/agreement_contacts'); ?>
<?php if ($helper->isEnabled()): ?>
    <div class="checkbox-content">
        <input name="gdpr_personal_data" id="gdpr_personal_data" title="<?php echo $this->htmlEscape(strip_tags($helper->getAgreement())); ?>" class="checkbox required-entry" type="checkbox" value="1" />
        <label for="gdpr_personal_data" class="long-label"><?php echo $helper->getAgreement(); ?></label>
        <?php if ($helper->getAdditionalInformation()): ?>
            <div class="gdpr-info">
                <p><?php echo $helper->getAdditionalInformation(); ?></p>
            </div>
        <?php endif; ?>
    </div>
    <div style="display:none;">
        <div id="termsPopUp" class="text-page terms-popup">
            <h1><?php echo $this->helper('stenik_gdprcompliance')->getTermsPageTitle(); ?></h1>
            <?php echo $this->helper('stenik_gdprcompliance')->getTermsPageContent(); ?>
        </div>
        <div id="privacyPolicyPopUp" class="text-page terms-popup">
            <h1><?php echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageTitle(); ?></h1>
            <?php echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageContent(); ?>
        </div>
    </div>
    <script>
        jQuery(function(){
            jQuery('.termsOpen').colorbox({inline:true});
        });
    </script>
<?php else: ?>
    <?php if ($helper->getAdditionalInformation()): ?>
        <div class="gdpr-info">
            <p><?php echo $helper->getAdditionalInformation(); ?></p>
        </div>
    <?php endif; ?>
<?php endif; ?>