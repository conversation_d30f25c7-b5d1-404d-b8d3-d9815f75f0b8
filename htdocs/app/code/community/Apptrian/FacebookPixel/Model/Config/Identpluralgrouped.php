<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */
class Apptrian_FacebookPixel_Model_Config_Identpluralgrouped
{
    const IDENT_PRODUCT              = '1';
    const IDENT_CHILDREN             = '2';
    const IDENT_CHILDREN_AND_PRODUCT = '3';
    
    protected $_options;
    
    public function toOptionArray()
    {
        if (!$this->_options) {
            $this->_options[] = array(
                'value' => self::IDENT_PRODUCT,
                'label' => Mage::helper('apptrian_facebookpixel')
                    ->__('Product SKU as (id) (Not Recommended)')
            );
            $this->_options[] = array(
                'value' => self::IDENT_CHILDREN,
                'label' => Mage::helper('apptrian_facebookpixel')
                    ->__('Children SKUs as (id)s')
            );
            $this->_options[] = array(
                'value' => self::IDENT_CHILDREN_AND_PRODUCT,
                'label' => Mage::helper('apptrian_facebookpixel')
                    ->__('Children SKUs as (id)s and Product SKU as (item_group_id)')
            );
        }
        
        return $this->_options;
    }
}
