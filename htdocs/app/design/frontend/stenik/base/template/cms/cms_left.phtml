<?php
/**
 * @package     Stenik_Template
 * <AUTHOR> <<EMAIL>>
 */
?>
<?php
    $moduleName        = Mage::app()->getFrontController()->getRequest()->getModuleName();
    $cmsPageIdentifier = Mage::getSingleton('cms/page')->getIdentifier();
?>

<a href="javascript:;" class="button open-responsive-sidebar"><?php echo $this->__('Other pages');?></a>
<div class="sidebar-responsive-wrapper">
    <nav class="sidebar-nav">
        <?php echo $this->getChildHtml('cms_block.sidebar_static_menu'); ?>
    </nav>
</div>

<script>
    jQuery(function() {
        jQuery("nav.sidebar-nav ul li").last().addClass('last');
        <?php if($moduleName == 'cms'): ?>
            jQuery('nav.sidebar-nav ul li a').each(function() {
                if(jQuery(this).attr('id') == '<?php echo $cmsPageIdentifier ?>') {
                    jQuery("nav.sidebar-nav ul li a#<?php echo $cmsPageIdentifier ?>").parent().addClass('active');
                }
            });
        <?php endif ?>
    });
</script>