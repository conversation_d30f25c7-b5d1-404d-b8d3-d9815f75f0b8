<?php

?>

<?php
    $stepCode = $this->getBlockAlias() ? : $this->getNameInLayout();
?>

<div id="sc-checkout-step-<?php echo $stepCode ?>" data-checkout-step="<?php echo $stepCode ?>" class="sc-checkout-step" style="display: none">
    <form id="co-<?php echo $stepCode ?>-form" action="">
        <h2 class="stepTitle"><?php echo $this->escapeHtml($this->getStepTitle()) ?></h2>
        <?php echo $this->getChildHtml(); ?>

        <div class="buttons-set" id="<?php echo $stepCode ?>-buttons-container">
            <?php
                $buttonTitle = $this->__('Continue');
                if ($this->getIsLastStep()) {
                    $buttonTitle = $this->__('Place Order');
                }
            ?>

            <?php if ($this->getIsLastStep()): ?>
                <input type="hidden" name="place_order" value="1">
            <?php endif ?>

            <button
                type="button"
                title="<?php echo $buttonTitle ?>"
                class="button checkout-color"
                onclick="stenikOnepageCheckout.save('<?php echo $stepCode ?>')"
            >
                <?php echo $buttonTitle ?>
            </button>
            <span class="please-wait" id="<?php echo $stepCode ?>-please-wait" style="display:none;"></span>
        </div>
    </form>
</div>

<script>
    stenikOnepageCheckout.addStep(<?php echo json_encode($stepCode) ?>);
    // var addressAndShippingMethodForm = new VarienForm('co-<?php echo $stepCode ?>-form');
    // $(addressAndShippingMethodForm.form).observe('submit', function(event){
    //     this.save('<?php echo $stepCode ?>', $('co-<?php echo $stepCode ?>-form'));
    //     Event.stop(event);
    // }.bind(stenikOnepageCheckout));
</script>
