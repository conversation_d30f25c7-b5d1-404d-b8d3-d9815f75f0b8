<?php
/**
 *
 * @package Stenik_AdminPromoCatalogApply
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_AdminPromoCatalogApply_Block_Adminhtml_Content extends Mage_Adminhtml_Block_Template
{
    /**
     * Return all cronjobs with code stenik_adminpromocatalogapply
     *
     * @return Mage_Cron_Model_Resource_Schedule_Collection
     */
    public function getCronjobs()
    {
        /** @var Mage_Cron_Model_Resource_Schedule_Collection $collection */
        $collection = Mage::getModel('cron/schedule')->getCollection();
        $collection->addFieldToFilter('job_code', 'stenik_adminpromocatalogapply');
        $collection->setOrder('schedule_id', 'desc');
        return $collection;
    }
}
