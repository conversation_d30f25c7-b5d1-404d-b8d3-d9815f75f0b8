<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
 * Inchoo is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
-->

<layout>
    <customer_account_login>
        <reference name="customer_form_login">
            <block type="inchoo_socialconnect/login" name="inchoo_socialconnect_login">
                <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_google_login_button" />
                <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_facebook_login_button" />
                <block type="inchoo_socialconnect/twitter_button" name="inchoo_socialconnect_twitter_login_button" />
                <block type="inchoo_socialconnect/linkedin_button" name="inchoo_socialconnect_linkedin_login_button" />
            </block>
        </reference>
    </customer_account_login>

    <customer_account_create>
        <reference name="customer_form_register">
            <block type="inchoo_socialconnect/register" name="inchoo_socialconnect_register">
                <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_checkout_google_button" template="inchoo/socialconnect/google/button-register.phtml" />
                <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_checkout_facebook_button" template="inchoo/socialconnect/facebook/button-register.phtml" />
                <block type="inchoo_socialconnect/twitter_button" name="inchoo_socialconnect_checkout_twitter_button" />
                <block type="inchoo_socialconnect/linkedin_button" name="inchoo_socialconnect_checkout_linkedin_button" />
            </block>
        </reference>
    </customer_account_create>

    <customer_account>
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="inchoo_socialconnect"
                ifconfig="customer/inchoo_socialconnect_google/enabled">
                <name>inchoo_socialconnect_google</name>
                <path>socialconnect/account/google</path>
                <label>Google Connect</label>
            </action>
            <action method="addLink" translate="label" module="inchoo_socialconnect"
                ifconfig="customer/inchoo_socialconnect_facebook/enabled">
                <name>inchoo_socialconnect_facebook</name>
                <path>socialconnect/account/facebook</path>
                <label>Facebook Connect</label>
            </action>
            <action method="addLink" translate="label" module="inchoo_socialconnect"
                ifconfig="customer/inchoo_socialconnect_twitter/enabled">
                <name>inchoo_socialconnect_twitter</name>
                <path>socialconnect/account/twitter</path>
                <label>Twitter Connect</label>
            </action>
            <action method="addLink" translate="label" module="inchoo_socialconnect"
                ifconfig="customer/inchoo_socialconnect_linkedin/enabled">
                <name>inchoo_socialconnect_linkedin</name>
                <path>socialconnect/account/linkedin</path>
                <label>LinkedIn Connect</label>
            </action>
        </reference>
    </customer_account>

    <inchoo_socialconnect_account_google translate="label">
        <label>Customer My Account Google Connect</label>
        <update handle="customer_account"/>
        <reference name="head">
            <action method="setTitle" translate="title" module="inchoo_socialconnect">
                <title>Google Connect</title>
            </action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="inchoo_socialconnect/google_account" name="inchoo_socialconnect_google_account">
                <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_account_google_button" />
            </block>
        </reference>
        <reference name="customer_account_navigation">
            <action method="setActive" module="inchoo_socialconnect">
                <path>socialconnect/account/google</path>
            </action>
        </reference>
    </inchoo_socialconnect_account_google>

   <inchoo_socialconnect_account_facebook translate="label">
        <label>Customer My Account Facebook Connect</label>
        <update handle="customer_account"/>
        <reference name="head">
            <action method="setTitle" translate="title" module="inchoo_socialconnect">
                <title>Facebook Connect</title>
            </action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="inchoo_socialconnect/facebook_account" name="inchoo_socialconnect_facebook_account">
                <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_account_facebook_button" />
            </block>
        </reference>
        <reference name="customer_account_navigation">
            <action method="setActive" module="inchoo_socialconnect">
                <path>socialconnect/account/facebook</path>
            </action>
        </reference>
    </inchoo_socialconnect_account_facebook>

   <inchoo_socialconnect_account_twitter translate="label">
        <label>Customer My Account Twitter Connect</label>
        <update handle="customer_account"/>
        <reference name="head">
            <action method="setTitle" translate="title" module="inchoo_socialconnect">
                <title>Twitter Connect</title>
            </action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="inchoo_socialconnect/twitter_account" name="inchoo_socialconnect_twitter_account">
                <block type="inchoo_socialconnect/twitter_button" name="inchoo_socialconnect_account_twitter_button" />
            </block>
        </reference>
        <reference name="customer_account_navigation">
            <action method="setActive" module="inchoo_socialconnect">
                <path>socialconnect/account/twitter</path>
            </action>
        </reference>
    </inchoo_socialconnect_account_twitter>

   <inchoo_socialconnect_account_linkedin translate="label">
        <label>Customer My Account LinkedIn Connect</label>
        <update handle="customer_account"/>
        <reference name="head">
            <action method="setTitle" translate="title" module="inchoo_socialconnect">
                <title>LinkedIn Connect</title>
            </action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="inchoo_socialconnect/linkedin_account" name="inchoo_socialconnect_linkedin_account">
                <block type="inchoo_socialconnect/linkedin_button" name="inchoo_socialconnect_account_linkedin_button" />
            </block>
        </reference>
        <reference name="customer_account_navigation">
            <action method="setActive" module="inchoo_socialconnect">
                <path>socialconnect/account/linkedin</path>
            </action>
        </reference>
    </inchoo_socialconnect_account_linkedin>

    <checkout_onepage_index>
        <reference name="head">
        </reference>
        <reference name="checkout.onepage.login">
            <block type="inchoo_socialconnect/checkout" name="inchoo_socialconnect_checkout">
                <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_checkout_google_button" />
                <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_checkout_facebook_button" />
                <block type="inchoo_socialconnect/twitter_button" name="inchoo_socialconnect_checkout_twitter_button" />
                <block type="inchoo_socialconnect/linkedin_button" name="inchoo_socialconnect_checkout_linkedin_button" />
            </block>
        </reference>
    </checkout_onepage_index>
</layout>