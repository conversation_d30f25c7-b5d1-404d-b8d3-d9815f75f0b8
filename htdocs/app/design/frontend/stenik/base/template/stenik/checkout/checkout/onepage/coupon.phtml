<?php
/**
 * @package Stenik_Checkout
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<div class="discount checkout-review-col-discount">
    <span class="title"><?php echo $this->__('You have a promo code?');?></span>
    <div class="discount-form checkout-fake-discount-form">
        <input type="hidden" name="coupon[remove]" id="remove-coupone" value="0" />
        <input type="hidden" name="coupon[rnd]" id="rnd-coupone" value="0" />

        <input class="input-text" id="coupon_code" name="coupon[coupon_code]" value="<?php echo $this->escapeHtml($this->getCouponCode()) ?>" />
        <?php if(strlen($this->getCouponCode())): ?>
            <button class="discount-button" onclick="stenikCheckoutSaveCoupon(true); return false;">
                <svg aria-hidden="true" class="icon-svg close">
                    <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#close' ?>"></use>
                </svg>
            </button>
        <?php else: ?>
            <button class="discount-button" onclick="stenikCheckoutSaveCoupon(false); return false;">
                <svg aria-hidden="true" class="icon-svg check">
                    <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#check' ?>"></use>
                </svg>
            </button>
        <?php endif ?>
    </div>
</div>



<script>
    window.stenikCheckoutSaveCoupon = function(remove) {
        jQuery('#remove-coupone').val(remove ? 1 : 0);
        jQuery('#rnd-coupone').val(Date.now()); <?php // Force data save even if the prev data was the same ?>
        stenikOnepageCheckout.validateAndSaveSection('coupon');
    }

    jQuery(function($) {

        $('.checkout-review-col-discount .title').click(function() {
            if($(this).hasClass('opened')) {
                $(this).removeClass('opened');
                $('.checkout-fake-discount-form').stop(true, true).slideUp();
            }
            else {
                $(this).addClass('opened');
                $('.checkout-fake-discount-form').stop(true, true).slideDown();
            }
        });

    });
</script>