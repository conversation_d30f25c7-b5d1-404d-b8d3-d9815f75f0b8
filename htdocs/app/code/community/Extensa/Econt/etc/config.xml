<?xml version="1.0"?>
<!--
/**
 * Module configuration
 *
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
-->
<config>
    <modules>
        <Extensa_Econt>
            <version>1.4.0.2</version>
        </Extensa_Econt>
    </modules>
    <global>
        <models>
            <extensa_econt>
                <class>Extensa_Econt_Model</class>
                <resourceModel>econt_resource</resourceModel>
            </extensa_econt>
            <econt_resource>
                <class>Extensa_Econt_Model_Resource</class>
                <entities>
                    <city>
                        <table>extensa_econt_city</table>
                    </city>
                    <cityoffice>
                        <table>extensa_econt_city_office</table>
                    </cityoffice>
                    <country>
                        <table>extensa_econt_country</table>
                    </country>
                    <customer>
                        <table>extensa_econt_customer</table>
                    </customer>
                    <loading>
                        <table>extensa_econt_loading</table>
                    </loading>
                    <loadingtracking>
                        <table>extensa_econt_loading_tracking</table>
                    </loadingtracking>
                    <office>
                        <table>extensa_econt_office</table>
                    </office>
                    <order>
                        <table>extensa_econt_order</table>
                    </order>
                    <quarter>
                        <table>extensa_econt_quarter</table>
                    </quarter>
                    <region>
                        <table>extensa_econt_region</table>
                    </region>
                    <street>
                        <table>extensa_econt_street</table>
                    </street>
                    <zone>
                        <table>extensa_econt_zone</table>
                    </zone>
                </entities>
            </econt_resource>
        </models>
        <helpers>
            <extensa_econt>
                <class>Extensa_Econt_Helper</class>
            </extensa_econt>
        </helpers>
        <blocks>
            <extensa_econt>
                <class>Extensa_Econt_Block</class>
            </extensa_econt>
        </blocks>
        <resources>
            <extensa_econt_setup>
                <setup>
                    <module>Extensa_Econt</module>
                </setup>
            </extensa_econt_setup>
        </resources>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <Extensa_Econt before="Mage_Adminhtml">Extensa_Econt_Adminhtml</Extensa_Econt>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <extensa_econt>
                    <file>extensa/econt.xml</file>
                </extensa_econt>
            </updates>
        </layout>
        <translate>
            <modules>
                <Extensa_Econt>
                    <files>
                        <default>Extensa_Econt.csv</default>
                    </files>
                </Extensa_Econt>
            </modules>
        </translate>
    </adminhtml>
    <frontend>
        <routers>
            <extensa_econt>
                <use>standard</use>
                <args>
                    <module>Extensa_Econt</module>
                    <frontName>extensa_econt</frontName>
                </args>
            </extensa_econt>
        </routers>
        <events>
            <payment_method_is_active>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>restrictPaymentsCd</method>
                    </extensa_econt>
                </observers>
            </payment_method_is_active>
            <checkout_type_onepage_save_order_after>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>saveOnepageOrder</method>
                    </extensa_econt>
                </observers>
            </checkout_type_onepage_save_order_after>
        </events>
        <layout>
            <updates>
                <extensa_econt>
                    <file>extensa/econt.xml</file>
                </extensa_econt>
            </updates>
        </layout>
        <translate>
            <modules>
                <Extensa_Econt>
                    <files>
                        <default>Extensa_Econt.csv</default>
                    </files>
                </Extensa_Econt>
            </modules>
        </translate>
    </frontend>
    <default>
        <carriers>
            <extensa_econt>
                <model>extensa_econt/shipping_carrier_econt</model>
                <title>Еконт Експрес</title>
                <name_door>Еконт Експрес - до врата</name_door>
                <name_office>Еконт Експрес - до офис</name_office>
                <name_aps>Еконт Експрес - до АПС</name_aps>
                <shipping_from>DOOR</shipping_from>
                <to_door>1</to_door>
                <cd>1</cd>
                <side>RECEIVER</side>
                <payment_method>CASH</payment_method>
                <currency>BGN</currency>
                <allowspecific>1</allowspecific>
                <specificcountry>BG</specificcountry>
            </extensa_econt>
        </carriers>
        <payment>
            <extensa_econt>
                <model>extensa_econt/payment_method_econt</model>
                <title>Еконт Експрес наложен платеж</title>
                <allowspecific>1</allowspecific>
                <specificcountry>BG</specificcountry>
            </extensa_econt>
        </payment>
    </default>
</config>