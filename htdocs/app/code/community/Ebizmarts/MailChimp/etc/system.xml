<?xml version="1.0"?>

<config>
    <tabs>
        <mailchimp translate="label" module="mailchimp">
            <label><![CDATA[Mailchimp]]></label>
            <sort_order>400</sort_order>
        </mailchimp>
    </tabs>
    <sections>
        <mailchimp translate="label" module="mailchimp">
            <class>mailchimp-section</class>
            <label>Mailchimp Configuration</label>
            <header_css>mailchimp-header</header_css>
            <tab>mailchimp</tab>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <hint>
                    <frontend_model>mailchimp/adminhtml_system_config_fieldset_mailchimp_hint</frontend_model>
                    <sort_order>0</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                </hint>
                <general translate="label comment">
                    <label>Mailchimp integration by ebizmarts</label>
                    <frontend_type>text</frontend_type>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>1</sort_order>
                    <fields>
                        <active translate="label comment">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <comment><![CDATA[By enabling this extension, you agree to <a href="https://mailchimp.com/legal/additional-terms/" target="_blank">Mailchimp's additional terms</a>.]]></comment>
                            <backend_model>mailchimp/system_config_backend_active</backend_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </active>
                        <apikey translate="label comment">
                            <label>API Key</label>
                            <frontend_type>obscure</frontend_type>
                            <sort_order>20</sort_order>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Please click the button below to get your API key.]]></comment>
                        </apikey>
                        <oauth_wizard translate="button_label">
                            <label></label>
                            <button_label>Get API credentials</button_label>
                            <frontend_model>mailchimp/adminhtml_system_config_oauthWizard</frontend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </oauth_wizard>
                        <storeid translate="label comment">
                            <label>Mailchimp Store</label>
                            <frontend_type>select</frontend_type>
                            <source_model>Ebizmarts_MailChimp_Model_System_Config_Source_Store</source_model>
                            <backend_model>mailchimp/system_config_backend_store</backend_model>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>0</can_be_empty>
                            <comment>
                                <model>mailchimp/adminhtml_storeid_comment</model>
                            </comment>
                        </storeid>
                        <account_details translate="label">
                            <label>Account details</label>
                            <frontend_model>mailchimp/adminhtml_system_config_account</frontend_model>
                            <source_model>Ebizmarts_MailChimp_Model_System_Config_Source_Account</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </account_details>
                        <reset_errors>
                            <frontend_type>button</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_resetErrors</frontend_model>
                            <sort_order>42</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </reset_errors>
                        <resend_subscriber>
                            <frontend_type>button</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_resendSubscribers</frontend_model>
                            <sort_order>44</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </resend_subscriber>
                        <list translate="label comment">
                            <label>General Subscription</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_list</source_model>
                            <backend_model>mailchimp/system_config_backend_list</backend_model>
                            <sort_order>45</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                            <comment><![CDATA[Synchronize Magento's General Subscription Audience with this Mailchimp audience.]]></comment>
                        </list>
                        <interest_categories translate="label comment">
                            <label>Interest Categories</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>mailchimp/system_config_source_customerGroup</source_model>
                            <sort_order>48</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                            <comment><![CDATA[Select MailChimp categories a subscriber can join.]]></comment>
                        </interest_categories>
                        <interest_in_success translate="label comment">
                            <label>Interest groups in checkout success</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>49</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[In case no Interest groups are selected it will work as a normal subscription to the configured audience]]></comment>
                        </interest_in_success>
                        <interest_success_before translate="label">
                            <label>Add html code before the interest groups are displayed</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                            <depends>
                                <interest_in_success>1</interest_in_success>
                            </depends>
                        </interest_success_before>
                        <interest_success_after translate="label">
                            <label>Add html code after the interest groups are displayed</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>51</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                            <depends>
                                <interest_in_success>1</interest_in_success>
                            </depends>
                        </interest_success_after>
                        <subscriber_batch_amount translate="label comment">
                            <label>Subscriber amount to be sent per run</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_batchLimit</source_model>
                            <sort_order>53</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment><![CDATA[Don't touch this value]]></comment>
                        </subscriber_batch_amount>
                        <connection_timeout translate="label comment">
                            <label>Connection timeout (in secs)</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_connectionTimeout</source_model>
                            <sort_order>54</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment><![CDATA[Don't touch this value]]></comment>
                        </connection_timeout>
                        <checkout_subscribe translate="label comment">
                            <label>Subscribe On Checkout</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_checkoutsubscribe</source_model>
                            <sort_order>55</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Show Newsletter Subscribe checkbox in the last Checkout Step (Order Review).]]></comment>
                        </checkout_subscribe>
                        <map_fields translate="label comment">
                            <label>Customer Fields Mapping</label>
                            <frontend_model>mailchimp/adminhtml_system_config_form_field_mapfields</frontend_model>
                            <backend_model>mailchimp/system_config_backend_mapfield</backend_model>
                            <sort_order>56</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[This is required in order to tell the extensions what customer attributes should be sent to your selected Mailchimp list. <a class="mc-link" target="_blank" href="http://wiki.ebizmarts.com/m4m-merge-fields">See recommended configuration here.</a>]]></comment>
                        </map_fields>
                        <create_fields translate="label comment">
                            <label>Create Merge Fields on Mailchimp</label>
                            <frontend_type>button</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_createMergeFields</frontend_model>
                            <sort_order>57</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Click this button to create the above fields on the selected Mailchimp audience.]]></comment>
                        </create_fields>
                        <create_webhook>
                            <label translate="comment">Create Webhook</label>
                            <frontend_type>button</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_createWebhook</frontend_model>
                            <sort_order>58</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Click this button to create the webhook manually, it will only work if the webhook doesn't already exist.]]></comment>
                        </create_webhook>
                        <webhook_delete translate="label">
                            <label>Webhook Delete Subscriber Action</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_webhookDelete</source_model>
                            <sort_order>59</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </webhook_delete>
                        <webhook_active translate="label comment">
                            <label>Enable Two Way Sync</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <backend_model>mailchimp/system_config_backend_twowaysync</backend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                            <comment><![CDATA[If this is set to "No", the synchronization will be from Magento's newsletter to Mailchimp only.]]></comment>
                        </webhook_active>
                        <magento_mail translate="label comment">
                            <label>Use Magento emails</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>61</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                            <comment><![CDATA[If you enable this Magento confirmation email will be sent for subscribers instead of Mailchimp email.]]></comment>
                        </magento_mail>
                        <enable_log translate="label comment">
                            <label>Enable Log</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_log</source_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment><![CDATA[File is {{base_dir}}/var/log/MailChimp_Errors.log]]></comment>
                        </enable_log>
                        <order_grid translate="label">
                            <label>Display on order grid</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_orderGrid</source_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </order_grid>
                    </fields>
                </general>
                <ecommerce translate="label">
                    <label>Ecommerce</label>
                    <sort_order>200</sort_order>
                    <frontend_type>text</frontend_type>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <backend_model>mailchimp/system_config_backend_ecommerce</backend_model>
                            <sort_order>205</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </active>
                        <customers_optin translate="label">
                            <label>Subscribe all customers to the newsletter</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>208</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </customers_optin>
                        <image_size translate="label">
                            <label>Select product image size to send</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_imageSize</source_model>
                            <sort_order>209</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </image_size>
                        <include_taxes translate="label">
                            <label>Include tax in product price</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_includingTaxes</source_model>
                            <sort_order>210</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>
                                <model>mailchimp/adminhtml_includetaxes_comment</model>
                            </comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </include_taxes>
                        <firstdate translate="label comment">
                            <label>First date</label>
                            <frontend_type>date</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_date</frontend_model>
                            <sort_order>211</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Orders previous than this value, will be ignored.]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </firstdate>
                        <send_promo translate="label">
                            <label>Send Promo Rules and Promo Codes</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>215</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends>
                                <active>1</active>
                            </depends>
                        </send_promo>
                        <resend_ecommerce_data translate="comment">
                            <frontend_type>button</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_resendEcommerceData</frontend_model>
                            <sort_order>230</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[This will not affect the behaviour of Automations.]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </resend_ecommerce_data>
                        <customer_batch_amount translate="label">
                            <label>Customer amount to be sent per run</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_batchLimit</source_model>
                            <sort_order>240</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </customer_batch_amount>
                        <product_batch_amount translate="label">
                            <label>Product amount to be sent per run</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_batchLimit</source_model>
                            <sort_order>250</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </product_batch_amount>
                        <order_batch_amount translate="label">
                            <label>Order amount to be sent per run</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_batchLimit</source_model>
                            <sort_order>260</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment></comment>
                        </order_batch_amount>
                    </fields>
                </ecommerce>
                <abandonedcart>
                    <label>Abandoned Cart</label>
                    <sort_order>500</sort_order>
                    <frontend_type>text</frontend_type>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <active translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </active>
                        <firstdate translate="label">
                            <label>First date</label>
                            <frontend_type>date</frontend_type>
                            <frontend_model>mailchimp/adminhtml_system_config_date</frontend_model>
                            <sort_order>12</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Carts previous than this value, will be ignored]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </firstdate>
                        <page translate="label comment">
                            <label>Redirect page</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_cmspage</source_model>
                            <sort_order>26</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Where to redirect the customer]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </page>
                        <cart_batch_amount translate="label">
                            <label>Cart amount to be sent per run</label>
                            <frontend_type>select</frontend_type>
                            <source_model>mailchimp/system_config_source_batchLimit</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment></comment>
                        </cart_batch_amount>
                    </fields>
                </abandonedcart>
            </groups>
        </mailchimp>
        <mandrill translate="label" module="mailchimp">
            <class>mandrill-section</class>
            <label>Mandrill Configuration</label>
            <header_css>mandrill-header</header_css>
            <tab>mailchimp</tab>
            <sort_order>110</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <hint>
                    <frontend_model>mailchimp/adminhtml_system_config_fieldset_mandrill_hint</frontend_model>
                    <sort_order>0</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                </hint>
                <general translate="label comment">
                    <label>Mandrill integration by ebizmarts</label>
                    <frontend_type>text</frontend_type>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>1</sort_order>
                    <fields>
                        <active translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </active>
                        <apikey translate="label comment">
                            <label>API Key</label>
                            <frontend_type>obscure</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Your API Key]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </apikey>
                        <enable_log translate="label comment">
                            <label>Enable Log</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>File is {{base_dir}}/var/log/Ebizmarts_Mandrill.log</comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </enable_log>
                        <user_info translate="label comment">
                            <label>Account Info</label>
                            <frontend_model>mailchimp/adminhtml_system_config_userinfo</frontend_model>
                            <source_model>mailchimp/system_config_source_userinfo</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[]]></comment>
                            <depends>
                                <active>1</active>
                            </depends>
                        </user_info>
                    </fields>
                </general>
            </groups>
        </mandrill>
    </sections>
</config>
