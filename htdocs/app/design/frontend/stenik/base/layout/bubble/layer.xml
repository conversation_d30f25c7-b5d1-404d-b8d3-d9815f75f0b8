<?xml version="1.0"?>
<layout version="0.1.0">
    <default>
        <reference name="head">
            <action method="addJs"><js>jquery/jquery-3.1.1.min.js</js><group>data-jquery</group></action>
            <action method="addItem" ifconfig="bubble_layer/general/enable_ajax">
                <type>js</type><script>bubble/layer/native.history.js</script>
                <group>data-jquery</group>
            </action>
            <action method="addItem">
                <type>skin_js</type><script>js/bubblelayer.js</script>
                <group>data-jquery</group>
            </action>
            <action method="addItem">
                <type>skin_css</type><name>css/bubble/layer.css</name>
            </action>
        </reference>
        <reference name="before_body_end">
            <block type="bubble_layer/catalog_layer_js" name="bubble.layer.js" template="bubble/layer/catalog/layer/js.phtml" />
        </reference>
    </default>
    <catalog_category_layered>
        <reference name="right">
            <block type="bubble_layer/catalog_layer_view" name="bubble.layer.right" before="-" template="catalog/layer/view.phtml">
                <block type="core/text_list" name="catalog.rightnav.state.renderers" as="state_renderers" />
            </block>
        </reference>
    </catalog_category_layered>
    <catalogsearch_result_index>
        <reference name="right">
            <block type="bubble_layer/catalog_layer_view" name="bubble.layer.right" before="-" template="catalog/layer/view.phtml">
                <block type="core/text_list" name="catalog.rightnav.state.renderers" as="state_renderers" />
            </block>
        </reference>
    </catalogsearch_result_index>
</layout>