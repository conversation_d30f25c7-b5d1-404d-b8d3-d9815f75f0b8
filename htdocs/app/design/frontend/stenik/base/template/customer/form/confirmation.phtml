<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2017 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<h1><?php echo $this->__('Send confirmation link') ?></h1>

<div class="clearH"></div>

<?php echo $this->getMessagesBlock()->toHtml() ?>

<div class="col-xs-6">
    <h4><?php echo $this->__('Retrieve your confirmation link here') ?></h4>
    <p><?php echo $this->__('Please enter your email below and we will send you confirmation link for it.') ?></p>

    <form action="" method="post" id="form-validate">
        <label for="email_address" class="required">* <?php echo $this->__('Email') ?></label>
        <input type="text" name="email" id="email_address" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Email')) ?>" class="input-text required-entry validate-email" value="<?php echo $this->escapeHtml($this->getEmail()) ?>" />
        
        <div class="clearH"></div>

        <button type="submit" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Submit')) ?>" class="button"><?php echo $this->__('Submit') ?></button>

        <div class="clearH2"></div>

        <a href="<?php echo $this->helper('customer')->getLoginUrl() ?>" class="forgotpassword backToLogin"><small>&laquo; </small><?php echo $this->__('Back to Login') ?></a>
    </form>
</div>

<script type="text/javascript">
    var dataForm = new VarienForm('form-validate', true);
</script>
