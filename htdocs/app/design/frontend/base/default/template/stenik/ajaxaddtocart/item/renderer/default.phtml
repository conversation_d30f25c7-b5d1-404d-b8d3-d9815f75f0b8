<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Mage_Checkout_Block_Cart_Item_Renderer
 */
?>
<?php $_item = $this->getItem(); ?>

<div class="ajax-cart-item">
    <div class="cart-img-wrapper">
        <?php if ($this->hasProductUrl()): ?>
            <a href="<?php echo $this->getProductUrl() ?>" title="<?php echo $this->escapeHtml($this->getProductName()) ?>" class="image">
                <img
                    src="<?php echo $this->getProductThumbnail()->resize(120,120) ?>"
                    srcset="<?php echo $this->getProductThumbnail()->resize(285,285) ?> 2x"
                    alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"
                >
            </a>
        <?php else: ?>
            <span class="image">
                <img
                    src="<?php echo $this->getProductThumbnail()->resize(120,120) ?>"
                    srcset="<?php echo $this->getProductThumbnail()->resize(285,285) ?> 2x"
                    alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"
                >
            </span>
        <?php endif ?>
    </div>
    <div class="item-info">
        <?php if ($this->hasProductUrl()):?>
            <a class="product-name" href="<?php echo $this->getProductUrl() ?>">
                <?php echo $this->escapeHtml($this->getProductName()) ?>
            </a>
        <?php else: ?>
            <span class="product-name">
                <?php echo $this->escapeHtml($this->getProductName()) ?>
            </span>
        <?php endif; ?>

        <?php if ($_options = $this->getOptionList()):?>
            <?php foreach ($_options as $_option) : ?>
                <?php $_formatedOptionValue = $this->getFormatedOptionValue($_option) ?>
                <span class="attributes">
                    <?php echo $this->escapeHtml($_option['label']) ?>: <?php echo $_formatedOptionValue['value'] ?>
                </span>
            <?php endforeach; ?>
        <?php endif;?>

        <?php $skuLabel = Mage::getResourceModel('catalog/product')->getAttribute('sku')->getStoreLabel(Mage::app()->getStore()->getName()); ?>
        <span class="attributes"><?php echo $skuLabel ?>: <?php echo $this->escapeHtml($_item->getSku()) ?></span>

        <?php if ($messages = $this->getMessages()): ?>
            <?php foreach ($messages as $message): ?>
                <p class="item-msg <?php echo $message['type'] ?>">* <?php echo $this->escapeHtml($message['text']) ?></p>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php $addInfoBlock = $this->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock): ?>
            <?php echo $addInfoBlock->setItem($_item)->toHtml() ?>
        <?php endif;?>
    </div>
</div>