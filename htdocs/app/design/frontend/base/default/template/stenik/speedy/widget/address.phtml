<?php
/**
 * Address Widget Template
 *
 * @package  Stenik_Speedy
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_Speedy_Block_Widget_Address
 */
?>
<div class="stenik-speedy-address-fields-content address-fields">
    <ul>
        <li style="display: none">
            <input
                type="hidden"
                id="<?php echo $this->getFieldId('update_address') ?>"
                name="<?php echo $this->getFieldName('update_address') ?>"
                value="1"
            />
            <?php
                $street = null;

                if ($this->getAddress()) {
                    $street = $this->getAddress()->getStreetFull();
                    if (is_array($street)) {
                        $street = trim(implode("\n", $street));
                    }
                }
            ?>
            <input
                type="hidden"
                id="<?php echo $this->getFieldId('address_street') ?>"
                name="<?php echo $this->getFieldName('street') ?>[]"
                value="<?php echo $street ?>"
            />
            <input
                type="hidden"
                id="<?php echo $this->getFieldId('postcode') ?>"
                name="<?php echo $this->getFieldName('postcode') ?>"
                value="<?php echo $this->getFieldValue('postcode') ?>"
            />
        </li>

        <li class="wide">
            <input
                type="text"
                id="<?php echo $this->getFieldId('city_name') ?>"
                name="<?php echo $this->getFieldName('city_name') ?>"
                value="<?php echo $this->escapeHtml($this->getFieldValue('city_name')) ?>"
                title="<?php echo $this->__('City') ?> *"
                placeholder="<?php echo $this->__('City') ?> *"
                class="input-text required-entry"
                autocomplete="off"
            />
            <input
                type="hidden"
                id="<?php echo $this->getFieldId('city_id') ?>"
                name="<?php echo $this->getFieldName('city_id') ?>"
                value="<?php echo $this->escapeHtml($this->getFieldValue('city_id')) ?>"
            />
        </li>

        <?php $toOffice = (bool) (int) $this->getFieldValue('to_office'); ?>
        <li class="wide">
            <input
                type="checkbox"
                id="<?php echo $this->getFieldId('to_office') ?>"
                name="<?php echo $this->getFieldName('to_office') ?>"
                value="1"
                <?php if ($toOffice): ?>checked="checked"<?php endif ?>
            />
            <label for="<?php echo $this->getFieldId('to_office') ?>"><?php echo $this->__('Deliver to Speedy Office');?></label>
        </li>

        <li class="wide speedyOfficeFields" <?php if (!$toOffice): ?>style="display: none"<?php endif ?>>
            <div class="field">
                <div class="input-box">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('office_name') ?>"
                        name="<?php echo $this->getFieldName('office_name') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('office_name')) ?>"
                        title="<?php echo $this->__('Office') ?> *"
                        placeholder="<?php echo $this->__('Office') ?> *"
                        class="input-text"
                    />
                    <input
                        type="hidden"
                        id="<?php echo $this->getFieldId('office_id') ?>"
                        name="<?php echo $this->getFieldName('office_id') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('office_id')) ?>"
                    />
                </div>
            </div>
        </li>

        <li class="wide speedyNonOfficeFields" <?php if ($toOffice): ?>style="display: none"<?php endif ?>>
            <div class="field">
                <div class="input-box">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('district_name') ?>"
                        name="<?php echo $this->getFieldName('district_name') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('district_name')) ?>"
                        title="<?php echo $this->__('District') ?> *"
                        placeholder="<?php echo $this->__('District') ?> *"
                        class="input-text"
                    />
                    <input
                        type="hidden"
                        id="<?php echo $this->getFieldId('district_id') ?>"
                        name="<?php echo $this->getFieldName('district_id') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('district_id')) ?>"
                    />
                </div>
            </div>

            <div class="separator">
                <?php echo $this->__('OR');?>
            </div>

            <div class="field">
                <div class="input-box streetName">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('street_name') ?>"
                        name="<?php echo $this->getFieldName('street_name') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('street_name')) ?>"
                        title="<?php echo $this->__('Street') ?> *"
                        placeholder="<?php echo $this->__('Street') ?> *"
                        class="input-text"
                    />
                    <input
                        type="hidden"
                        id="<?php echo $this->getFieldId('street_id') ?>"
                        name="<?php echo $this->getFieldName('street_id') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('street_id')) ?>"
                        title="<?php echo $this->__('Street') ?> *"
                    />
                </div>

                <div class="input-box streetNumber">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('street_number') ?>"
                        name="<?php echo $this->getFieldName('street_number') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('street_number')) ?>"
                        title="<?php echo $this->__('Street Number') ?> *"
                        placeholder="<?php echo $this->__('#') ?> *"
                        class="input-text"
                    />
                </div>
            </div>
        </li>

        <li class="fields wide speedyNonOfficeFields" <?php if ($toOffice): ?>style="display: none"<?php endif ?>>
            <div class="field short">
                <div class="input-box">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('building') ?>"
                        name="<?php echo $this->getFieldName('building') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('building')) ?>"
                        title="<?php echo $this->__('Building') ?> *"
                        placeholder="<?php echo $this->__('Building') ?> *"
                        class="input-text"
                    />
                </div>
            </div>

            <div class="field short">
                <div class="input-box">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('entrance') ?>"
                        name="<?php echo $this->getFieldName('entrance') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('entrance')) ?>"
                        title="<?php echo $this->__('Entrance') ?> *"
                        placeholder="<?php echo $this->__('Entrance') ?> *"
                        class="input-text"
                    />
                </div>
            </div>

            <div class="field short">
                <div class="input-box">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('floor') ?>"
                        name="<?php echo $this->getFieldName('floor') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('floor')) ?>"
                        title="<?php echo $this->__('Floor') ?> *"
                        placeholder="<?php echo $this->__('Floor') ?> *"
                        class="input-text"
                    />
                </div>
            </div>

            <div class="field short">
                <div class="input-box">
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('apartment') ?>"
                        name="<?php echo $this->getFieldName('apartment') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('apartment')) ?>"
                        title="<?php echo $this->__('Apartment') ?> *"
                        placeholder="<?php echo $this->__('Apartment') ?> *"
                        class="input-text"
                    />
                </div>
            </div>
        </li>

        <li class="wide speedyNonOfficeFields">
            <div class="field">
                <div class="input-box">
                    <label for="<?php echo $this->getFieldId('note') ?>"><?php echo $this->__('Note') ?></label>
                    <input
                        type="text"
                        id="<?php echo $this->getFieldId('note') ?>"
                        name="<?php echo $this->getFieldName('note') ?>"
                        value="<?php echo $this->escapeHtml($this->getFieldValue('note')) ?>"
                        title="<?php echo $this->__('Note') ?> *"
                        class="input-text"
                    />
                </div>
            </div>
        </li>

        <li>
            <?php $validationClass = 'speedy_validation_' . md5(uniqid(true)) ?>
            <input
                type="text"
                id="<?php echo $this->getFieldId('speedy_validation') ?>"
                name="<?php echo $this->getFieldName('speedy_validation') ?>"
                value="1"
                style="width: 0; height: 0; position: absolute; overflow: hidden; visibility: hidden;"
                class="<?php echo $validationClass ?>"
            />

            <div class="validation-advice" id="advice-<?php echo $validationClass ?>-<?php echo $this->getFieldId('speedy_validation') ?>" style="display: none">
                <?php echo $this->__('Incomplete address details');?>
            </div>
        </li>

        <li style="display: none">
            <script>
                jQuery(function($) {
                    window.Stenik = window.Stenik || {};
                    Number.isNaN  = Number.isNaN || isNaN; // IE9 Support

                    Stenik.escapeSelector = function(string) {
                        return string.replace(/[!"#$%&'()*+,.\/:;<=>?@\[\\\]\^`{|}~]/g, '\\$&');
                    };

                    var fieldsData = <?php echo json_encode($this->getSpeedyFieldsData()); ?>;

                    for (fieldKey in fieldsData) {
                        fieldsData[fieldKey].selector = '#' + Stenik.escapeSelector(fieldsData[fieldKey]['input_id']);
                    }
                    /*$('[name="<?php echo $this->getCustomerAddressFieldName('street') ?>[]"]').not(fieldsData.address_street.selector).each(function() {
                        var $input = $(this);
                        $([
                            $input.parent(),
                            $input.parent().parent()
                        ]).filter('.wide,.input-box').remove();

                        $input.remove();
                    });*/

                    var updateAddress = function() {
                        var newStreetAddress = '';

                        var toOffice = $(fieldsData.to_office.selector).is(':checked');

                        for (inputKey in fieldsData) {
                            if (toOffice && inputKey != 'office_name') {
                                continue;
                            }

                            if (!toOffice && inputKey == 'office_name') {
                                continue;
                            }


                            var valuePrefix = fieldsData[inputKey]['valuePrefix'];
                            var $input = $(fieldsData[inputKey].selector);
                            var val = $input.val();
                            if (valuePrefix !== null && $input.length > 0 && val != '') {
                                newStreetAddress += valuePrefix + ' ' + val;
                            }

                        }

                        // trim by , or empty space
                        newStreetAddress = newStreetAddress.replace(/(^[,\s]+|[,\s]+$)/gm, '');

                        $(fieldsData.address_street.selector).val(newStreetAddress);
                    };

                    var validateAddress = function() {
                        var $validationInput = $(fieldsData.speedy_validation.selector);
                        var isValid = true;

                        try {
                            if (parseInt($(fieldsData.city_id.selector).val()) == 0) {
                                throw {message: <?php echo Mage::helper('core')->jsonEncode(Mage::helper('stenik_speedy')->__('Please choose city from the dropdown menu.')) ?>};
                            }

                            <?php
                            /*
                                Try one of these combos:

                                * District + Bulding
                                * District + Street No
                                * Street + Street No
                                * Street + Bulding
                                * Office
                                * Note len > 4
                             */

                            ?>

                            var vals = {};
                            for (fieldKey in fieldsData) {
                                vals[fieldKey] = $(fieldsData[fieldKey].selector).val();
                            }

                            var comboValidation = {
                                // District + Bulding
                                district_building: (
                                    vals.district_name != '' && parseInt(vals.district_id) != 0 &&
                                    vals.building != ''
                                ),

                                // District + Street No
                                // district_streetNo: (
                                //     vals.district_name != '' && parseInt(vals.district_id) != 0 &&
                                //     vals.street_number != ''
                                // ),

                                // Street + Street No
                                street_streetNo: (
                                    vals.street_name != '' && parseInt(vals.street_id) != 0 &&
                                    vals.street_number != ''

                                ),

                                // Street + Bulding
                                street_building: (
                                    vals.street_name != '' && parseInt(vals.street_id) != 0 &&
                                    vals.building != ''
                                ),

                                // Office
                                office: (vals.office_name != '' && parseInt(vals.office_id) != 0),

                                // Note len > 4
                                note: (vals.note.length > 4)
                            };

                            validComboExists = false;

                            for (comboKey in comboValidation) {
                                validComboExists = validComboExists || comboValidation[comboKey];
                            }

                            if (!validComboExists) {
                                throw {message: <?php
                                    $combinationTexts = array(
                                        Mage::helper('stenik_speedy')->__('District + Building'),
                                        Mage::helper('stenik_speedy')->__('Street + Street Number'),
                                        Mage::helper('stenik_speedy')->__('Speedy Office'),
                                        Mage::helper('stenik_speedy')->__('Note with %s or more letters', 5),
                                    );

                                    echo Mage::helper('core')->jsonEncode(
                                        Mage::helper('stenik_speedy')->__('Please enter one of the following combinations:') . '<br>' .
                                        '<ul><li>' .
                                        implode('</li><li>', $combinationTexts) .
                                        '</li></ul>'
                                    )
                                ?>};
                            }
                        } catch(e) {

                            var errorMessage = '';
                            if (e.message) {
                                errorMessage = e.message;
                            } else {
                                errorMessage = <?php echo Mage::helper('core')->jsonEncode(Mage::helper('stenik_speedy')->__('Incomplete address details')) ?>;
                            }
                            $('#' + Stenik.escapeSelector('advice-' + <?php echo json_encode($validationClass) ?> + '-' + fieldsData.speedy_validation.input_id)).html(errorMessage);
                            isValid = false;
                        }

                        $validationInput.val(isValid ? '1' : '');
                    };

                    var initAutocompleteCouple = function(
                        $autocompleteInput,
                        $valueInput,
                        sourceUrl,
                        preparePostDataCallback,
                        parseSourceResultCallback,
                        onSelect,
                        minLength
                    ) {
                        if (typeof onSelect === 'undefined') { onSelect = function() {}; }
                        if (typeof minLength === 'undefined') { minLength = 2; }
                        var autocompleteCache = {};
                        $($autocompleteInput).autocomplete({
                            source: function(request, response) {
                                if (request.term in autocompleteCache) {
                                    response(autocompleteCache[request.term]);
                                    return;
                                }

                                $.ajax( {
                                    url: sourceUrl,
                                    dataType: 'json',
                                    data: preparePostDataCallback(request)
                                }).done(function(result) {
                                    if (result.error) {
                                        return response(false);
                                    }

                                    var items = parseSourceResultCallback(result);

                                    autocompleteCache[request.term] = items;
                                    response(items);
                                }).fail(function() {
                                    $valueInput.val('').change();
                                    return response(false);
                                });
                            },
                            minLength: minLength,
                            select: function( event, ui ) {
                                $($valueInput).val(ui.item.id).change();

                                if (onSelect) {
                                    onSelect(event, ui);
                                }
                            }
                        });

                        $($autocompleteInput).keyup(function(e) {
                            if ((e.keyCode || e.which) == 13 /* Enter */) {
                                return;
                            }
                            $($valueInput).val('');
                        });

                        var checkCouple = function() {
                            var valueInputInt = parseInt($($valueInput).val());
                            if (valueInputInt == 0 || Number.isNaN(valueInputInt)) {
                                $($autocompleteInput).val('');
                            }
                        };

                        $($autocompleteInput).change(checkCouple);

                        // Fix for some mobile browsers
                        $autocompleteInput.on('input', function(){
                            $(this).autocomplete("search", $(this).val());
                        });

                        checkCouple();
                    };

                    // Office autocomplete
                    initAutocompleteCouple(
                        $(fieldsData.office_name.selector),
                        $(fieldsData.office_id.selector),
                        <?php echo json_encode($this->getUrl('stenik_speedy/autocomplete/getOffices')) ?>,
                        function(request) { return {
                            q: request.term,
                            city: $(fieldsData.city_id.selector).val()
                        };},
                        function(result) {
                            var items = [];
                            for (var office, item, i = 0; i < result.offices.length; i++) {
                                office = result.offices[i];
                                item = {
                                    office: office,
                                    value: office.name + ', ' + office.street_address,
                                    id: office.id || office.office_id,
                                };
                                item.label = office.name + ', ' + office.street_address;
                                items.push(item);
                            }
                            return items;
                        },
                        function() {},
                        0
                    );

                    // District autocomplete
                    initAutocompleteCouple(
                        $(fieldsData.district_name.selector),
                        $(fieldsData.district_id.selector),
                        <?php echo json_encode($this->getUrl('stenik_speedy/autocomplete/getDistricts')) ?>,
                        function(request) { return {
                            q: request.term,
                            city: $(fieldsData.city_id.selector).val()
                        };},
                        function(result) {
                            var items = [];
                            for (var i = 0; i < result.districts.length; i++) {
                                var district = result.districts[i];
                                var item = {
                                    district: district,
                                    value: district.type + ' ' + district.name,
                                    id: district.id || district.district_id,
                                };
                                item.label = district.type + ' ' + district.name;
                                items.push(item);
                            }
                            return items;
                        },
                        function() {},
                        1
                    );

                    // Street Autocomplete
                    initAutocompleteCouple(
                        $(fieldsData.street_name.selector),
                        $(fieldsData.street_id.selector),
                        <?php echo json_encode($this->getUrl('stenik_speedy/autocomplete/getStreets')) ?>,
                        function(request) { return {
                            q: request.term,
                            city: $(fieldsData.city_id.selector).val()
                        };},
                        function(result) {
                            var items = [];
                            for (var i = 0; i < result.streets.length; i++) {
                                var street = result.streets[i];
                                var item = {
                                    street: street,
                                    value: street.type + ' ' + street.name,
                                    id: street.id || street.street_id,
                                };
                                item.label = street.type + ' ' + street.name;
                                items.push(item);
                            }
                            return items;
                        },
                        function() {},
                        1
                    );

                    // City Autocomplete
                    initAutocompleteCouple(
                        $('#' + Stenik.escapeSelector('<?php echo $this->getFieldId('city_name') ?>')),
                        $(fieldsData.city_id.selector),
                        <?php echo json_encode($this->getUrl('stenik_speedy/autocomplete/getCities')) ?>,
                        function(request) { return {
                            q: request.term,
                            country: $('#' + Stenik.escapeSelector('<?php echo $this->getCustomerAddressFieldId('country_id') ?>')).val()
                        };},
                        function(result) {
                            var items = [];
                            for (var i = 0; i < result.cities.length; i++) {
                                var city = result.cities[i];
                                var item = {
                                    city: city,
                                    value: city.name,
                                    id: city.city_id,
                                };
                                item.label = city.type + ' ' + city.name + ' (' + city.region_name + ', ' + city.area_name + ')';

                                // trim dots, commas and white spaces
                                item.label = item.label.replace(/(^[,\.\s]+|[,\.\s]+$)/gm, '');
                                items.push(item);
                            }
                            return items;
                        },
                        function(event, ui) {
                            $('#' + Stenik.escapeSelector('<?php echo $this->getFieldId('postcode') ?>')).val(ui.item.city.postcode);
                            // $('#' + Stenik.escapeSelector('<?php echo $this->getCustomerAddressFieldId('zip') ?>')).val(ui.item.city.postcode);
                            // $('#' + Stenik.escapeSelector('<?php echo $this->getCustomerAddressFieldId('city') ?>')).change();
                        },
                        2
                    );

                    $('#' + Stenik.escapeSelector('<?php echo $this->getFieldId('city_name') ?>')).change(function() {
                        for (inputKey in fieldsData) {

                            if (inputKey == 'city_id') continue;
                            if (inputKey == 'note') continue;

                            var $input = $(fieldsData[inputKey].selector);
                            if ($input.is('[type="checkbox"]')) {
                                if (inputKey != 'to_office') {
                                    $input.prop('checked', false);
                                    $input.attr('checked', false);
                                }
                            } else {
                                $input.val('');
                            }
                        }

                        updateAddress();
                        validateAddress();
                    });

                    $(fieldsData.to_office.selector).change(function() {
                        if ($(this).is(':checked')) {
                            $('.speedyOfficeFields').show();
                            $('.speedyNonOfficeFields').hide();
                        } else {
                            $('.speedyOfficeFields').hide();
                            $('.speedyNonOfficeFields').show();
                        }
                    });

                    for (inputKey in fieldsData) {
                        $(fieldsData[inputKey].selector).change(validateAddress);
                        $(fieldsData[inputKey].selector).change(updateAddress);
                    }

                    updateAddress();
                    validateAddress();

                    Validation.addAllThese([
                        [<?php echo json_encode($validationClass) ?>,  <?php echo json_encode($this->__('Incomplete address details'));?>, function(v) {
                            validateAddress();
                            return (jQuery(fieldsData.speedy_validation.selector).val() == '1');
                        }]]
                    );
                });
            </script>
        </li>
    </ul>
</div>