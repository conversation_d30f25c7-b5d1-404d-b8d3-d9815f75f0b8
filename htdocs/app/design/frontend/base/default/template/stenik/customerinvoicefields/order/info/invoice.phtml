<?php
/**
 * @package Stenik_CustomerInvoiceFields
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php $order = $this->getOrder() ?>

<?php if ($order && $order->getCustomerInvoice()): ?>
    <div class="order-info-box order-invoice-info-box">
        <div class="box">
            <div class="box-title">
                <h2><?php echo Mage::helper('stenik_customerinvoicefields')->__('Invoice') ?></h2>
            </div>

        </div>
        <div class="box-content">
            <table cellspacing="0">
                <?php if ($order->getCustomerInvoiceType() == Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_PERSONAL): ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal Name') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_personal_name')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal PIN') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_personal_pin')) ?></strong></td>
                    </tr>
                    <?php if (0): ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal City') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_personal_city')) ?></strong></td>
                    </tr>
                    <?php endif ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal Address') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_personal_addr')) ?></strong></td>
                    </tr>
                <?php else: # Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_COMPANY ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company Name') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_company_name')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company PIC') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_company_pic')) ?></strong></td>
                    </tr>
                    <?php if (0): ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company City') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_company_city')) ?></strong></td>
                    </tr>
                    <?php endif ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company Addr') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_company_addr')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company URN') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_company_urn')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company VAT') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($order->getData('customer_invoice_company_vat')) ?></strong></td>
                    </tr>
                <?php endif ?>
            </table>

            <?php echo $this->getChildHtml() ?>
        </div>
    </div>
<?php endif ?>