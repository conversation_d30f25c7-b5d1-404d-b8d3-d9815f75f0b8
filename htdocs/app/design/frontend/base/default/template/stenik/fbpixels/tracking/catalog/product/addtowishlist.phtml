<?php
    $helper        = $this->helper('stenik_fbpixels');
    $productHelper = $this->helper('stenik_fbpixels/product');

    if($helper->isEventHidden('add_to_wishlist'))
        return;

    if(!$btnClass = $helper->getAddToWishListButtonClass())
        return;

    if(!$_product = $this->getProduct())
        return;

/**
 * Other Parameters
 *     value, currency, content_name, content_category, content_ids
 */
?>
<script type="text/javascript">
var productData = {
        ids:'<?php echo $_product->getSku() ?>',
        value:'<?php echo $_product->getFinalPrice() ?>',
        currency:'<?php echo Mage::app()->getStore()->getCurrentCurrencyCode() ?>',
        name:'<?php echo $_product->getName() ?>',
        categories:'<?php if($categoryPath = $productHelper->getCategoryPathNames($_product)): ?><?php echo $categoryPath ?><?php endif; ?>'
};

document.observe("dom:loaded", function() {
    $$('.<?php echo $btnClass; ?>').invoke('observe', 'click', function() {
        sFbTrackAddToWishList(productData);
    });
});
</script>