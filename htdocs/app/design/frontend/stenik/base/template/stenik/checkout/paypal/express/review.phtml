<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/** @var $this Mage_Paypal_Block_Express_Review */
?>

<?php
    $this->setCanEditShippingAddress(false);
    $this->setCanEditShippingMethod(false);
?>

<h1><?php echo $this->__('Review Order') ?></h1>

<?php echo $this->getMessagesBlock()->toHtml() ?>

<?php if ($this->getShippingAddress()): ?>
    <div class="row info-set">
        <div class="col-xs-12">
        </div>
        <div class="col-xs-6">
            <div class="box">
                <div class="box-title">
                    <h4><?php echo $this->__('Shipping Address') ?><span class="separator"><?php if ($this->getCanEditShippingAddress()):?> | </span><a href="<?php echo $this->getEditUrl() ?>"><?php echo $this->__('Edit') ?></a><?php endif;?></h4>
                </div>
                <div class="box-content">
                    <address><?php echo $this->renderAddress($this->getShippingAddress())?></address>
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="box">
                <div class="box-title">
                    <h4><?php echo $this->__('Shipping Method') ?></h4>
                </div>
                <div class="box-content">
                    <form method="post" id="shipping_method_form" action="<?php echo $this->escapeHtml($this->getShippingMethodSubmitUrl()) ?>">
                        <?php if ($this->getCanEditShippingMethod() || !$this->getCurrentShippingRate()):?>
                            <?php if ($groups = $this->getShippingRateGroups()):?>
                                <?php $currentRate = $this->getCurrentShippingRate(); ?>
                                <fieldset>
                                    <select name="shipping_method" id="shipping_method" style="width:250px;" class="required-entry" disabled>
                                        <?php if (!$currentRate):?>
                                            <option value=""><?php echo $this->__('Please select a shipping method...') ?></option>
                                        <?php endif;?>
                                        <?php foreach ($groups as $code => $rates):?>
                                            <optgroup label="<?php echo $this->escapeHtml($this->getCarrierName($code)) ?>" style="font-style:normal;">
                                                <?php foreach ($rates as $rate):?>
                                                    <option value="<?php echo $this->renderShippingRateValue($rate)?>"<?php echo ($currentRate === $rate) ? ' selected="selected"' : '' ;?>>
                                                        <?php echo $this->renderShippingRateOption($rate)?>
                                                    </option>
                                                <?php endforeach;?>
                                            </optgroup>
                                        <?php endforeach;?>
                                    </select>
                                </fieldset>
                            <?php else: ?>
                                <p><strong><?php echo $this->__('Sorry, no quotes are available for this order at this time.') ?></strong></p>
                            <?php endif;?>
                        <?php else: ?>
                            <p><strong><?php echo $this->renderShippingRateOption($this->getCurrentShippingRate())?></strong></p>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="clearH"></div>
<?php endif; ?>

<div class="row info-set">
    <div class="col-xs-12">
        <div class="box">
            <div class="box-title">
                <h4><?php echo $this->__('Payment Method') ?></h4>
            </div>
            <div class="box-content">
                <?php echo $this->escapeHtml($this->getPaymentMethodTitle()) ?>
            </div>
        </div>
    </div>
</div>

<form method="post" id="order_review_form" action="<?php echo $this->getPlaceOrderUrl() ?>">

    <div class="row info-set">
        <div class="col-xs-12">
            <h6 class="legend"><?php echo $this->__('Items in Your Shopping Cart') ?></h6>
            <div id="details-reload">
                <?php echo $this->getChildHtml('details') ?>
                <?php echo $this->getChildHtml('checkout.onepage.section.comment.agreements'); ?>
            </div>
        </div>
    </div>
    <div class="paypal-review-placeorderbtn-wrapper">
        <div class="buttons-set buttons-set-order" id="review-buttons-container">
            <button type="button" id="review_button" value="<?php echo Mage::helper('core')->quoteEscape($this->__('Place Order')) ?>" class="button btn-checkout">
                <?php echo $this->__('Place Order') ?>
            </button>
            <span class="please-wait" id="review-please-wait" style="display:none;">
                <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif') ?>" alt="<?php echo Mage::helper('core')->quoteEscape($this->__('Submitting order information...')) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Submitting order information...')) ?>" class="v-middle" /> <?php echo $this->__('Submitting order information...') ?>
            </span>
        </div>
    </div>
    <a href="<?php echo $this->getUrl('checkout/cart') ?>" class="edit-shopping-cart"><?php echo $this->__('Edit Shopping Cart') ?></a>
    <div class="clearH2"></div>
</form>

<script>
    <?php if ($this->getUseAjax()):?>
        OrderReviewController.prototype._submitOrder = function() {
            if (this._canSubmitOrder) {
                if (this._pleaseWait) {
                    this._pleaseWait.show();
                }
                new Ajax.Request(this.form.action, {
                    parameters: {isAjax: 1, method: 'POST'},
                    onSuccess: function(transport) {
                        var response = transport.responseJSON || transport.responseText.evalJSON(true) || {};
                        if (response.redirect) {
                            setLocation(response.redirect);
                            return;
                        }
                        if (response.success) {
                            setLocation('<?php echo $this->getSuccessUrl()?>');
                            return;
                        } else {
                            var msg = response.error_messages;
                            if (Object.isArray(msg)) {
                                msg = msg.join("\n").stripTags().toString();
                            }
                            if (msg) {
                                $('review-please-wait').hide();
                                alert(msg.stripTags().toString());
                                return;
                            }
                        }
                        $('review-please-wait').hide();
                        alert('<?php echo $this->jsQuoteEscape($this->__('Unknown Error. Please try again later.')); ?>');
                        return;
                    },
                    onFailure: function(){
                        alert('<?php echo $this->jsQuoteEscape($this->__('Server Error. Please try again.')) ?>');
                        $('review-please-wait').hide();
                    }
                });
            }
        }
    <?php endif ?>

    PayPalExpressAjax = new OrderReviewController($('order_review_form'), $('review_button'),
        $('shipping_method'), $('shipping_method_form'), 'details-reload'
    );
    PayPalExpressAjax.addPleaseWait($('review-please-wait'));
</script>
