<?php
/**
* @category  Apptrian
* @package   Apptrian_FacebookPixel
* <AUTHOR>
* @copyright Copyright (c) Apptrian (http://www.apptrian.com)
* @license   http://www.apptrian.com/license Proprietary Software License EULA
*/

/**
* Facebook Pixel Code block
*
* @var $this Apptrian_FacebookPixel_Block_Code
*/

$data = $this->getFacebookPixelData();
$idData = $data['id_data'];
$action = $data['full_action_name'];
$pageHandles = $data['page_handles'];
$pageHandlesCategory = $data['page_handles_category'];
$pageHandlesProduct = $data['page_handles_product'];
$pageHandlesQuote = $data['page_handles_quote'];
$pageHandlesOrder = $data['page_handles_order'];
$pageHandlesSearch = $data['page_handles_search'];

$isPixelEnabled = $this->isPixelEnabled();
$isApiEnabled = $this->isApiEnabled();
$isBaseCodeEnabled = $this->isBaseCodeEnabled();
$firingMode = $this->getFiringMode();

$isPageViewEnabled = $this->isEventEnabled('PageView');
$isPageViewWithAll = $this->isPageViewWithAll();
$isPageViewWithAllApi = $this->isPageViewWithAll(true);

$isMoveParamsOutsideContentsEnabled = $this->isMoveParamsOutsideContentsEnabled();

$isDataProcessingEnabled = $this->isDataProcessingEnabled();
$dpo = $this->getDpo();
$dpoCountry = $this->getDpoCountry();
$dpoState = $this->getDpoState();

// bof Check if FB Pixel is allowed
if (in_array($action, $pageHandles) && ($isPixelEnabled || $isApiEnabled)): ?>
<script>
  jQuery('document').ready(function () {
    jQuery.ajax({
      url: '<?php echo Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_WEB) ?>apptrian_facebookpixel/index/matching',
      data: {
        sections: "apptrian_facebook_pixel_matching_section"
      },
      type: "GET",
      dataType: "json"
    }).done(function (json) {
      const result = json;
    });
  });
</script>

<!-- Facebook Pixel Code -->
<script>
<?php if ($isBaseCodeEnabled): ?>
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.agent='dvapptrian';n.queue=[];
t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');
<?php endif; ?>
(function ($, window, document) {
  $(document).on('ajaxComplete', function (event, xhr, settings) {
    var userData = {};
      <?php /* @noEscape */ echo $this->getUrlMarker() ?>
      <?php /* @noEscape */ echo $this->getCategoryIdMarker() ?>
      <?php /* @noEscape */ echo $this->getProductIdMarker() ?>
      <?php /* @noEscape */ echo $this->getSearchMarker() ?>

    function isEmpty(obj) {
      for (var prop in obj) {
        if (obj.hasOwnProperty(prop)) {
          return false;
        }
      }

      return true;
    }

    var sectionUrl = '<?php echo Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_WEB) ?>apptrian_facebookpixel/index/matching'
      + '?sections=apptrian_facebook_pixel_matching_section';

    if (settings.url === sectionUrl) {
      var response = JSON.parse(xhr.responseText);
      var section;
      var sectionData;

      if (response !== 'undefined') {
        userData = response;
      }

        <?php if ($isBaseCodeEnabled): ?>
        <?php if ($isDataProcessingEnabled): ?>
      fbq(
        'dataProcessingOptions',
          <?php /* @noEscape */ echo json_encode($dpo, JSON_PRETTY_PRINT) ?>,
          <?php echo $this->escapeHtml($dpoCountry) ?>,
          <?php echo $this->escapeHtml($dpoState) ?>
      );
        <?php endif; ?>

        <?php foreach ($idData as $id): ?>
      if (!isEmpty(userData)) {
        fbq('init', '<?php echo $this->escapeHtml($id) ?>', userData);
      } else {
        fbq('init', '<?php echo $this->escapeHtml($id) ?>');
      }
        <?php endforeach ?>
        <?php endif; ?>

      function stringToHash(string) {
        var hash = 0;

        if (string.length == 0) return hash;

        for (i = 0; i < string.length; i++) {
          char = string.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash;
        }

        return String(hash);
      }

      function generateEventId(eName) {
        var uCookie = document.cookie;
        var uHash = stringToHash(uCookie);
        var url = window.location.href;
        var urlHash = stringToHash(url);

        function getTimeStamp() {
          if (!Date.now) {
            Date.now = function () {
              return new Date().getTime();
            }
          }

          return Date.now();
        }

        var timestamp = String(getTimeStamp());

        return eName + uHash + urlHash + timestamp;
      }

      function fireConversionsApiEvent(eName, eData, eId) {
        var apiUrl = '<?php echo Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_WEB) ?>apptrian_facebookpixel/index/index';

        $.ajax({
          url: apiUrl,
          data: {
            eventName: eName,
            eventData: eData,
            eventId: eId,
            url: window.location.href,
            userData: userData
          },
          type: "POST",
          dataType: "json",
        })
          .done(function (json) {
            var result = json;
          });
      }


      function moveParamsOutsideContents(data) {
          <?php if ($isMoveParamsOutsideContentsEnabled): ?>
        var isMoveParamsOutsideContentsEnabled = 1;
          <?php else: ?>
        var isMoveParamsOutsideContentsEnabled = 0;
          <?php endif; ?>

        if (isMoveParamsOutsideContentsEnabled) {
          if (!('contents' in data)) {
            return data;
          }

          var contents = data['contents'];
          var contentsLength = contents.length;

          if (contentsLength > 1) {
            var c = 0;
            for (i = 0; i < contentsLength; i++) {
              var item = contents[i];

              for (var index in item) {
                if (index == 'id' || index == 'item_price' || index == 'quantity') {
                  continue;
                }

                // You cannot do anything with param just
                // Remove the param from contents
                delete data['contents'][c][index];
              }

              c++;
            }
          } else {
            var item = contents[0];
            for (var index in item) {
              var value = item[index];
              if (index == 'id' || index == 'item_price' || index == 'quantity') {
                continue;
              }

              // Set the param
              data[index] = value;
              // Remove the param from contents
              delete data['contents'][0][index];
            }
          }

          return data;
        } else {
          return data;
        }
      }

      // Ready PageView data. (It is fired below for each event conditionally.)
      var pageViewEventId = generateEventId("PageView");
      var pageViewEventIdObj = {};
      pageViewEventIdObj.eventID = pageViewEventId;

      var pageViewData = {};

        <?php
        // bof category code
        if (in_array($action, $pageHandlesCategory)): ?>
        <?php if ($isPageViewEnabled && $isBaseCodeEnabled && $isPageViewWithAll): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($isPageViewEnabled && $isPageViewWithAllApi): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php
        $categoryData = $this->getCategoryData();
        if (!empty($categoryData)): ?>
        <?php
        $categoryEventName = $categoryData['event_name'];
        $categoryEventData = $categoryData['data'];

        $categoryEventNameJson = json_encode($categoryEventName, JSON_PRETTY_PRINT);
        $categoryEventDataJson = json_encode($categoryEventData, JSON_FORCE_OBJECT);
        ?>

      var categoryEventName = <?php /* @noEscape */ echo $categoryEventNameJson ?>;
      var categoryEventData = <?php /* @noEscape */ echo $categoryEventDataJson ?>;
      var categoryEventId = generateEventId(categoryEventName);
      var categoryEventIdObj = {};
      categoryEventIdObj.eventID = categoryEventId;

        <?php if ($this->isEventEnabled('ViewContent') && $isBaseCodeEnabled): ?>
      fbq("track", categoryEventName, categoryEventData, categoryEventIdObj);
        <?php endif; ?>

        <?php if ($this->isApiEventEnabled('ViewContent')): ?>
      fireConversionsApiEvent(categoryEventName, categoryEventData, categoryEventId);
        <?php endif; ?>

        <?php endif; ?>
        <?php
        // eof category code
        // bof product code
      elseif (in_array($action, $pageHandlesProduct)): ?>
        <?php if ($isPageViewEnabled && $isBaseCodeEnabled && $isPageViewWithAll): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($isPageViewEnabled && $isPageViewWithAllApi): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php
        $productData = $this->getProductData();
        // bof product data
        if (!empty($productData)): ?>
        <?php

        $productEventData = $productData['data'];
        $productEventDataWithContentIds = $productData['data_with_content_ids'];
        $contentsWithIds = $productData['contents_with_ids'];
        $bundleProductOptionsData = $productData['bundle_product_options_data'];
        $configurableProductOptionsData = $productData['configurable_product_options_data'];
        $productId = $productData['product_id'];
        $productType = $productData['product_type'];
        $isDetectSelectedSkuEnabled = $this->isDetectSelectedSkuEnabled($productType);

        if (empty($productEventDataWithContentIds)) {
            $productEventDataForViewContent = $productEventData;
        } else {
            $productEventDataForViewContent = $productEventDataWithContentIds;
        }

        $productEventDataJson = json_encode($productEventData, JSON_PRETTY_PRINT);
        $productEventDataForViewContentJson = json_encode($productEventDataForViewContent, JSON_PRETTY_PRINT);
        $contentsWithIdsJson = json_encode($contentsWithIds, JSON_PRETTY_PRINT);
        $bundleProductOptionsDataJson = json_encode($bundleProductOptionsData, JSON_PRETTY_PRINT);
        $configurableProductOptionsDataJson = json_encode($configurableProductOptionsData, JSON_PRETTY_PRINT);

        ?>

      var productData = <?php /* @noEscape */ echo $productEventDataJson ?>;
      var productDataForViewContent = <?php /* @noEscape */ echo $productEventDataForViewContentJson ?>;
      var isDetectSelectedSkuEnabled = 0;
      var taxFlag = <?php /* @noEscape */ echo $this->getDisplayTaxFlag() ?>;

        <?php if ($isDetectSelectedSkuEnabled): ?>
      // This is used to check whether to execute updateProductData functions
      isDetectSelectedSkuEnabled = 1;

        <?php if ($productType == 'bundle'): ?>
      // bof Detect product SKUs (bundle)

      var contentsWithIds = <?php /* @noEscape */ echo $contentsWithIdsJson ?>;
      var bundleProductOptionsData = <?php /* @noEscape */ echo $bundleProductOptionsDataJson ?>;
      var selectors = {};

      // bof option loop
      for (let optionId in bundleProductOptionsData) {
        // Set all quantity input selectors
        var nameAttributeValue = 'bundle_option_qty[' + optionId + ']';
        selectors['qty' + optionId] = $('input[name="' + nameAttributeValue + '"]');

        // bof Quantity input handler
        selectors['qty' + optionId].on('change keyup paste click', function () {
          var idAttr = $(this).attr('id');
          var oId = idAttr.replace(/[^0-9]/g, '');
          var pId = 0;
          var typeAttr = '';
          var sIds = 0;
          var nameAttrValue = '';
          var valueAttr = '';

          // Set qty of all sibling selections
          var siblings = bundleProductOptionsData[oId];
          for (let sId in siblings) {
            pId = siblings[sId]['product_id'];

            if ($('#bundle-option-' + oId + '-' + sId).length) {
              // bof radio (checkbox does not have qty input)
              typeAttr = selectors['opt' + oId + '_' + sId].attr('type');

              if (typeAttr == 'radio') {
                if (selectors['opt' + oId + '_' + sId].is(':checked')) {
                  // Set qty of product to current qty value
                  contentsWithIds[pId].quantity = formatQty($(this).val());
                }
              }

              if (typeAttr == 'hidden') {
                // Case when there is only one radio option
                // Set qty of product to current qty value
                contentsWithIds[pId].quantity = formatQty($(this).val());
              }
              // eof radio (checkbox does not have qty input)
            } else if ($('#bundle-option-' + oId).length) {
              // bof dropdown (multiselect does not have qty input)
              sIds = selectors['opt' + oId + '_' + sId].parent().val();

              if (!Array.isArray(sIds) && sIds == sId) {
                // Set qty of product to current qty value
                contentsWithIds[pId].quantity = formatQty($(this).val());
              }
              // eof dropdown (multiselect does not have qty input)
            } else {
              // Edge case when there is only one option in dropdown
              // There is no id only name="bundle_option[X]" value="Y" type="hidden"
              nameAttrValue = 'bundle_option[' + optionId + ']';
              selectors['opt' + oId + '_' + sId] = $('input[name="' + nameAttrValue + '"]');

              typeAttr = selectors['opt' + oId + '_' + sId].attr('type');

              if (typeAttr == 'hidden') {
                valueAttr = selectors['opt' + oId + '_' + sId].attr('value');
                if (valueAttr == sId) {
                  // Set qty of product to current qty value
                  contentsWithIds[pId].quantity = formatQty($(this).val());
                }
              }
            }
          }
        });
        // eof Quantity input handler

        // bof selection loop
        for (let selectionId in bundleProductOptionsData[optionId]) {
          // Get product ID
          var pId = bundleProductOptionsData[optionId][selectionId]['product_id'];

          // Initialize default product qty var
          var pQty = 0;

          // Set qty to 0
          contentsWithIds[pId].quantity = 0;

          if ($('#bundle-option-' + optionId + '-' + selectionId).length) {
            // bof checkbox and radio

            var idAttributeValue = 'bundle-option-' + optionId + '-' + selectionId;
            selectors['opt' + optionId + '_' + selectionId] = $('#' + idAttributeValue);

            var typeAttr = selectors['opt' + optionId + '_' + selectionId].attr('type');

            // bof If anything is pre-checked
            if (selectors['opt' + optionId + '_' + selectionId].is(':checked')) {
              // Case when there is qty input
              if (selectors['qty' + optionId].length) {
                // Set qty of product to current qty value
                contentsWithIds[pId].quantity = formatQty(selectors['qty' + optionId].val());
              } else {
                // Case when there is no qty input
                // Get default quantity
                pQty = bundleProductOptionsData[optionId][selectionId]['product_quantity'];
                // Set qty of product to defualt quantity
                contentsWithIds[pId].quantity = pQty;
              }
            }
            // eof If anything is pre-checked

            // bof Edge case when there is only one radio
            if (typeAttr == 'hidden') {
              // Edge case only one radio option
              // Case when there is qty input
              if (selectors['qty' + optionId].length) {
                // Set qty of product to current qty value
                contentsWithIds[pId].quantity = formatQty(selectors['qty' + optionId].val());
              } else {
                // Case when there is no qty input
                // Get default quantity
                pQty = bundleProductOptionsData[optionId][selectionId]['product_quantity'];
                // Set qty of product to defualt quantity
                contentsWithIds[pId].quantity = pQty;
              }
            }
            // eof Edge case when there is only one radio

            // bof Edge case when there is 'none' radio option
            if (typeAttr == 'radio') {
              if ($('#bundle-option-' + optionId).length) {
                var oProperty = 'opt' + optionId + '_none';

                if (!selectors.hasOwnProperty(oProperty)) {
                  // Set this 'none' radio option only if it is not set
                  selectors[oProperty] = $('#bundle-option-' + optionId);
                }

                // The 'none' radio option handler
                selectors[oProperty].on('change, click', function () {
                  var idAttr = $(this).attr('id');
                  var oId = idAttr.replace(/[^0-9]/g, '');

                  if ($(this).is(':checked')) {
                    // Set qty of all siblings to 0
                    var spId = 0;
                    var siblings = bundleProductOptionsData[oId];
                    for (let s in siblings) {
                      spId = siblings[s]['product_id'];
                      contentsWithIds[spId].quantity = 0;
                    }
                  }
                });
              }
            }
            // eof Edge case when there is 'none' radio option

            // bof checkbox and radio handler
            selectors['opt' + optionId + '_' + selectionId].on('change, click', function () {

              var idAttr = $(this).attr('id');
              var typeAttr = $(this).attr('type');
              var oId = 0;
              var sId = 0;
              var pId = 0;

              // Get option IDs from id attribute
              var optionAndSelectionIds = (idAttr.match(/\d+\.\d+|\d+\b|\d+(?=\w)/g) || []).map(
                function (v) {
                  return +v;
                }
              );

              // If there are 2 ids then you can detect product ID
              if (optionAndSelectionIds.length === 2) {
                oId = optionAndSelectionIds[0];
                sId = optionAndSelectionIds[1];

                pId = bundleProductOptionsData[oId][sId]['product_id'];

                // If it is checkbox
                if (typeAttr == 'checkbox') {
                  // If it is checked
                  if ($(this).is(':checked')) {
                    // Get default quantity
                    var pQty = bundleProductOptionsData[oId][sId]['product_quantity'];
                    // Set qty of product to defualt quantity
                    contentsWithIds[pId].quantity = pQty;
                  }
                  // If it is not checked
                  if (!$(this).is(':checked')) {
                    // Set qty of product to 0
                    contentsWithIds[pId].quantity = 0;
                  }
                }

                // If it is radio
                if (typeAttr == 'radio') {
                  if ($(this).is(':checked')) {
                    // Set qty of all siblings to 0
                    var spId = 0;
                    var siblings = bundleProductOptionsData[oId];
                    for (let s in siblings) {
                      spId = siblings[s]['product_id'];
                      contentsWithIds[spId].quantity = 0;
                    }

                    // Set qty of product to current qty value
                    contentsWithIds[pId].quantity = formatQty(selectors['qty' + oId].val());

                    // bof Fix for radio not reading correct qty value
                    setTimeout(function () {
                      // Set qty of product to current qty value
                      contentsWithIds[pId].quantity = formatQty(selectors['qty' + oId].val());
                    }, 300);
                    // eof Fix for radio not reading correct qty value
                  }
                }
              }
            });
            // eof checkbox and radio handler

            // eof checkbox and radio
          } else if ($('#bundle-option-' + optionId).length) {
            // bof dropdown and multiselect

            var idAttribute = 'bundle-option-' + optionId;
            selectors['opt' + optionId + '_' + selectionId] = $(
              '#' + idAttribute + ' option[value=' + selectionId + ']'
            );

            // bof If anything is pre-selected
            if (selectors['opt' + optionId + '_' + selectionId].is(':selected')) {
              // Case when there is qty input
              if (selectors['qty' + optionId].length) {
                // Set qty of product to current qty value
                contentsWithIds[pId].quantity = formatQty(selectors['qty' + optionId].val());
              } else {
                // Case when there is no qty input
                pQty = bundleProductOptionsData[optionId][selectionId]['product_quantity'];
                // Set qty of product to defualt quantity
                contentsWithIds[pId].quantity = pQty;
              }
            }
            // eof If anything is pre-selected

            // bof dropdown and multiselect handler
            selectors['opt' + optionId + '_' + selectionId].parent().on(
              'change, click', function () {

                var idAttr = $(this).attr('id');
                var oId = idAttr.replace(/[^0-9]/g, '');
                var sId = $(this).val();
                var pId = 0;
                var pQty = 0;
                var multipleAttr = '';

                // Set qty of all siblings to 0
                var siblings = bundleProductOptionsData[oId];
                var spId = 0;

                for (let s in siblings) {
                  spId = siblings[s]['product_id'];
                  contentsWithIds[spId].quantity = 0;
                }

                if (Array.isArray(sId)) {
                  // There is a bug when using for let loop
                  // sId sometimes gets additional indexes
                  // Use simple for loop with length and index
                  // (Happens with multiselect)
                  for (var i = 0; i < sId.length; i++) {
                    var selId = sId[i];

                    // If both exist then set product selection
                    if (oId && selId) {
                      // Get product Id
                      pId = bundleProductOptionsData[oId][selId]['product_id'];
                      // Get default quantity
                      pQty = bundleProductOptionsData[oId][selId]['product_quantity'];
                      // Set qty of product to defualt quantity
                      contentsWithIds[pId].quantity = pQty;
                    } else {
                      // Edge case if there is 'none' option for multiselect
                      // Set qty of all siblings to 0
                      for (let s in siblings) {
                        spId = siblings[s]['product_id'];
                        contentsWithIds[spId].quantity = 0;
                      }

                      multipleAttr = $(this).attr('multiple');

                      if (multipleAttr != 'multiple') {
                        // Break out of the for loop
                        // (No need to look at other selections because 'none' is selected.)
                        break;
                      }
                    }
                  }
                } else {
                  // If both exist then set product selection
                  if (oId && sId) {
                    // Get product Id
                    pId = bundleProductOptionsData[oId][sId]['product_id'];
                    // Set qty of product to current qty value
                    contentsWithIds[pId].quantity = formatQty(selectors['qty' + oId].val());
                  } else {
                    // Edge case if there is 'none' option for dropdown
                    // Set qty of all siblings to 0
                    for (let s in siblings) {
                      spId = siblings[s]['product_id'];
                      contentsWithIds[spId].quantity = 0;
                    }
                  }
                }
              });
            // eof dropdown and multiselect handler

            // eof dropdown and multiselect
          } else {
            // Edge case when checkbox and multiselect have only one option
            // Checkbox - There is no id only name="bundle_option[X]" value="Y" type="hidden"
            // Multiselect - There is no id only name="bundle_option[X]" value="Y" type="hidden"
            // To unify the code use name attribute selector
            var nameAttributeValue = 'bundle_option[' + optionId + ']';
            selectors['opt' + optionId + '_' + selectionId] = $('input[name="' + nameAttributeValue + '"]');

            var typeAttr = selectors['opt' + optionId + '_' + selectionId].attr('type');

            if (typeAttr == 'hidden') {
              var valueAttr = selectors['opt' + optionId + '_' + selectionId].attr('value');
              if (valueAttr == selectionId) {
                // Get default quantity
                pQty = bundleProductOptionsData[optionId][selectionId]['product_quantity'];
                // Set qty of product to defualt quantity
                contentsWithIds[pId].quantity = pQty;
              }
            }
          }
        }
        // eof selection loop
      }
      // eof option loop

      // bof Update product data based on selected SKUs (bundle)
      function updateProductData() {
        if ('contents' in productData) {
          // Reset contents
          productData.contents = [];

          // Set value to 0
          productData.value = 0;

          var qty = 0;
          var newValue = 0;
          var itemValue = 0;
          var itemTotal = 0;
          var globalQty = formatQty($('#qty').val());

          for (let productId in contentsWithIds) {
            qty = contentsWithIds[productId].quantity;
            if (qty > 0) {
              // Adjust qty with global qty
              if (globalQty > 1) {
                qty = qty * globalQty;
                contentsWithIds[productId].quantity = qty;
              }

              // Set the item in the contents
              productData.contents.push(contentsWithIds[productId]);

              // Increase value based on qty and price
              itemValue = contentsWithIds[productId].item_price;
              itemTotal = itemValue * qty;
              // Make sure it is 2 decimal float
              newValue += parseFloat(parseFloat(itemTotal).toFixed(2));
            }
          }

          // Set value to new value
          productData.value = parseFloat(parseFloat(newValue).toFixed(2));
        }

        // Make sure content_type is set to product
        // Dedection will return product not product_group
        if ('content_type' in productData) {
          productData.content_type = 'product';
        }
      }

      // eof Update product data based on selected SKUs (bundle)

      // eof Detect product SKUs (bundle)
        <?php elseif ($productType == 'configurable'): ?>
      // bof Detect product SKU (configurable)

      var selectedPrice = 0;
      var selectedQty = 0;
      var selectedSku = '';
      var selectedSkuData = null;
      var contentsWithIds = <?php /* @noEscape */ echo $contentsWithIdsJson ?>;
      var configurableProductId = <?php /* @noEscape */ echo $productId ?>;
      var $price = $('#product-price-' + configurableProductId);
      var $priceInclTax = $('#price-including-tax-' + configurableProductId);

      // bof Detect selected SKU
      $('#product-options-wrapper .swatch-link').on('click', function () {
        selectedProduct();
      });

      function selectedProduct() {
        var foundIds = [];
        $.each(spConfig.settings, function (selectIndex, select) {
          var attributeId = select.id.replace('attribute', '');
          var selectedValue = select.options[select.selectedIndex].value;

          $.each(spConfig.config.attributes[attributeId].options, function (optionIndex, option) {
            if (option.id == selectedValue) {
              var optionProducts = option.products;

              if (foundIds.length == 0) {
                foundIds = optionProducts;
              } else {
                var productIntersection = [];
                $.each(optionProducts, function (productIndex, productId) {
                  if (foundIds.indexOf(productId) > -1) {
                    productIntersection.push(productId);
                  }
                });
                foundIds = productIntersection;
              }
            }
          });
        });

        if (foundIds.length == 1) {
          var selectedProductId = foundIds[0];

          if (selectedProductId in contentsWithIds) {
            selectedSkuData = contentsWithIds[selectedProductId];
            selectedSku = contentsWithIds[selectedProductId]['id'];

            if (taxFlag) {
              if ($priceInclTax.length) {
                selectedPrice = formatPrice($priceInclTax.text());
              } else {
                if ($price.length) {
                  selectedPrice = formatPrice($price.text());
                }
              }
            } else {
              if ($price.length) {
                selectedPrice = formatPrice($price.text());
              }
            }
          }
        }
      }

      // eof Detect selected SKU

      // bof Detect selected SKU qty
      if ($('#qty').length) {
        $('#qty').on('change keyup paste click', function () {
          // Make sure it is 2 decimal float
          // (Magento allows qty to be a float not just integer)
          selectedQty = formatQty($(this).val());
        });
      }
      // eof Detect selected SKU qty

      // bof Update product data based on selected SKU
      function updateProductData() {
        if ('contents' in productData) {
          // Set selected SKU data
          if (selectedSkuData !== null) {
            // Reset contents
            productData.contents = [];
            // Set selected SKU contents
            productData.contents[0] = selectedSkuData;
          }

          // Set selected price to item_price and value based on qty
          if (selectedPrice) {
            // Set item_price
            productData.contents[0].item_price = selectedPrice;

            // The value field must be calculated based on qty
            if (selectedQty > 1) {
              var total = selectedQty * selectedPrice;
              // Make sure it is 2 decimal float
              productData.value = parseFloat(parseFloat(total).toFixed(2));
            } else {
              productData.value = selectedPrice;
            }
          }

          if (selectedQty) {
            productData.contents[0].quantity = selectedQty;
          }

          if (selectedSku) {
            productData.contents[0].id = selectedSku;
          }
        }

        // Make sure content_type is set to product
        // Dedection will return product not product_group
        if ('content_type' in productData) {
          productData.content_type = 'product';
        }
      }

      // eof Update product data based on selected SKU (configurable)

      // eof Detect product SKU (configurable)
        <?php elseif ($productType == 'grouped'): ?>
      // bof Detect product SKUs (grouped)

      var contentsWithIds = <?php /* @noEscape */ echo $contentsWithIdsJson ?>;
      var selectors = {};
      var nameAttributeValue = '';

      for (let productId in contentsWithIds) {
        // Set all quantities to zero to as default
        contentsWithIds[productId].quantity = 0;

        if (taxFlag) {
          if ($('#price-including-tax-' + productId).length) {
            selectors['price' + productId] = $('#price-including-tax-' + productId);
          } else {
            if ($('#product-price-' + productId).length) {
              selectors['price' + productId] = $('#product-price-' + productId);
            }
          }
        } else {
          if ($('#product-price-' + productId).length) {
            selectors['price' + productId] = $('#product-price-' + productId);
          }
        }

        nameAttributeValue = 'super_group[' + productId + ']';
        selectors['qty' + productId] = $('input[name="' + nameAttributeValue + '"]');

        // Set all quantities and prices to current
        contentsWithIds[productId].quantity = selectors['qty' + productId].val();
        contentsWithIds[productId].item_price = formatPrice(selectors['price' + productId].text());

        // Set all selectors for change keyup paste click
        selectors['qty' + productId].on('change keyup paste click', function () {
          var nameAttr = $(this).attr('name');
          var productId = nameAttr.replace(/[^0-9]/g, '');

          // Make sure it is 2 decimal float
          // (Magento allows qty to be a float not just integer)
          contentsWithIds[productId].quantity = parseFloat(parseFloat($(this).val()).toFixed(2));
        });
      }

      // bof Update product data based on selected SKUs (grouped)
      function updateProductData() {
        if ('contents' in productData) {
          // Reset contents
          productData.contents = [];

          // Set value to 0
          productData.value = 0;

          var qty = 0;
          var newValue = 0;
          var itemValue = 0;
          var itemTotal = 0;

          for (let productId in contentsWithIds) {
            qty = contentsWithIds[productId].quantity;
            if (qty > 0) {
              // Set the item in the contents
              productData.contents.push(contentsWithIds[productId]);

              // Increase value based on qty and price
              itemValue = contentsWithIds[productId].item_price;
              itemTotal = itemValue * qty;
              // Make sure it is 2 decimal float
              newValue += parseFloat(parseFloat(itemTotal).toFixed(2));
            }
          }

          // Set value to new value
          productData.value = newValue;
        }

        // Make sure content_type is set to product
        // Dedection will return product not product_group
        if ('content_type' in productData) {
          productData.content_type = 'product';
        }
      }

      // eof Update product data based on selected SKUs (grouped)

      // eof Detect product SKUs (grouped)
        <?php else: ?>
      // bof Detect product SKU (downlodable, simple, virtual, etc.)

      var selectedPrice = 0;
      var selectedQty = 0;
      var productId = <?php /* @noEscape */ echo $productId ?>;
      var $price = $('#product-price-' + productId);
      var $priceInclTax = $('#price-including-tax-' + productId);

      // bof Detect selected SKU qty
      if ($('#qty').length) {
        $('#qty').on('change keyup paste click', function () {
          // Make sure it is 2 decimal float
          // (Magento allows qty to be a float not just integer)
          selectedQty = formatQty($(this).val());
        });
      }
      // eof Detect selected SKU qty

      // bof Update product data based on selected SKUs (downlodable, simple, virtual, etc.)
      function updateProductData() {
        if ('contents' in productData) {
          // Get price
          if (taxFlag) {
            if ($priceInclTax.length) {
              selectedPrice = formatPrice($priceInclTax.text());
            } else {
              if ($price.length) {
                selectedPrice = formatPrice($price.text());
              }
            }
          } else {
            if ($price.length) {
              selectedPrice = formatPrice($price.text());
            }
          }

          var newValue = 0;
          var newItemPrice = 0;
          var newQty = 0;

          // If there is selected price update item_price
          if (selectedPrice > 0) {
            productData.contents[0].item_price = selectedPrice;
          }

          // If there is selected quantity update quantity
          if (selectedQty > 0) {
            productData.contents[0].quantity = selectedQty;
          }

          // Get new value
          newItemPrice = parseFloat(parseFloat(productData.contents[0].item_price).toFixed(2));
          newQty = parseFloat(parseFloat(productData.contents[0].quantity).toFixed(2));
          newValue = newItemPrice * newQty;

          // Make sure it is 2 decimal float
          productData.value = parseFloat(parseFloat(newValue).toFixed(2));
        }

        // Make sure content_type is set to product
        // Dedection will return product not product_group
        if ('content_type' in productData) {
          productData.content_type = 'product';
        }
      }

      // eof Update product data based on selected SKU (downlodable, simple, virtual, etc.)

      // eof Detect product SKU (downlodable, simple, virtual, etc.)
        <?php endif; ?>

      // bof Utility functions used by all types of products
      function formatQty(text) {
        // Make sure it is 2 decimal float
        return parseFloat(parseFloat(text).toFixed(2));
      }

      function formatPrice(text) {
        var decimalSymbol = escapeRegExp('<?php /* @noEscape */ echo $this->getPriceDecimalSymbol(); ?>');

        // RegEx to remove everything except numbers and decimal sign
        // Example: /[^0-9\.]/gi
        var regEx = new RegExp('[^0-9' + decimalSymbol + ']', 'gi');
        var priceText = text.replace(regEx, '');

        // Replace decimal sign with decimal point
        var price = priceText.replace(decimalSymbol, '.');

        // Make sure it is 2 decimal float
        return parseFloat(parseFloat(price).toFixed(2));
      }

      function escapeRegExp(text) {
        return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
      }

      // eof Utility functions used by all types of products

      // eof isDetectSelectedSkuEnabled
        <?php endif; ?>

      var viewContentEventId = generateEventId('ViewContent');
      var viewContentEventIdObj = {};
      viewContentEventIdObj.eventID = viewContentEventId;

// If needed disallowed params will be moved
      productDataForViewContent = moveParamsOutsideContents(productDataForViewContent);

        <?php if ($this->isEventEnabled('ViewContent') && $isBaseCodeEnabled): ?>
      fbq('track', 'ViewContent', productDataForViewContent, viewContentEventIdObj);
        <?php endif; ?>

        <?php if ($this->isApiEventEnabled('ViewContent')): ?>
      fireConversionsApiEvent('ViewContent', productDataForViewContent, viewContentEventId);
        <?php endif; ?>

// Add Pixel Events to the button's click handler
      $('.add-to-cart .btn-cart').on('click', function () {
        // Update product data if detection is enabled
        if (isDetectSelectedSkuEnabled) {
          updateProductData();
        }
        // The contents must have at least one item
        if (productData.contents.length) {
          // If needed disallowed params will be moved
          productData = moveParamsOutsideContents(productData);

          var addToCartEventId = generateEventId('AddToCart');
          var addToCartEventIdObj = {};
          addToCartEventIdObj.eventID = addToCartEventId;

            <?php if ($this->isEventEnabled('AddToCart') && $isBaseCodeEnabled): ?>
          fbq('track', 'AddToCart', productData, addToCartEventIdObj);
            <?php endif; ?>

            <?php if ($this->isApiEventEnabled('AddToCart')): ?>
          fireConversionsApiEvent('AddToCart', productData, addToCartEventId);
            <?php endif; ?>
        }
      });

      $('.add-to-links .link-wishlist').on('click', function () {
        // Update product data if detection is enabled
        if (isDetectSelectedSkuEnabled) {
          updateProductData();
        }
        // The contents must have at least one item
        if (productData.contents.length) {
          // If needed disallowed params will be moved
          productData = moveParamsOutsideContents(productData);

          var addToWishlistEventId = generateEventId('AddToWishlist');
          var addToWishlistEventIdObj = {};
          addToWishlistEventIdObj.eventID = addToWishlistEventId;

            <?php if ($this->isEventEnabled('AddToWishlist') && $isBaseCodeEnabled): ?>
          fbq('track', 'AddToWishlist', productData, addToWishlistEventIdObj);
            <?php endif; ?>

            <?php if ($this->isApiEventEnabled('AddToWishlist')): ?>
          fireConversionsApiEvent('AddToWishlist', productData, addToWishlistEventId);
            <?php endif; ?>
        }
      });
        <?php
        // eof product data
        endif; ?>
        <?php
        // eof product code
        // bof search code
      elseif (in_array($action, $pageHandlesSearch)): ?>
        <?php if ($isPageViewEnabled && $isBaseCodeEnabled && $isPageViewWithAll): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($isPageViewEnabled && $isPageViewWithAllApi): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php
        $searchData = $this->getSearchData();
        if (!empty($searchData)): ?>
        <?php
        $searchEventName = $searchData['event_name'];
        $searchEventData = $searchData['data'];

        $searchEventNameJson = json_encode($searchEventName, JSON_PRETTY_PRINT);
        $searchEventDataJson = json_encode($searchEventData, JSON_FORCE_OBJECT);
        ?>

      var searchEventName = <?php /* @noEscape */ echo $searchEventNameJson ?>;
      var searchEventData = <?php /* @noEscape */ echo $searchEventDataJson ?>;
      var searchEventId = generateEventId(searchEventName);
      var searchEventIdObj = {};
      searchEventIdObj.eventID = searchEventId;

        <?php if ($this->isEventEnabled('Search') && $isBaseCodeEnabled): ?>
      fbq("track", searchEventName, searchEventData, searchEventIdObj);
        <?php endif; ?>

        <?php if ($this->isApiEventEnabled('Search')): ?>
      fireConversionsApiEvent(searchEventName, searchEventData, searchEventId);
        <?php endif; ?>

        <?php endif; ?>
        <?php
        // eof search code
        // bof register code
      elseif ($action == 'customer_account_create'): ?>
        <?php if ($isPageViewEnabled && $isBaseCodeEnabled && $isPageViewWithAll): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($isPageViewEnabled && $isPageViewWithAllApi): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php
        $regData = $this->getDataForCompleteRegistrationEvent();
        if (!empty($regData)): ?>
        <?php
        $regEventName = $regData['event_name'];
        $regEventData = $regData['data'];

        $regEventNameJson = json_encode($regEventName, JSON_PRETTY_PRINT);
        $regEventDataJson = json_encode($regEventData, JSON_FORCE_OBJECT);
        ?>
      $('#form-validate.form-create-account button.submit').on('click', function () {
        var regEventName = <?php /* @noEscape */ echo $regEventNameJson ?>;
        var regEventData = <?php /* @noEscape */ echo $regEventDataJson ?>;
        var regEventId = generateEventId(regEventName);
        var regEventIdObj = {};
        regEventIdObj.eventID = regEventId;

          <?php if ($this->isEventEnabled('CompleteRegistration') && $isBaseCodeEnabled): ?>
        fbq("track", regEventName, regEventData, regEventIdObj);
          <?php endif; ?>

          <?php if ($this->isApiEventEnabled('CompleteRegistration')): ?>
        fireConversionsApiEvent(regEventName, regEventData, regEventId);
          <?php endif; ?>
      });
        <?php endif; ?>
        <?php
        // eof register code
        // bof checkout
      elseif (in_array($action, $pageHandlesQuote)): ?>
        <?php if ($isPageViewEnabled && $isBaseCodeEnabled && $isPageViewWithAll): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($isPageViewEnabled && $isPageViewWithAllApi): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php
        $quoteData = $this->getQuoteData();
        if (!empty($quoteData)): ?>
        <?php
        $quoteEventName = $quoteData['event_name'];
        $quoteEventData = $quoteData['data'];

        $quoteEventNameJson = json_encode($quoteEventName, JSON_PRETTY_PRINT);
        $quoteEventDataJson = json_encode($quoteEventData, JSON_PRETTY_PRINT);
        ?>

      var quoteEventName = <?php /* @noEscape */ echo $quoteEventNameJson ?>;
      var quoteEventData = <?php /* @noEscape */ echo $quoteEventDataJson ?>;
      var quoteEventId = generateEventId(quoteEventName);
      var quoteEventIdObj = {};
      quoteEventIdObj.eventID = quoteEventId;

        <?php if ($this->isEventEnabled('InitiateCheckout') && $isBaseCodeEnabled): ?>
      fbq("track", quoteEventName, quoteEventData, quoteEventIdObj);
        <?php endif; ?>

        <?php if ($this->isApiEventEnabled('InitiateCheckout')): ?>
      fireConversionsApiEvent(quoteEventName, quoteEventData, quoteEventId);
        <?php endif; ?>

        <?php endif; ?>
        <?php
        // eof checkout
        // bof checkout success
      elseif (in_array($action, $pageHandlesOrder)): ?>
        <?php if ($isPageViewEnabled && $isBaseCodeEnabled && $isPageViewWithAll): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($isPageViewEnabled && $isPageViewWithAllApi): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php
        $orderData = $this->getOrderData();
        if (!empty($orderData)): ?>
        <?php
        $orderEventName = $orderData['event_name'];
        $orderEventData = $orderData['data'];

        $orderEventNameJson = json_encode($orderEventName, JSON_PRETTY_PRINT);
        $orderEventDataJson = json_encode($orderEventData, JSON_PRETTY_PRINT);
        ?>

      var orderEventName = <?php /* @noEscape */ echo $orderEventNameJson ?>;
      var orderEventData = <?php /* @noEscape */ echo $orderEventDataJson ?>;
      var orderEventId = generateEventId(orderEventName);
      var orderEventIdObj = {};
      orderEventIdObj.eventID = orderEventId;

        <?php if ($this->isEventEnabled('Purchase') && $isBaseCodeEnabled): ?>
      fbq("track", orderEventName, orderEventData, orderEventIdObj);
        <?php endif; ?>

        <?php if ($this->isApiEventEnabled('Purchase')): ?>
      fireConversionsApiEvent(orderEventName, orderEventData, orderEventId);
        <?php endif; ?>

        <?php endif; ?>
        <?php
        // eof checkout success
        // bof cms_index_index or cms_page_view or any other page.
        else: ?>
        <?php if ($this->isEventEnabled('PageView') && $isBaseCodeEnabled): ?>
      fbq("track", "PageView", pageViewData, pageViewEventIdObj);
        <?php endif; ?>

        <?php if ($this->isApiEventEnabled('PageView')): ?>
      fireConversionsApiEvent("PageView", pageViewData, pageViewEventId);
        <?php endif; ?>

        <?php endif; //eof cms_index_index or cms_page_view or any other page. ?>
    } // end of is customer data call
  }); // end of on ajaxComplete
})(jQuery, window, document);
</script>

<?php if ($this->isNoScriptEnabled()): ?>
<noscript>
    <?php foreach ($idData as $id): ?>
      <img height="1" width="1" style="display:none"
           alt="Facebook Pixel <?php echo $this->escapeHtml($id) ?>"
           src="https://www.facebook.com/tr?id=<?php echo $this->escapeHtml($id) ?>&ev=PageView&noscript=1"/>
    <?php endforeach; ?>
</noscript>
<?php endif; ?>
<!-- End Facebook Pixel Code -->

<?php
// eof Check if FB Pixel is allowed
endif; ?>
