<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php if (!$this->isCustomerLoggedIn()): ?>
    <div style="display:none;">
        <div class="checkout-popup-login" id="checkoutPopUpLogin">
            <h4><?php echo $this->__('Login to your account') ?></h4>
            <?php echo $this->getChildHtml('login_before')?>
            <?php echo $this->getMessagesBlock()->toHtml() ?>

            <form id="login-form" action="<?php echo $this->getPostAction() ?>" method="post">
                <?php echo $this->getBlockHtml('formkey'); ?>

                <label for="login-email"><?php echo $this->__('Email ') ?><em>*</em></label>
                <input type="email" class="input-text required-entry validate-email" id="login-email" name="login[username]" value="<?php echo $this->escapeHtml($this->getUsername()) ?>" />

                <label for="login-password">* <?php echo $this->__('Password') ?><em>*</em></label>
                <input type="password" class="input-text required-entry" id="login-password" name="login[password]" />

                <?php echo $this->getChildHtml('form.additional.info'); ?>
                <?php echo $this->getChildHtml('persistent.remember.me'); ?>

                <input name="context" type="hidden" value="checkout" />
                <div class="clearH"></div>

                <button type="submit" class="button" onclick="onepageLogin(this)"><?php echo $this->__('Login') ?></button>
                <a href="<?php echo $this->getUrl('customer/account/forgotpassword') ?>" class="forgotpassword"><?php echo $this->__('Forgot your password?') ?></a>
            </form>
        </div>
    </div>

    <script type="text/javascript">
        var loginForm = new VarienForm('login-form', true);
        $('login-email').observe('keypress', bindLoginPost);
        $('login-password').observe('keypress', bindLoginPost);
        function bindLoginPost(evt){
            if (evt.keyCode == Event.KEY_RETURN) {
                loginForm.submit();
            }
        }
        function onepageLogin(button)
        {
            if(loginForm.validator && loginForm.validator.validate()){
                button.disabled = true;
                loginForm.submit();
            }
        }
    </script>
<?php endif ?>
