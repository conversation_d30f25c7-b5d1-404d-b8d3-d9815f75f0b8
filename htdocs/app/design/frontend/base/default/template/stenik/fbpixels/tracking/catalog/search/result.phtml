<?php
    $helper = $this->helper('stenik_fbpixels');

    if($helper->isEventHidden('search'))
        return;

    if(!Mage::app()->getRequest()->getParam($helper->getCatalogSearchQueryParameter()))
        return;

    $searchString = Mage::app()->getRequest()->getParam($helper->getCatalogSearchQueryParameter());

/**
 * Other parameters
 *     value, currency, content_category, content_ids, search_string
 */
?>
<script>
fbq('track', 'Search', {
    search_string: '<?php echo $searchString ?>'
});
</script>
