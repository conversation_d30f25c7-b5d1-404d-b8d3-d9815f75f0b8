<?php
$pageData = $this->getPageData();
$pageIdentifier = Mage::app()->getFrontController()->getAction()->getFullActionName();
?>
<!-- Global site tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo $pageData->getConversionId() ?>"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '<?php echo $pageData->getConversionId() ?>');
</script>
<script>
    gtag('event', 'page_view', {
        'send_to': '<?php echo $pageData->getConversionId() ?>',
        <?php if ($pageData->getItemId()): ?>
            'dynx_itemid': '<?php echo $pageData->getItemId(); ?>',
        <?php endif; ?>
        'dynx_pagetype': '<?php echo ($pageData->getPageType()) ? $pageData->getPageType() : '' ?>',
        'dynx_totalvalue': '<?php echo ($pageData->getTotalValue()) ? $pageData->getTotalValue() : 0 ?>'
    });
</script>
<?php if ($pageIdentifier == 'checkout_onepage_success'): ?>
<!-- Event snippet for Purchase conversion page -->
<?php
$conversionId = $pageData->getConversionId();
if ($pageData->getLabelId()) {
    $conversionId .= '/' . $pageData->getLabelId();
}
?>
<script>
    gtag('event', 'conversion', {
        'send_to': '<?php echo $conversionId ?>'
    });
</script>
<?php endif; ?>
