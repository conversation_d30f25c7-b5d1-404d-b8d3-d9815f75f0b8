<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /** @var $this Mage_Checkout_Block_Onepage_Shipping_Method_Available */ ?>
<?php $_shippingRateGroups = $this->getShippingRates(); ?>

<?php if (!$_shippingRateGroups): ?>

    <p><?php echo $this->__('Sorry, no quotes are available for this order at this time.') ?></p>

<?php else: ?>
    <?php
        $shippingMethodToCheck = $this->getAddressShippingMethod();
        if (!$shippingMethodToCheck) {
            // Autocheck first
            foreach ($_shippingRateGroups as $_rates) {
                foreach ($_rates as $_rate) {
                    $shippingMethodToCheck = $_rate->getCode();
                    break 2;
                }
            }
        }
    ?>
    <?php $shippingCodePrice = array(); ?>
    <?php $_sole = count($_shippingRateGroups) == 1; foreach ($_shippingRateGroups as $code => $_rates): ?>

            <?php $_sole = $_sole && count($_rates) == 1; foreach ($_rates as $_rate): ?>

                <?php $shippingCodePrice[] = "'".$_rate->getCode()."':".(float)$_rate->getPrice(); ?>

                <?php if (!$_rate->getErrorMessage()): ?>

                    <?php if ($_sole) : ?>
                        <span class="no-display shippingMethod">
                            <input name="shipping_method_prechoose" data-used-address-fields="<?php echo $this->getMethodUsedAddressFields($_rate->getCode()) ?>" type="radio" value="<?php echo $_rate->getCode() ?>" id="s_method_prechoose_<?php echo $_rate->getCode() ?>" checked="checked" class="validate-one-required-by-name" />
                        </span>
                    <?php else: ?>
                        <div class="shippingMethod shippingMethodPrechoose" data-applicable-countries="<?php echo $_rate->getApplicableCountries() ? implode(',', $_rate->getApplicableCountries()) : '' ?>">
                            <input name="shipping_method_prechoose" data-used-address-fields="<?php echo $this->getMethodUsedAddressFields($_rate->getCode()) ?>" type="radio" value="<?php echo $_rate->getCode() ?>" id="s_method_prechoose_<?php echo $_rate->getCode() ?>"<?php if($_rate->getCode()===$shippingMethodToCheck) echo ' checked="checked"' ?> class="radio prechoose validate-one-required-by-name"/>

                            <?php if ($_rate->getCode() === $shippingMethodToCheck): ?>
                                <script type="text/javascript">
                                    lastPrice = <?php echo (float)$_rate->getPrice(); ?>;
                                </script>
                            <?php endif; ?>

                            <label for="s_method_prechoose_<?php echo $_rate->getCode() ?>">
                                <?php echo $this->escapeHtml($_rate->getMethodTitle()) ?>
                                <span class="no-display price"></span>
                            </label>
                        </div>
                    <?php endif; ?>

                <?php endif ?>

            <?php endforeach; ?>

    <?php endforeach; ?>

<?php endif; ?>

<script>
    jQuery(function($) {
        $('.shippingMethodPrechoose input').change(function() {
            var $noSelectedShippingMethodText = $('#checkout-shipping-method-load .no-selected-address-text');
            if ($noSelectedShippingMethodText.length) {
                $noSelectedShippingMethodText.show();
            } else {
                $('#checkout-shipping-method-load .no-shipping-quotes-text').show();
            }
            $('#checkout-shipping-method-load .sp-methods').hide();
        });
    });
</script>
