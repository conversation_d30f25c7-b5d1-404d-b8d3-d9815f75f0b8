<?php
    /**
     * Available helper functions to use:
     *
     * echo $this->helper('stenik_gdprcompliance')->getTermsCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageContent();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageContent();
     */
?>

<?php $helperTermsAndPrivacy = $this->helper('stenik_gdprcompliance/agreement_termsAndPrivacy'); ?>
<?php if ($helperTermsAndPrivacy->isEnabled()): ?>
    <li class="control">
        <div class="input-box">
            <input type="checkbox" name="<?php echo $this->getFieldName('gdpr_terms_and_privacy'); ?>" title="<?php echo $this->htmlEscape(strip_tags($helperTermsAndPrivacy->getAgreement())); ?>" value="1" id="<?php echo $this->getFieldId('gdpr_terms_and_privacy'); ?>"<?php if($this->getFormData()->getGdprTermsAndPrivacy() == 1): ?> checked="checked"<?php endif; ?> class="checkbox required-entry" />
        </div>
        <label for="<?php echo $this->getFieldId('gdpr_terms_and_privacy'); ?>"><?php echo $helperTermsAndPrivacy->getAgreement(); ?></label>
    </li>
<?php endif ?>

<?php $helperPrivacyPolicy = $this->helper('stenik_gdprcompliance/agreement_privacyPolicy'); ?>
<?php if ($helperPrivacyPolicy->isEnabled()): ?>
    <li class="control">
        <div class="input-box">
            <input type="checkbox" name="<?php echo $this->getFieldName('gdpr_privacy_policy')?>" title="<?php echo $this->htmlEscape(strip_tags($helperPrivacyPolicy->getAgreement())); ?>" value="1" id="<?php echo $this->getFieldId('gdpr_privacy_policy'); ?>"<?php if($this->getFormData()->getGdprPrivacyPolicy()): ?> checked="checked"<?php endif; ?> class="checkbox required-entry" />
        </div>
        <label for="<?php echo $this->getFieldId('gdpr_privacy_policy'); ?>"><?php echo $helperPrivacyPolicy->getAgreement(); ?></label>
        <?php if ($helperPrivacyPolicy->getAdditionalInformation()): ?>
        <label><?php echo $helperPrivacyPolicy->getAdditionalInformation(); ?></label>
        <?php endif; ?>
    </li>
<?php else: ?>
    <li class="control">
        <div class="input-box">
            <input type="hidden" name="<?php echo $this->getFieldName('gdpr_privacy_policy'); ?>" title="<?php echo $this->htmlEscape(strip_tags($helperPrivacyPolicy->getAgreement())); ?>" value="1" id="gdpr_privacy_policy" />
        </div>
        <?php if ($helperPrivacyPolicy->getAdditionalInformation()): ?>
        <label><?php echo $helperPrivacyPolicy->getAdditionalInformation(); ?></label>
        <?php endif; ?>
    </li>
<?php endif ?>

<?php $helperAge = $this->helper('stenik_gdprcompliance/agreement_age'); ?>
<?php if ($helperAge->isEnabled()): ?>
    <li class="control">
        <div class="input-box">
            <input type="checkbox" name="<?php echo $this->getFieldName('gdpr_age')?>" title="<?php echo $this->htmlEscape(strip_tags($helperAge->getAgreement())); ?>" value="1" id="<?php echo $this->getFieldId('gdpr_age'); ?>"<?php if($this->getFormData()->getGdprAge()): ?> checked="checked"<?php endif; ?> class="checkbox required-entry" />
        </div>
        <label for="<?php echo $this->getFieldId('gdpr_age'); ?>"><?php echo $helperAge->getAgreement(); ?></label>
        <?php if ($helperAge->getAdditionalInformation()): ?>
        <label><?php echo $helperAge->getAdditionalInformation(); ?></label>
        <?php endif; ?>
    </li>
<?php else: ?>
    <li class="control">
        <div class="input-box">
            <input type="hidden" name="<?php echo $this->getFieldName('gdpr_age'); ?>" title="<?php echo $this->htmlEscape(strip_tags($helperAge->getAgreement())); ?>" value="1" id="gdpr_age" />
        </div>
        <?php if ($helperAge->getAdditionalInformation()): ?>
        <label><?php echo $helperAge->getAdditionalInformation(); ?></label>
        <?php endif; ?>
    </li>
<?php endif ?>

<?php $helperNewsletter = $this->helper('stenik_gdprcompliance/agreement_newsletter'); ?>
<?php if ($this->getDisplayNewsletterCheckbox() && $helperNewsletter->isEnabled()): ?>
    <li class="control">
        <div class="input-box">
            <input type="checkbox" name="is_subscribed" title="<?php echo $this->htmlEscape(strip_tags($helperNewsletter->getAgreement())); ?>" value="1" id="is_subscribed"<?php if($this->getFormData()->getIsSubscribed()): ?> checked="checked"<?php endif; ?> class="checkbox" />
            <label for="is_subscribed"><?php echo $helperNewsletter->getAgreement(); ?></label>
        </div>
    </li>
<?php endif; ?>