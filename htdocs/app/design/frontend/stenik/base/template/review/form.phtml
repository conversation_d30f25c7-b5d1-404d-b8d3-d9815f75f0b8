<?php
/**
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Review_Block_Form
 */
?>
<?php if ($this->getAllowWriteReviewFlag()): ?>
    <div class="col-xs-5 customer-form">
        <h6><?php echo $this->__('Your opinion is important to us');?></h6>
        <div class="clearH"></div>
        <form action="<?php echo $this->getAction() ?>" method="post" id="review-form">

            <?php echo $this->getBlockHtml('formkey'); ?>
            <?php echo $this->getChildHtml('form_fields_before')?>

            <label for="subjectID">* <?php echo $this->__('Subject');?></label>
            <input type="text" name="title" class="input-text required-entry" value="<?php echo $this->escapeHtml($this->getProductInfo()->getName()) ?>" id="subjectID">

            <label for="review_nickname">* <?php echo $this->__('Your names');?></label>
            <input type="text" name="nickname" id="review_nickname" class="input-text required-entry" value="<?php echo $this->escapeHtml($data->getNickname()) ?>" >

            <div class="clearH"></div>

            <label class="auto-width"><?php echo $this->__('Your rating');?></label>
            <div class="rating-stars" id="product-review-table">
                <?php foreach ($this->getRatings() as $_rating): ?>
                    <?php foreach ($_rating->getOptions() as $_option): ?>
                        <input type="radio"
                               name="ratings[<?php echo $_rating->getId() ?>]"
                               id="<?php echo $this->escapeHtml($_rating->getRatingCode()) ?>_<?php echo $_option->getValue() ?>"
                               value="<?php echo $_option->getId() ?>"
                               class="star cursorPointer"
                        />
                    <?php endforeach ?>
                <?php endforeach ?>
            </div>
            <input type="hidden" name="validate_rating" class="validate-rating" value="" />

            <div class="clearH"></div>

            <label for="">* <?php echo $this->__('Review');?></label>
            <textarea name="detail" id="review_detail" class="required-entry"></textarea>

            <div class="clearH"></div>

            <?php if (Mage::helper('core')->isModuleEnabled('Stenik_GdprCompliance')): ?>
                <div class="checkbox-content">
                    <?php echo $this->getLayout()->createBlock('stenik_gdprcompliance/widget_review')->toHtml(); ?>
                </div>
            <?php endif; ?>

            <div class="clearH"></div>

            <div class="google-captcha-box">
                <?php echo $this->getChildHtml('studioforty9.recaptcha.explicit'); ?>
            </div>

            <div class="clearH"></div>

            <button class="button" title="<?php echo $this->__('Leave a comment');?>" type="submit"><?php echo $this->__('Leave a comment');?></button>
        </form>
    </div>

    <script>
        jQuery(function($){
            $('#review-form .star').rating();
        });

        var dataForm = new VarienForm('review-form');
        Validation.addAllThese(
        [
           ['validate-rating', '<?php echo $this->__('Please select one of each of the ratings above') ?>', function(v) {
                var tr = $('product-review-table');
                var inputs;
                var error = 1;

                inputs = tr.select('input');

                for( i in inputs ) {
                    if( inputs[i].checked == true ) {
                        error = 0;
                    }
                }

                if( error == 1 ) {
                    return false;
                } else {
                    error = 1;
                }
                return true;
            }]
        ]
        );
    </script>

<?php else: ?>
    <?php
        $referer = Mage::helper('core/url')->getCurrentUrl();
        $referer = Mage::helper('core')->urlEncode($referer);
        $params = array('referer' => $referer);
    ?>
    <div class="col-xs-5 customer-form">
        <h6><?php echo $this->__('Your opinion is important to us');?></h6>
        <p>
            <?php echo $this->__('To rate You need to register or') ?>
            <a href="<?php echo $this->getUrl('customer/account/login', $params); ?>"><?php echo $this->__('log in to your account') ?></a>
            <?php echo $this->__('if you already have created!') ?>
        </p>
    </div>
<?php endif ?>