<?php

?>

<?php
    $sectionCode = $this->getSectionCode();
    $sectionGroup = $this->getSectionGroup();

    $updateSectionGroupsOnChange = $this->getUpdateSectionGroupsOnChange();
    $updateSectionGroupsOnFilledFields = $this->getUpdateSectionGroupsOnFilledFields() ? : array();

    if (is_array($updateSectionGroupsOnChange)) {
        $updateSectionGroupsOnChange = array_keys($updateSectionGroupsOnChange);
    } else {
        $updateSectionGroupsOnChange = array();
    }

?>

<div class="stenik-onepage-section"
     id="stenik-onepage-section-<?php echo $this->quoteEscape($sectionCode) ?>"
     data-section-code="<?php echo $this->quoteEscape($sectionCode) ?>"
     data-section-group="<?php echo $this->quoteEscape($sectionGroup) ?>"
     data-section-affect-groups="<?php echo implode(',', $updateSectionGroupsOnChange) ?>"
     data-section-affect-groups-on-filled-fields="<?php echo implode(',', $updateSectionGroupsOnFilledFields) ?>"
     data-section-preserve-values-on-reload="<?php echo (int) $this->getSectionPreserveValuesOnReload() ?>"
     data-section-disable-auto-submit="<?php echo (int) $this->getSectionDisableAutosubmit() ?>"
>
    <?php /* This content may be replaced with ajax */ ?>
    <?php echo $this->getChildHtml(); ?>
</div>

