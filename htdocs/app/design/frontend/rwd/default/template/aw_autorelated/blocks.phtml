<?php
/**
 * aheadWorks Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://ecommerce.aheadworks.com/AW-LICENSE.txt
 *
 * =================================================================
 *                 MAGENTO EDITION USAGE NOTICE
 * =================================================================
 * This software is designed to work with Magento community edition and
 * its use on an edition other than specified is prohibited. aheadWorks does not
 * provide extension support in case of incorrect edition use.
 * =================================================================
 *
 * @category   AW
 * @package    AW_Autorelated
 * @version    2.5.0
 * @copyright  Copyright (c) 2010-2012 aheadWorks Co. (http://www.aheadworks.com)
 * @license    http://ecommerce.aheadworks.com/AW-LICENSE.txt
 */
?><?php if ($this->getBlocks() && $this->getBlocks()->getSize()) : ?>
    <?php echo $this->getBlocksHtml() ?>
    <script type="text/javascript">
        //<![CDATA[
        document.observe('dom:loaded', function () {
            var aw_arp_popup=false;
            $$('#map-popup').each(
            function(item){
                if(aw_arp_popup){ item.remove(); }
                aw_arp_popup=true;
            });
        });
        //]]>
    </script>
<?php endif; ?>