<?xml version="1.0"?>
<layout>
    <checkout_onepage_index>
        <update handle="stenik_checkout_checkout_shipping_method_prechoose"/>
        <update handle="stenik_checkout_partial_billing_address_update"/>

        <reference name="checkout.onepage.billing.additional_address_fields">
            <block type="stenik_speedy/widget_address" name="stenik_speedy.address.fields" before="-">
                <action method="setFieldIdFormat"><format>billing:stenik_speedy_%s</format></action>
                <action method="setFieldNameFormat"><format>billing[stenik_speedy_%s]</format></action>
                <action method="setCustomerAddressFieldIdFormat"><format>billing:%s</format></action>
                <action method="setCustomerAddressFieldNameFormat"><format>billing[%s]</format></action>
                <!-- <action method="setTemplate"><empty/></action>
                <action method="setTemplate" ifconfig="carriers/stenik_speedy/active">
                    <template>stenik/speedy/widget/address.phtml</template>
                </action> -->
            </block>
        </reference>

        <reference name="checkout.onepage.shipping_method.prechoose">
            <action method="setMethodUsedAddressFields"><code>stenikspeedy_speedy</code><value>stenik-speedy-address-fields</value></action>
        </reference>
    </checkout_onepage_index>

    <stenik_checkout_partial_billing_address_update>
        <reference name="checkout.onepage.section.billing">
            <action method="addUpdateSectionGroupsOnFilledFields">
                <fields>
                    <stenik_speedy_city_name>billing:stenik_speedy_city_name</stenik_speedy_city_name>
                    <stenik_speedy_city_id>billing:stenik_speedy_city_id</stenik_speedy_city_id>
                    <stenik_speedy_district_name>billing:stenik_speedy_district_name</stenik_speedy_district_name>
                    <stenik_speedy_district_id>billing:stenik_speedy_district_id</stenik_speedy_district_id>
                    <stenik_speedy_street_name>billing:stenik_speedy_street_name</stenik_speedy_street_name>
                    <stenik_speedy_street_id>billing:stenik_speedy_street_id</stenik_speedy_street_id>
                    <stenik_speedy_street_number>billing:stenik_speedy_street_number</stenik_speedy_street_number>
                    <stenik_speedy_building>billing:stenik_speedy_building</stenik_speedy_building>
                    <stenik_speedy_entrance>billing:stenik_speedy_entrance</stenik_speedy_entrance>
                    <stenik_speedy_floor>billing:stenik_speedy_floor</stenik_speedy_floor>
                    <stenik_speedy_apartment>billing:stenik_speedy_apartment</stenik_speedy_apartment>
                    <stenik_speedy_note>billing:stenik_speedy_note</stenik_speedy_note>
                    <stenik_speedy_office_name>billing:stenik_speedy_office_name</stenik_speedy_office_name>
                    <stenik_speedy_office_id>billing:stenik_speedy_office_id</stenik_speedy_office_id>
                    <stenik_speedy_speedy_validation>billing:stenik_speedy_speedy_validation</stenik_speedy_speedy_validation>
                </fields>
            </action>
        </reference>
    </stenik_checkout_partial_billing_address_update>

    <customer_address_form>
        <update handle="stenik_checkout_customer_address_prechoose"></update>

        <reference name="customer.address.edit.shipping_methods">
            <action method="setMethodUsedAddressFields"><code>stenikspeedy_speedy</code><value>stenik-speedy-address-fields</value></action>
        </reference>

        <reference name="customer.address.edit.custom_address_fields">
            <block type="stenik_speedy/widget_address" name="stenik_speedy.address.fields" before="-">
                <action method="loadCustomerAddress"></action>
                <action method="setFieldIdFormat"><format>stenik_speedy_%s</format></action>
                <action method="setFieldNameFormat"><format>stenik_speedy_%s</format></action>
                <action method="setCustomerAddressFieldIdFormat"><format>%s</format></action>
                <action method="setCustomerAddressFieldNameFormat"><format>%s</format></action>
                <!-- <action method="setTemplate"><empty/></action>
                <action method="setTemplate" ifconfig="carriers/stenik_speedy/active">
                    <template>stenik/speedy/widget/address.phtml</template>
                </action> -->
            </block>
        </reference>
    </customer_address_form>
</layout>