<?php
//override  Enterprise_PageCache_Model_Observer renderBlockPlaceholder method
class Bitware_SpeedengineDeferJS_Model_PageCache_Observer extends Enterprise_PageCache_Model_Observer
{
    public function renderBlockPlaceholder(Varien_Event_Observer $observer)
    {
        if (!$this->_isEnabled) {
            return $this;
        }
        $block = $observer->getEvent()->getBlock();
        $transport = $observer->getEvent()->getTransport();
        $placeholder = $this->_config->getBlockPlaceholder($block);

        if ($transport && $placeholder && !$block->getSkipRenderTag()) {
            $blockHtml = $transport->getHtml();
            $footerJs = Mage::helper('bitware_speedengineDeferJS');
            if (in_array($block->getNameInLayout(), $footerJs->getBlocksToSkip())) {
                $blockHtml = $footerJs->addJsToExclude($blockHtml);
            }

            $request = Mage::app()->getFrontController()->getRequest();
            $processor = $this->_processor->getRequestProcessor($request);
            if ($processor && $processor->allowCache($request)) {
                $container = $placeholder->getContainerClass();
                if ($container && !Mage::getIsDeveloperMode()) {
                    $container = new $container($placeholder);
                    $container->setProcessor(Mage::getSingleton('enterprise_pagecache/processor'));
                    $container->setPlaceholderBlock($block);

                    // Modify to not save block with JS in it as JS is being moved to the end of the page.
                    $container->saveCache($footerJs->moveJs($blockHtml));
                }
            }

            $blockHtml = $placeholder->getStartTag() . $blockHtml . $placeholder->getEndTag();
            $transport->setHtml($blockHtml);
        }
        return $this;
    }
}