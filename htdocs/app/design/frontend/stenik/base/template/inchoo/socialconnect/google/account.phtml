<?php
/**
 * Inchoo is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
?>
<div class="inchoo-socialconnect-account">

    <h1><?php echo $this->__('Google Connect') ?></h1>

    <?php if($this->_hasData()): ?>

        <?php
            $googleId = $this->_getGoogleId();
            $status = $this->_getStatus();
            $email = $this->_getEmail();
            $picture = $this->_getPicture();
            $name = $this->_getName();
            $gender = $this->_getGender();
            $birthday = $this->_getBirthday();
        ?>

        <?php if(!empty($picture)): ?>
            <p style="float:left; margin: 0px 20px 20px 0px;"><img src="<?php echo $picture; ?>" alt="<?php echo $this->escapeHtml($name); ?>" /></p>
        <?php endif; ?>

        <?php if(!empty($status)): ?>
            <p><?php printf($this->__('Connected as %s', '<strong>'.$status.'</strong>')) ?></p>
        <?php endif; ?>

        <?php if(!empty($email)): ?>
            <p><?php printf($this->__('Email: %s', '<strong>'.$email.'</strong>')) ?></p>
        <?php endif; ?>

        <?php if(!empty($googleId)): ?>
            <p><?php printf($this->__('Google #: %s', '<strong>'.$googleId.'</strong>')) ?></p>
        <?php endif; ?>

        <?php if(!empty($gender)): ?>
            <p><?php printf($this->__('Gender: %s', '<strong>'.$gender.'</strong>')) ?></p>
        <?php endif; ?>

        <?php if(!empty($birthday)): ?>
            <p><?php printf($this->__('Birthday: %s', '<strong>'.$birthday.'</strong>')) ?></p>
        <?php endif; ?>
        
        <div class="clearH"></div>
        
        <p>
            <a href="<?php echo $this->getUrl('socialconnect/google/disconnect') ?>">
                <?php echo $this->__('Disconnect my profile on %s and my Gmail account.', Mage::getStoreConfig('general/store_information/name')) ?>
            </a>
        </p>

    <?php else: ?>

        <p><?php echo $this->__('You can connect store account with your Google account so you could login easier in the future.') ?></p>
        <div class="social-login">
            <?php echo $this->getChildHtml('inchoo_socialconnect_account_google_button'); ?>
        </div>

    <?php endif; ?>

</div>