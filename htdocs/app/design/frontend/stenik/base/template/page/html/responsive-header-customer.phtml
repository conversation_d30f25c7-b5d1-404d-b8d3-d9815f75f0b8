
<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<?php if ($this->helper('customer')->isLoggedIn()): ?>
    <a class="icon-link" href="<?php echo $this->getUrl('customer/account') ?>">
        <svg aria-hidden="true" class="icon-svg user"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#user' ?>"></use></svg>
        <?php echo $this->__('Profile');?>
    </a>
    <a class="icon-link" href="<?php echo $this->getUrl('customer/account/logout') ?>">
        <svg aria-hidden="true" class="icon-svg logout"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#logout' ?>"></use></svg>
        <?php echo $this->__('Logout');?>
    </a>
<?php else: ?>
    <a class="icon-link" href="<?php echo $this->getUrl('customer/account/login') ?>">
        <svg aria-hidden="true" class="icon-svg user"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#user' ?>"></use></svg>
        <?php echo $this->__('Login');?>
    </a>
    <a class="icon-link" href="<?php echo $this->getUrl('customer/account/create') ?>">
        <svg aria-hidden="true" class="icon-svg register"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#register' ?>"></use></svg>
        <?php echo $this->__('Registration');?>
    </a>
<?php endif ?>

<a href="<?php if ($_items): ?><?php echo $_helper->getListUrl() ?><?php else: ?>javascript:;<?php endif; ?>" class="icon-link<?php if ($_helper->getItemCount()): ?> has-items<?php endif ?>">
    <svg aria-hidden="true" class="icon-svg compare"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use></svg>
    <?php echo $this->__('Compare Products');?>
    <span class="notification"><?php echo $_helper->getItemCount(); ?></span>
</a>

<a href="<?php echo $this->getUrl('wishlist') ?>" class="icon-link<?php if ($this->helper('wishlist')->getItemCount() >= 1): ?> has-items<?php endif ?>">
    <svg aria-hidden="true" class="icon-svg wishlist"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use></svg>
    <?php echo $this->__('My Wishlist');?>
    <span class="notification"><?php echo $this->helper('wishlist')->getItemCount(); ?></span>
</a>