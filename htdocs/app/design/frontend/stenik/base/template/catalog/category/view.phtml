<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Category view template
 *
 * @see Mage_Catalog_Block_Category_View
 */
?>
<?php
    $_helper    = $this->helper('catalog/output');
    $_category  = $this->getCurrentCategory();
    $_imgHtml  = '';
    if ($_imgUrl = $_category->getImageUrl()) {
        $_imgHtml = '<img src="'.$_imgUrl.'" alt="'.$this->escapeHtml($_category->getName()).'" title="'.$this->escapeHtml($_category->getName()).'" />';
        $_imgHtml = $_helper->categoryAttribute($_category, $_imgHtml, 'image');
    }
?>

<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>

<div class="row">
    <div class="col-xs-12">

        <?php if ($_category->getPageHeadingTitle()): ?>
            <h1><?php echo $_category->getPageHeadingTitle(); ?></h1>
        <?php elseif ($_category->getSeoTitle()): ?>
            <h1><?php echo $_category->getSeoTitle(); ?></h1>
        <?php else: ?>
            <h1><?php echo $_helper->categoryAttribute($_category, $_category->getName(), 'name') ?></h1>
        <?php endif; ?>
        
        <?php if($_imgUrl): ?>
            <div class="category-banner">
                <?php echo $_imgHtml ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php if($this->isContentMode()): ?>
    <div class="category-landing-cms">
        <?php echo $this->getCmsBlockHtml() ?>
    </div>
<?php elseif($this->isMixedMode()): ?>
    <div class="category-landing-cms">
        <?php echo $this->getCmsBlockHtml() ?>
    </div>
    <?php echo $this->getProductListHtml() ?>
<?php else: ?>
    <?php echo $this->getProductListHtml() ?>
<?php endif; ?>


<?php if($_description=$this->getCurrentCategory()->getDescription()): ?>
    <?php if (Mage::app()->getRequest()->getParam('p',0) <= 1): // Show description only on first page ?>
        <div class="row">
            <div class="col-sm-12">
                <div class="accent-text-content category-description">
                    <div class="text-page">
                        <?php echo $_helper->categoryAttribute($_category, $_description, 'description') ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>