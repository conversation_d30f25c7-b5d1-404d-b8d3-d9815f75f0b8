<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     stenik_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Template for Mage_Page_Block_Html
 */
?>

<!DOCTYPE html>
<html lang="<?php echo $this->getLang() ?>" class="langClass-<?php echo $this->getLang() ?>">
    <head><?php echo $this->getChildHtml('head') ?></head>

    <body<?php echo $this->getBodyClass()?' class="'.$this->getBodyClass().'"':'' ?>>

        <?php echo $this->getChildHtml('after_body_start') ?>
        <?php echo $this->getChildHtml('global_notices') ?>

        <?php echo $this->getChildHtml('header') ?>

        <div id="main" class="col2-right-layout">

            <div class="container">

                <?php echo $this->getChildHtml('breadcrumbs') ?>
                
                <?php echo $this->getChildHtml('global_messages') ?>

                <div class="row main-content">
                    <div class="col-main col-xs-12 col-sm-9">
                        <?php echo $this->getChildHtml('content') ?>
                    </div>
                    <aside class="sidebar col-right col-xs-12 col-sm-3">
                        <?php echo $this->getChildHtml('right') ?>
                    </aside>
                </div>

            </div>
            
        </div>

        <?php echo $this->getChildHtml('footer') ?>

        <?php echo $this->getChildHtml('global_cookie_notice') ?>

        <?php echo $this->getChildHtml('before_body_end') ?>
        <?php echo $this->getAbsoluteFooter() ?>
    </body>
</html>
