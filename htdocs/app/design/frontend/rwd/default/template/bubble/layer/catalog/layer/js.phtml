<?php
/**
 * @var $this Bubble_Layer_Block_Catalog_Layer_Js
 */
?>
<div id="bubble-layer-overlay" style="display:none;">
    <img id="bubble-layer-loader" src="<?php echo $this->getSkinUrl('images/bubble/layer/loader.gif') ?>" width="64" height="68">
</div>
<script type="text/javascript">
//<![CDATA[
var BubbleLayer = new BubbleLayer({
    leftSelector: '.col-left-first',
    enableAjax: <?php echo $this->getAjaxEnabled(); ?>,
    enableAutoScroll: <?php echo $this->getAutoScrollEnabled(); ?>,
    enableAjaxToolbar: <?php echo $this->getAjaxToolbarEnabled(); ?>,
    currentUrl: '<?php echo $this->helper('core/url')->getCurrentUrl() ?>',
    priceFormat: <?php echo $this->helper('core')->jsonEncode($this->getJsPriceFormat()) ?>,
    onUpdateComplete: function() {
        if (typeof($j) === 'function') {
            $j('.toggle-content').each(function () {
                var wrapper = jQuery(this);

                var hasTabs = wrapper.hasClass('tabs');
                var hasAccordion = wrapper.hasClass('accordion');
                var startOpen = wrapper.hasClass('open');

                var dl = wrapper.children('dl:first');
                var dts = dl.children('dt');
                var panes = dl.children('dd');
                var groups = [dts, panes];

                //Create a ul for tabs if necessary.
                if (hasTabs) {
                    var ul = jQuery('<ul class="toggle-tabs"></ul>');
                    dts.each(function () {
                        var dt = jQuery(this);
                        var li = jQuery('<li></li>');
                        li.html(dt.html());
                        ul.append(li);
                    });
                    ul.insertBefore(dl);
                    var lis = ul.children();
                    groups.push(lis);
                }

                //Add "last" classes.
                var i;
                for (i = 0; i < groups.length; i++) {
                    groups[i].filter(':last').addClass('last');
                }

                function toggleClasses(clickedItem, group) {
                    var index = group.index(clickedItem);
                    var i;
                    for (i = 0; i < groups.length; i++) {
                        groups[i].removeClass('current');
                        groups[i].eq(index).addClass('current');
                    }
                }

                //Toggle on tab (dt) click.
                dts.on('click', function (e) {
                    //They clicked the current dt to close it. Restore the wrapper to unclicked state.
                    if (jQuery(this).hasClass('current') && wrapper.hasClass('accordion-open')) {
                        wrapper.removeClass('accordion-open');
                    } else {
                        //They're clicking something new. Reflect the explicit user interaction.
                        wrapper.addClass('accordion-open');
                    }
                    toggleClasses(jQuery(this), dts);
                });

                //Toggle on tab (li) click.
                if (hasTabs) {
                    lis.on('click', function (e) {
                        toggleClasses(jQuery(this), lis);
                    });
                    //Open the first tab.
                    lis.eq(0).trigger('click');
                }

                //Open the first accordion if desired.
                if (startOpen) {
                    dts.eq(0).trigger('click');
                }

            });

            enquire.register('(max-width: ' + bp.medium + 'px)', {
                setup: function () {
                    this.toggleElements = $j(
                        // This selects the menu on the My Account and CMS pages
                        '.col-left-first .block:not(.block-layered-nav) .block-title, ' +
                        '.col-left-first .block-layered-nav .block-subtitle--filter, ' +
                        '.sidebar:not(.col-left-first) .block .block-title'
                    );
                },
                match: function () {
                    this.toggleElements.toggleSingle();
                },
                unmatch: function () {
                    this.toggleElements.toggleSingle({destruct: true});
                }
            });
        }

        $$('.block-layered-nav dt').invoke('observe', 'click', function () {
            BubbleLayer.handlePriceSliders();
        });

        BubbleLayer.adaptHeight('.bubble-layer-top dd.filter-content');
        BubbleLayer.adaptHeight('.category-products .product-info');

        if (typeof BubbleAjaxCart != 'undefined' && BubbleAjaxCart) {
            BubbleAjaxCart.init();
        }
    }
});
// Adapt filter height
BubbleLayer.adaptHeight('.bubble-layer-top dd.filter-content');

Event.observe(window, 'resize', function() {
    BubbleLayer.adaptHeight('.bubble-layer-top dd.filter-content');
});
//]]>
</script>