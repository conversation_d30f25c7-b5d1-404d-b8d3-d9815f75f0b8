<?php
/**
 * @package Stenik_SbGensoft
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_SbGensoft_Block_Catalog_Product_View
 */
?>

<?php $warehousesInfo = $this->getWarehousesInfo() ?>
<?php if ($warehousesInfo && count($warehousesInfo)): ?>
    <ul>
        <?php foreach ($warehousesInfo as $warehouseInfo): ?>
            <li><?php echo $this->escapeHtml($warehouseInfo->getName()) ?></li>
        <?php endforeach ?>
    </ul>
<?php else: ?>
    <?php echo $this->__('There is no available data.');?>
<?php endif ?>