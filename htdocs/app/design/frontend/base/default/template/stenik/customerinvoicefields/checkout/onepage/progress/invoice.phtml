<?php
/**
 * @package Stenik_CustomerInvoiceFields
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php if ($this->getCheckout()->getStepData('billing', 'complete')): ?>
    <?php $quote = $this->getCheckout()->getQuote(); ?>
    <dt class="complete">
        <?php echo Mage::helper('stenik_customerinvoicefields')->__('Invoice') ?>
        <span class="changelink">
            <span class="separator">|</span>
            <a href="#billing"
               onclick="checkout.changeSection('opc-billing'); return false;"
            >
                <?php echo Mage::helper('checkout')->__($quote->getCustomerInvoice() ? 'Change' : 'Add') ?>
            </a>
        </span>
    </dt>

    <dd class="complete">
        <?php if ($quote->getCustomerInvoice()): ?>
            <table>
                <?php if ($quote->getCustomerInvoiceType() == Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_PERSONAL): ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal Name') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_personal_name')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal PIN') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_personal_pin')) ?></strong></td>
                    </tr>
                    <?php if (0): ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal City') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_personal_city')) ?></strong></td>
                    </tr>
                    <?php endif ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Personal Address') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_personal_addr')) ?></strong></td>
                    </tr>
                <?php else: # Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_COMPANY ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company Name') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_company_name')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company PIC') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_company_pic')) ?></strong></td>
                    </tr>
                    <?php if (0): ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company City') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_company_city')) ?></strong></td>
                    </tr>
                    <?php endif ?>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company Addr') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_company_addr')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company URN') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_company_urn')) ?></strong></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo Mage::helper('stenik_customerinvoicefields')->__('Company VAT') ?></label></td>
                        <td class="value"><strong><?php echo $this->escapeHtml($quote->getData('customer_invoice_company_vat')) ?></strong></td>
                    </tr>
                <?php endif ?>
            </table>

            <?php echo $this->getChildHtml() ?>
        <?php endif ?>
    </dd>
<?php else: ?>
    <dt>
        <?php echo Mage::helper('stenik_customerinvoicefields')->__('Invoice') ?>
    </dt>
<?php endif; ?>
