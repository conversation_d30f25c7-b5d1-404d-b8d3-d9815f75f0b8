<?xml version="1.0"?>
<config>
    <modules>
        <Bitware_SpeedengineDeferJS>
            <version>0.1.3</version>
        </Bitware_SpeedengineDeferJS>
    </modules>
    <global>
        <helpers>
            <bitware_speedengineDeferJS>
                <class>Bitware_SpeedengineDeferJS_Helper</class>
            </bitware_speedengineDeferJS>
        </helpers>
        <models>
            <bitware_speedengineDeferJS>
                <class>Bitware_SpeedengineDeferJS_Model</class>
            </bitware_speedengineDeferJS>
            <enterprise_pagecache>
                <rewrite>
                    <observer>Bitware_SpeedengineDeferJS_Model_PageCache_Observer</observer>
                </rewrite>
            </enterprise_pagecache>
        </models>
    </global>
    <frontend>
        <events>
            <core_block_abstract_to_html_after>
                <observers>
                    <bitware_speedengineDeferJS_block>
                        <type>singleton</type>
                        <class>bitware_speedengineDeferJS/observer</class>
                        <method>bottomInlineJS</method>
                    </bitware_speedengineDeferJS_block>
                </observers>
            </core_block_abstract_to_html_after>
        </events>
    </frontend>
	<adminhtml>
    <acl>
        <resources>
            <all>
                <title>Allow Everything</title>
            </all>
            <admin>
                <children>
                    <system>
                        <children>
                            <config>
                                <children>
                                    <bitware>
                                        <title>Bitware - All</title>
                                    </bitware>
                                </children>
                            </config>
                        </children>
                    </system>
                </children>
            </admin>
        </resources>
    </acl>
</adminhtml>
</config>
