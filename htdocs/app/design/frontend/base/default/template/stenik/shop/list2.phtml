<?php
/**
 * @package Stenik_Shop
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php
    $shopCollection = $this->getShopCollection();
    $shopCount      = $shopCollection->getSize();

    $cities = array();
    foreach ($shopCollection as $shop) {
        if (!in_array($shop->getCity(), $cities))
            $cities[] = $shop->getCity();
    }

?>

<h1><?php echo Mage::helper('checkout')->__('Shops') ?></h1>
<?php if ($shopsBanner = $this->getChildHtml('cms_block.shops_listing_banner')): ?>
    <div class="shops-banner">
        <?php echo $shopsBanner ?>
    </div>
<?php endif ?>

<div class="tabs-nav-shops">
    <a class="tab-nav-item active all-types" href="javascript:;" data-shop-type-id="#tab1"><?php echo $this->__('All') ?></a>
    <?php foreach (Mage::getModel('stenik_shop/shop_attribute_source_type')->getOptionArray() as $key => $value): ?>
        <?php if ($key == '') continue; ?>
        <a class="tab-nav-item" href="javascript:;" data-shop-type-id="<?php echo md5($key) ?>"><?php echo $this->__($value); ?></a>
    <?php endforeach; ?>
</div>

<div class="location-box clearfix">
    <div class="input-box">
        <input id="store-locator-address" type="text" class="input-text" placeholder="<?php echo $this->__('Enter location...') ?>">
    </div>
    <div class="radius-range">
        <label for="distance"><?php echo $this->__('Radius') ?></label>
        <input id="store-locator-range" type="range" min="1" max="100" data-orientation>
    </div>
    <a href="#" id="store-locator-reset" class="button no-background"><?php echo $this->__('Reset') ?></a>
    <a href="#" id="store-locator-search" class="button"><?php echo $this->__('Search') ?></a>
</div>
<script>
    var defaultSliderRadius = 70;

    jQuery(function($) {
        $('input[type="range"]').rangeslider({
            polyfill : false,
            rangeClass: 'rangeslider',
            handleClass: 'rangeslider__handle'
        });
        $('input[type="range"]').on("input", function(){$('.value').text(this.value)});

        $('input[type="range"]').val(defaultSliderRadius).change();
    });
</script>

<div class="shops-listing-wrapper">
    <div class="row">
        <?php if ($shopCount): ?>

            <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?libraries=places&v=3&amp;key=<?php echo $this->helper('stenik_shop')->getGoogleMapsKey() ?>"></script>
            <script src="https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/markerclusterer.js"></script>

            <script>
                var map, searchRange, searchBox;
                var markers = [];
                var markerCluster;
                function initialize() {
                    <?php
                        $shopsData = array();
                        foreach ($shopCollection as $shop) {
                            $shopsData[] = array(
                                'id' => $shop->getId(),
                                'title' => $shop->getName(),
                                'latitude' => $shop->getLatitude(),
                                'longitude' => $shop->getLongitude(),
                            );
                        }
                    ?>

                    var shopsData = <?php echo json_encode($shopsData); ?>;
                    if (!shopsData.length)
                        return;

                    var mapOptions = {
                        center: new google.maps.LatLng(shopsData[0].latitude, shopsData[0].longitude),
                        scrollwheel: false,
                        zoom: 7
                    };

                    map = new google.maps.Map(document.getElementById('gmap'), mapOptions);
                    map.setCenter({lat: 42.5026129, lng: 25.1806043});

                    var mapType = new google.maps.StyledMapType(
                        [{"featureType":"administrative","elementType":"labels.text.fill","stylers":[{"color":"#444444"}]},{"featureType":"landscape","elementType":"all","stylers":[{"color":"#f2f2f2"}]},{"featureType":"poi","elementType":"all","stylers":[{"visibility":"off"}]},{"featureType":"road","elementType":"all","stylers":[{"saturation":-100},{"lightness":45}]},{"featureType":"road.highway","elementType":"all","stylers":[{"visibility":"simplified"}]},{"featureType":"road.arterial","elementType":"labels.icon","stylers":[{"visibility":"off"}]},{"featureType":"transit","elementType":"all","stylers":[{"visibility":"off"}]},{"featureType":"water","elementType":"all","stylers":[{"color":"#9b8c62"},{"visibility":"on"},{"saturation":"0"},{"lightness":"50"}]}], { name:"Grayscale" });
                    map.mapTypes.set('grayscale', mapType);
                    map.setMapTypeId('grayscale');


                    for (var i = shopsData.length - 1; i >= 0; i--) {
                        (function() {

                            var image = '<?php echo Mage::helper('stenik_shop/shop')->getMarkerIconUrl() ?>';
                            var shopData = shopsData[i];
                            shopData.marker = new google.maps.Marker({
                                position: new google.maps.LatLng(shopData.latitude, shopData.longitude),
                                map: map,
                                animation: google.maps.Animation.DROP,
                                title: shopData.title,
                                icon: image,
                                shopDataId: shopData.id
                            });

                            if (shopData.latitude != 0 || shopData.longitude != 0) {
                                jQuery('.shops-listing .shop-box[data-shop="' + shopData.id + '"] a.view-on-map').click(function(e) {
                                    jQuery("#store-locator-reset").click();
                                    jQuery('.shops-listing .shop-box').removeClass('selected');
                                    jQuery(this).parents('.shop-box').addClass('selected');

                                    map.panTo(shopData.marker.getPosition());
                                    map.setZoom(17);
                                    map.setCenter({lat: parseFloat(shopData.latitude), lng: parseFloat(shopData.longitude)});
                                });
                            }
                            markers[shopData.id] = shopData.marker;
                        })();
                    }
                    var options = {
                        imagePath: '<?php echo str_replace('/m1.png', '/m', $this->getSkinUrl('images/stenik_shop/map/m1.png')) ?>'
                    };
                    markerCluster = new MarkerClusterer(map, markers.filter(function(){return true;}), options);

                    var input = document.getElementById('store-locator-address');
                    searchBox = new google.maps.places.Autocomplete(input, {componentRestrictions: {country: 'bg'}});

                    searchRange = new google.maps.Circle({
                        strokeColor: '#ff9600',
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: '#FFAB00 ',
                        fillOpacity: 0.35,
                        map: map,
                        radius: parseFloat(defaultSliderRadius) * 1000
                    });
                }

                google.maps.event.addDomListener(window, 'load', initialize);

                jQuery(function($) {
                     function updateRadius(circle, center, radius) {
                        var map = circle.getMap();
                        circle.setRadius(radius * 1000);
                        circle.setCenter(center);
                        map.fitBounds(circle.getBounds());

                        markers.forEach(function(item, index) {
                            var shopDetailsElement = jQuery("div[data-shop='" + markers[index].shopDataId +"']");
                            if (google.maps.geometry.spherical.computeDistanceBetween(markers[index].getPosition(), circle.getCenter()) <= circle.getRadius()) {
                                markers[index].setMap(map);
                                shopDetailsElement.show();
                                if (markerCluster) markerCluster.addMarker(markers[index], true);
                            } else {
                                shopDetailsElement.hide();
                                markers[index].setMap(null);
                                if (markerCluster) markerCluster.removeMarker(markers[index], true);
                            }

                            if(jQuery(".shop-box:visible").length <= 0) {
                                jQuery(".shop-box.no-results").show();
                            }
                        });

                        if (markerCluster) {
                            markerCluster.resetViewport();
                            markerCluster.repaint();
                            markerCluster.redraw();
                        }
                    }

                    function resetMap() {
                        jQuery('.shop-box:not(.no-results)').show();
                        jQuery('.shop-box.no-results').hide();
                        jQuery('#store-locator-address').val('');
                        jQuery('input[type="range"]').val(defaultSliderRadius).change();
                        searchRange.setRadius(0);
                        initialize();
                    }

                    jQuery("#store-locator-search").on('click', function (e) {
                        e.preventDefault();
                        if (!searchBox || !searchRange || !searchBox.getPlace())
                            return;

                        updateRadius(
                            searchRange,
                            {'lat': searchBox.getPlace().geometry.location.lat(), 'lng': searchBox.getPlace().geometry.location.lng()},
                            parseFloat(jQuery('#store-locator-range').val())
                        );
                    });

                    jQuery("#store-locator-reset").on('click', function (e) {
                        e.preventDefault();
                        resetMap();
                    });
                });

                jQuery(function($){
                    $('.shops-boxes-wrapper').jScrollPane({showArrows: false, autoReinitialise: true});

                    $('.shops-listing .drop-down-js.chose-city').each(function(){
                        var $sortOptions = $(this);
                        $sortOptions.find('.sub-options a').click(function(){
                            $('#name-search').val('').keyup();
                            var $this = $(this);
                            $this.parents('.drop-down-js').first().find('.open-item').html($this.html());
                            var cityId = $this.data('shop-city-id');
                            var $cityShops = $('.shops-listing .shop-box[data-shop-city-id="' + cityId + '"]');
                            $('.shops-listing .shop-box').not($cityShops).hide();

                            var shopIds = [];
                            $cityShops.each(function() {
                                shopIds.push($(this).data('shop'));
                            });
                            markers.forEach(function (item, index) {
                                if (shopIds.indexOf(index) >= 0) {
                                    item.setMap(map);
                                    if (markerCluster) markerCluster.addMarker(item, true);
                                }
                                else {
                                    item.setMap(null);
                                    if (markerCluster) markerCluster.removeMarker(item, true);
                                }
                            });

                            if ($this.parent().hasClass('all-cities')) {
                                $('.shops-listing .shop-box').show();
                                $cityShops = $('.shops-listing .shop-box');
                                $cityShops.each(function() {
                                    shopIds.push($(this).data('shop'));
                                });
                                markers.forEach(function (item, index) {
                                    if (shopIds.indexOf(index) >= 0) {
                                        item.setMap(map);
                                        if (markerCluster) markerCluster.addMarker(item, true);
                                    }
                                });
                            }

                            if (markerCluster) {
                                markerCluster.resetViewport();
                                markerCluster.repaint();
                                markerCluster.redraw();
                            }

                            $cityShops.show();
                            $cityShops.first().click();
                        });
                    });

                    $('.tabs-nav-shops').each(function(){
                        var $sortTabs = $(this);
                        $sortTabs.find('.tab-nav-item').click(function(){
                            var $this = $(this);
                            $('.tab-nav-item').removeClass('active');
                            $this.addClass('active');
                            var shopType = $this.data('shop-type-id');
                            var $typeShops = $('.shops-listing .shop-box[data-shop-type-id*="' + shopType + '"]');
                            $('.shops-listing .shop-box').not($typeShops).hide();

                            var typeIds = [];
                            $typeShops.each(function() {
                                typeIds.push($(this).data('shop'));
                            });
                            markers.forEach(function (item, index) {
                                if (typeIds.indexOf(index) >= 0) {
                                    item.setMap(map);
                                    if (markerCluster) markerCluster.addMarker(item, true);
                                }
                                else {
                                    item.setMap(null);
                                    if (markerCluster) markerCluster.removeMarker(item, true);
                                }
                            });

                            $typeShops.show();
                            $typeShops.first().click();
                            if ($this.hasClass('all-types')) {
                                $('.shops-listing .shop-box').show();
                                markers.forEach(function (item, index) {
                                    item.setMap(map);
                                    if (markerCluster) markerCluster.addMarker(item, true);
                                });
                            }

                            if (markerCluster) {
                                markerCluster.resetViewport();
                                markerCluster.repaint();
                                markerCluster.redraw();
                            }
                        });
                    });

                    $('.shops-listing .drop-down-js.chose-city').trigger('closeselect');

                    if ($(window).width() < 599) {
                        $(".view-on-map").click(function() {
                            $('html, body').animate({
                                scrollTop: $(".shops-listing-google-map").offset().top - 50
                            }, 1000);
                        });
                    }

                    $('.responsive-back-to-top').click(function() {
                        $('body,html').animate({scrollTop:0},1000);
                    });

                    $('#name-search').on('keyup paste', function() {
                        if ($(this).val().length < 3) {
                            $('.shop-box').each(function() {
                                if ($(this).data('filtered') === 1) {
                                    $(this).data('filtered', 0).show();
                                    markers[$(this).data('shop')].setMap(map);
                                }
                            });

                            $('.shop-box.no-results').hide();
                            return;
                        }

                        var searchString = $(this).val().toLowerCase();

                        $('.shop-box:visible:not(.no-results)').each(function() {
                            var title  = $(this).find('.title').text();
                            var shopId = $(this).data('shop');

                            if (title.toLowerCase().search(searchString) >= 0) {
                                $(this).data('filtered', 0).show();
                                markers[shopId].setMap(map);
                            } else {
                                $(this).data('filtered', 1).hide();
                                markers[shopId].setMap(null);
                            }
                        });

                        if($(".shop-box:visible").length <= 0) {
                            $(".shop-box.no-results").show();
                        }
                    });
                });
            </script>

            <div class="col-xs-3 shops-listing">
                <?php if (!empty($cities)): ?>
                    <div class="toolbar">
                        <div class="drop-down chose-city drop-down-js">
                            <a class="open-item" href="javascript:;"><?php echo $this->__('Filter by location');?></a>
                            <ul class="sub-options">
                                <li class="all-cities"><a href="javascript:;"><?php echo $this->__('All');?></a></li>
                                <?php $i = 0; foreach ($cities as $city): ?>
                                    <li><a href="javascript:;" data-shop-city-id="<?php echo md5($city) ?>"><?php echo $city; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <div class="input-box">
                            <input type="text" id="name-search" class="input-text" placeholder="<?php echo $this->__('Filter by name...') ?>">
                        </div>
                    </div>
                <?php endif; ?>
                <div class="shops-boxes-wrapper">
                    <?php foreach ($shopCollection as $shop): ?>
                        <?php $types = explode(',', $shop->getType()); ?>
                        <?php
                            $dataType = '';
                            foreach ($types as $typeId) {
                                $dataType[] = md5($typeId);
                            }
                            $dataType = implode(',', $dataType)
                        ?>
                        <div class="shop-box" data-shop="<?php echo $shop->getId(); ?>" data-shop-city-id="<?php echo md5($shop->getCity()) ?>" data-shop-type-id="<?php echo $dataType ?>">
                            <a class="title" href="<?php echo $shop->getUrl() ?>"><?php echo $shop->getName() ?></a>
                            <p><?php echo $shop->getCity() ?>, <?php echo $shop->getAddress() ?></p>
                            <?php if ($shop->getMobilePhone()): ?><p><?php echo $this->__('Telephone');?>: <?php echo $shop->getMobilePhone(); ?></p><?php endif ?>
                            <p>
                                <a class="view-more" href="<?php echo $shop->getUrl() ?>"><?php echo $this->__('View more');?></a>
                                <a class="view-on-map" href="javascript:;"><?php echo $this->__('View on map');?></a>
                            </p>
                        </div>
                    <?php endforeach ?>
                    <div style="display: none;" class="shop-box no-results">
                        <p><?= __('No results found for your search criteria.') ?></p>
                    </div>
                </div>
            </div>

            <div class="col-xs-9 shops-listing-google-map">
                <div id="gmap"></div>
                <a href="javascript:;" class="button responsive-back-to-top"><?php echo $this->__('Back to top');?></a>
            </div>

        <?php else: ?>
            <p class="no-shops-found"><?php echo $this->__('No shops found') ?></p>
        <?php endif; ?>
    </div>
</div>

<?php if ($shopCount): ?>
    <script>
        (function(){
            var container = jQuery('.shops-listing');
            var storeListingWidth = container.find('.shops-listing').first().width();
            var storeBoxOuterWidth = container.find('.shops-listing .shop-box').first().outerWidth();

            var currentFilters = {};

            function storeBoxShowFromList(jqElements, filterCode) {

                var elementsToHide = container.find('.shops-listing .shop-box:visible').not(jqElements);
                if (elementsToHide.length) {
                    jQuery.when(
                        elementsToHide.css({opacity: 0, visibility: 'hidden'}).delay(350-10).hide(0)
                    ).then(function(){
                        jQuery.when(
                            jqElements.show(0).css({opacity: 1, visibility: 'visible'}).delay(10)
                        ).then(function() {
                            if (jqElements.length)
                                container.find('.noShopsFound').hide();
                            else container.find('.noShopsFound').show();
                        });
                    });
                } else {
                    jQuery.when(
                        jqElements.show(0).css({opacity: 1, visibility: 'visible'})
                    ).then(function(){
                        if (jqElements.length)
                            container.find('.noShopsFound').hide();
                        else container.find('.noShopsFound').show();
                    });
                }

            }

        })();
    </script>
<?php endif; ?>