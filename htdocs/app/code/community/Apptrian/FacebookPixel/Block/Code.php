<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */
class Apptrian_FacebookPixel_Block_Code extends Mage_Core_Block_Template
{
    /**
     * Used in code.phtml and returns needed data.
     *
     * @return array
     */
    public function getFacebookPixelData()
    {
        $data = array();
        
        $helper = Mage::helper('apptrian_facebookpixel');
        
        $data['id_data']               = $helper->getFacebookPixelId();
        $data['full_action_name']      = Mage::app()->getFrontController()
            ->getAction()->getFullActionName();
        $data['page_handles']          = $helper->getPageHandles();
        $data['page_handles_category'] = $helper->getPageHandles('category');
        $data['page_handles_product']  = $helper->getPageHandles('product');
        $data['page_handles_quote']    = $helper->getPageHandles('quote');
        $data['page_handles_order']    = $helper->getPageHandles('order');
        $data['page_handles_search']   = $helper->getPageHandles('search');
        
        return $data;
    }
    
    /**
     * Returns configuration value for Facebook Pixel.
     *
     * @return bool
     */
    public function isPixelEnabled()
    {
        return Mage::helper('apptrian_facebookpixel')->isPixelEnabled();
    }
    
    /**
     * Returns configuration value for noscript_enabled.
     *
     * @return bool
     */
    public function isBaseCodeEnabled()
    {
        return Mage::helper('apptrian_facebookpixel')->isBaseCodeEnabled();
    }
    
    /**
     * Returns configuration value for noscript_enabled.
     *
     * @return bool
     */
    public function isNoScriptEnabled()
    {
        return Mage::helper('apptrian_facebookpixel')->isNoScriptEnabled();
    }
    
    /**
     * Returns configuration value for Facebook Conversions API.
     *
     * @return bool
     */
    public function isApiEnabled()
    {
        return Mage::helper('apptrian_facebookpixel')->isApiEnabled();
    }
    
    /**
     * Returns configuration value for firing mode for Facebook Conversions API.
     *
     * @return bool
     */
    public function getFiringMode()
    {
        return Mage::helper('apptrian_facebookpixel')->getFiringMode();
    }
    
    /**
     * Returns configuration value for PageView with all.
     *
     * @return int
     */
    public function isPageViewWithAll($server = false)
    {
        return Mage::helper('apptrian_facebookpixel')->isPageViewWithAll($server);
    }
    
    /**
     * Returns category data needed for tracking.
     *
     * @return array
     */
    public function getCategoryData()
    {
        return Mage::helper('apptrian_facebookpixel')->getCategoryDataForClient();
    }
    
    /**
     * Returns product data needed for tracking.
     *
     * @return array
     */
    public function getProductData($id = 0)
    {
        return Mage::helper('apptrian_facebookpixel')->getProductData($id);
    }
    
    /**
     * Returns data needed for tracking from order object.
     *
     * @return array
     */
    public function getOrderData()
    {
        return Mage::helper('apptrian_facebookpixel')->getOrderDataForClient();
    }
    
    /**
     * Returns data needed for tracking from quote object.
     *
     * @return array
     */
    public function getQuoteData()
    {
        return Mage::helper('apptrian_facebookpixel')->getQuoteDataForClient();
    }
    
    /**
     * Returns search data needed for tracking.
     *
     * @return array
     */
    public function getSearchData()
    {
        return Mage::helper('apptrian_facebookpixel')->getSearchDataForClient();
    }
    
    /**
     * Returns configuration value for event.
     *
     * @return bool
     */
    public function isEventEnabled($event, $server = false)
    {
        return Mage::helper('apptrian_facebookpixel')->isEventEnabled($event, $server);
    }
    
    /**
     * Returns configuration value for noscript_enabled.
     *
     * @return bool
     */
    public function isApiEventEnabled($event)
    {
        return Mage::helper('apptrian_facebookpixel')->isApiEventEnabled($event);
    }
    
    /**
     * Returns configuration value for moving params outside contents.
     *
     * @return int
     */
    public function isMoveParamsOutsideContentsEnabled($server = false)
    {
        return Mage::helper('apptrian_facebookpixel')->isMoveParamsOutsideContentsEnabled($server);
    }
    
    /**
     * Returns configuration value for detect_selected_sku
     *
     * @return bool
     */
    public function isDetectSelectedSkuEnabled($productType, $server = false)
    {
        return Mage::helper('apptrian_facebookpixel')->isDetectSelectedSkuEnabled($productType, $server);
    }
    
    /**
     * Returns price decimal sign
     *
     * @return string
     */
    public function getPriceDecimalSymbol()
    {
        return Mage::helper('apptrian_facebookpixel')->getPriceDecimalSymbol();
    }
    
    /**
     * Returns flag based on "Stores > Cofiguration > Sales > Tax
     * > Price Display Settings > Display Product Prices In Catalog"
     * Returns 0 or 1 instead of 1, 2, 3.
     *
     * @return int
     */
    public function getDisplayTaxFlag()
    {
        return Mage::helper('apptrian_facebookpixel')->getDisplayTaxFlag();
    }
    
    /**
     * Returns data for CompleteRegistration event.
     *
     * @param int $customerId
     * @return array
     */
    public function getDataForCompleteRegistrationEvent($customerId = 0)
    {
        return Mage::helper('apptrian_facebookpixel')->getDataForClientCompleteRegistrationEvent($customerId);
    }
    
    /**
     * Retruns flag for Data Processing Options.
     * (The 1 for enabled and 0 for disabled.)
     *
     * @return int
     */
    public function isDataProcessingEnabled()
    {
        return Mage::helper('apptrian_facebookpixel')->isDataProcessingEnabled();
    }
    
    /**
     * Returns array of Data Processing Options.
     *
     * @return array
     */
    public function getDpo()
    {
        return Mage::helper('apptrian_facebookpixel')->getDpo();
    }
    
    /**
     * Retruns country id for Data Processing Options.
     *
     * @return int
     */
    public function getDpoCountry()
    {
        return Mage::helper('apptrian_facebookpixel')->getDpoCountry();
    }
    
    /**
     * Retruns state id for Data Processing Options.
     *
     * @return int
     */
    public function getDpoState()
    {
        return Mage::helper('apptrian_facebookpixel')->getDpoState();
    }
    
    /**
     * Returns category ID marker.
     *
     * @return string
     */
    public function getCategoryIdMarker()
    {
        $currentCategory = Mage::registry('current_category');
        
        if ($currentCategory) {
            return 'var apptrianFacebookPixelCategoryId=' . $currentCategory->getId() . ';';
        }
        
        return '';
    }
    
    /**
     * Returns product ID marker.
     *
     * @return string
     */
    public function getProductIdMarker()
    {
        $currentProduct = Mage::registry('current_product');
        
        if ($currentProduct) {
            return 'var apptrianFacebookPixelProductId=' . $currentProduct->getId() . ';';
        }
        
        return '';
    }
    
    /**
     * Returns search marker.
     *
     * @return string
     */
    public function getSearchMarker()
    {
        $searchHandles = Mage::helper('apptrian_facebookpixel')
            ->getPageHandles('search');
        $action        = Mage::app()->getFrontController()
            ->getAction()->getFullActionName();
        
        if (in_array($action, $searchHandles)) {
            return 'var apptrianFacebookPixelSearch=1;';
        }
        
        return '';
    }
    
    /**
     * Returns url marker.
     *
     * @return string
     */
    public function getUrlMarker()
    {
        // Do not show marker onregistration, quote and order pages
        $regHandle    = 'customer_account_create';
        $quoteHandles = Mage::helper('apptrian_facebookpixel')
            ->getPageHandles('quote');
        $orderHandles = Mage::helper('apptrian_facebookpixel')
            ->getPageHandles('order');
        $action       = Mage::app()->getFrontController()
            ->getAction()->getFullActionName();
        
        if ($action == $regHandle
            || in_array($action, $quoteHandles)
            || in_array($action, $orderHandles)
        ) {
            return '';
        } else {
            $currentUrl = Mage::helper('apptrian_facebookpixel')->getCurrentUrl();
            return 'var apptrianFacebookPixelUrl="' . $currentUrl . '";';
        }
    }
}
