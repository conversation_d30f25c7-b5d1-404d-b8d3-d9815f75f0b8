<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */
class Apptrian_FacebookPixel_Model_Config_Ident
{
    const IDENT_PRODUCT            = '1';
    const IDENT_PRODUCT_AND_PARENT = '3';
    
    protected $_options;
    
    public function toOptionArray()
    {
        if (!$this->_options) {
            $this->_options[] = array(
                'value' => self::IDENT_PRODUCT,
                'label' => Mage::helper('apptrian_facebookpixel')
                    ->__('Product SKU as (id)')
            );
            $this->_options[] = array(
                'value' => self::IDENT_PRODUCT_AND_PARENT,
                'label' => Mage::helper('apptrian_facebookpixel')
                    ->__('Product SKU as (id) and Parent SKU as (item_group_id)')
            );
        }
        
        return $this->_options;
    }
}
