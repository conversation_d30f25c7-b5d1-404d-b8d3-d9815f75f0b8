<?php
/**
 * aheadWorks Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://ecommerce.aheadworks.com/AW-LICENSE.txt
 *
 * =================================================================
 *                 MAGENTO EDITION USAGE NOTICE
 * =================================================================
 * This software is designed to work with Magento community edition and
 * its use on an edition other than specified is prohibited. aheadWorks does not
 * provide extension support in case of incorrect edition use.
 * =================================================================
 *
 * @category   AW
 * @package    AW_Autorelated
 * @version    2.5.0
 * @copyright  Copyright (c) 2010-2012 aheadWorks Co. (http://www.aheadworks.com)
 * @license    http://ecommerce.aheadworks.com/AW-LICENSE.txt
 */
?><?php
$abstractBlock = $this->helper('awautorelated')->getAbstractProductBlock();
$collection = $this->getCollection();

if ($this->getPosition() == AW_Autorelated_Model_Source_Position::INSTEAD_NATIVE_RELATED_BLOCK) {

    $this->iterateBlock();

    if ($collection && $collection->getSize()) {
        $this->markAsShowed();
    } elseif ($this->showNativeBlock()) {
        $this->markAsShowed();
        echo $this->getParent()->getChildHtml('catalog-product-related');
    }
}
?>
<?php if ($collection && $collection->getSize()) : ?>
        <div class="block block-related aw-arp-block aw-arp-block-<?php echo $this->getData('id') ?>">
            <div class="block-title">
                <h2><?php echo $this->htmlEscape($this->getData('name')) ?></h2>
            </div>
            <div class="block-content aw-arp-block-content">
                <ul>
                    <?php foreach ($collection as $product) : ?>
                        <li class="aw-arp-item">
                            <a href="<?php echo $abstractBlock->getProductUrl($product) ?>"><img src="<?php echo $this->helper('catalog/image')->init($product, 'thumbnail')->resize(75) ?>" alt="<?php echo $this->htmlEscape($product->getName()) ?>" width="75" height="75" /></a><br />
                            <a href="<?php echo $abstractBlock->getProductUrl($product) ?>"><?php echo $this->htmlEscape($product->getName()) ?></a><br />
                            <?php echo $abstractBlock->getPriceHtml($product, true, '-related') ?>
                            <?php if ($product->isSaleable()) : ?>
                              <div class="aw-arp-addtocart">
                                <p>
                                    <a class="link-cart" href="javascript:setLocation('<?php echo $abstractBlock->getAddToCartUrl($product) ?>');"><?php echo $this->__('Add to Cart') ?></a>
                                </p>
                           </div>
                            <?php else : ?>
                                <p class="availability out-of-stock"><span><?php echo $this->__('Out of stock') ?></span></p>
                            <?php endif ?>
                            <?php if ($this->helper('wishlist')->isAllow()) : ?>
                                <a href="javascript:setLocation('<?php echo $abstractBlock->getAddToWishlistUrl($product) ?>')" class="link-wishlist"><?php echo $this->__('Add to Wishlist') ?></a>
                            <?php endif; ?>
                        </li>
                    <?php endforeach ?>
                </ul>
            </div>
        </div>
<?php endif;?>