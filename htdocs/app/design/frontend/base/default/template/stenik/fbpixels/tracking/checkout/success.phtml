<?php
    $helper = $this->helper('stenik_fbpixels');

    if($helper->isEventHidden('purchase'))
        return;

    $product_skus = $this->getOrderedProductSkus();
    if (!$product_skus || !is_array($product_skus))
        return;

    $order = Mage::getModel('sales/order')->loadByIncrementId(Mage::get<PERSON><PERSON><PERSON>('checkout/session')->getLastRealOrderId());

    $amount = $order->getBaseGrandTotal() - $order->getShippingAmount() - $order->getBaseTaxAmount();

    $productSkus = '';
    foreach ($product_skus as $sku) {
        if($productSkus != '')
            $productSkus .= ',';

        $productSkus .= '"'.$sku.'"';
    }
?>
<script>
fbq('track', 'Purchase', {
    value: '<?php echo $amount; ?>',
    currency: '<?php echo $order->getOrderCurrencyCode(); ?>',
    content_type: 'product',
    content_ids: [<?php echo $productSkus; ?>],
    num_items: '<?php echo $order->getTotalItemCount(); ?>'
});
</script>
