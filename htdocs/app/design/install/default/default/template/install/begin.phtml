<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Installation start page
 *
 * @see Mage_Install_Block_Begin
 */
?>
<div class="page-head">
    <h3><?php echo $this->__("Welcome to Magento's Installation Wizard") ?>!</h3>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<?php if(count($this->getMessagesBlock()->getMessages())==0): ?>
<form action="<?php echo $this->getPostUrl() ?>" method="post">
<div style="height:20em; border:1px solid #ccc; margin-bottom:8px; padding:5px; background:#fff; overflow: auto; overflow-x:hidden; overflow-y:scroll;">
<?php echo $this->getLicenseHtml() ?>
</div>

<script type="text/javascript">
//<![CDATA[
function changeSubmitStatus(obj) {
    $('submitButton').disabled = !obj.checked;
    if(obj.checked){
        $('submitButton').setStyle({'background':'#f18200','borderColor':'','cursor':'pointer','color':'#C73615'});
    }
    else{
        $('submitButton').setStyle({'background':'#bbb','borderColor':'#bbb','cursor':'default','color':'white'});
    }
}
//]]>
</script>
    <p>
        <input type="checkbox" name="agree" value="1" id="agree" onclick="changeSubmitStatus(this)" class="checkbox" />
        <label for="agree"><?php echo $this->__('I agree to the above terms and conditions.') ?></label>
    </p>
    <div class="button-set">
        <button class="form-button" type="submit" id="submitButton" disabled="disabled"><span><?php echo $this->__('Continue') ?></span></button>
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
    changeSubmitStatus($('agree'));
//]]>
</script>
<?php endif ?>
