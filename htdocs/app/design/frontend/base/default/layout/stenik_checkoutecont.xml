<?xml version="1.0"?>
<layout>
    <checkout_onepage_index>
        <update handle="stenik_checkout_checkout_shipping_method_prechoose"/>
        <update handle="stenik_checkout_partial_billing_address_update"/>

        <reference name="checkout.onepage.billing.additional_address_fields">
            <block type="extensa_econt/checkout_shipping_econt" name="extensa_econt">
                <action method="setTemplate" ifconfig="carriers/extensa_econt/active">
                    <template>stenik/checkoutecont/extensa/econt/checkout/shipping/econt.phtml</template>
                </action>
            </block>
        </reference>

        <reference name="checkout.onepage.shipping_method.prechoose">
            <action method="setMethodUsedAddressFields"><code>extensa_econt_econt_door</code><value>econt-door-address-fields</value></action>
            <action method="setMethodUsedAddressFields"><code>extensa_econt_econt_office</code><value>econt-office-address-fields</value></action>
        </reference>
    </checkout_onepage_index>

    <stenik_checkout_partial_billing_address_update>
        <reference name="checkout.onepage.section.billing">
            <action method="addUpdateSectionGroupsOnFilledFields">
                <fields>
                    <extensa_econt_office_city_id>extensa_econt:office_city_id</extensa_econt_office_city_id>
                    <extensa_econt_office_id>extensa_econt:office_id</extensa_econt_office_id>
                    <!-- <extensa_econt_office_code>extensa_econt:office_code</extensa_econt_office_code> -->
                    <extensa_econt_city>extensa_econt:city</extensa_econt_city>
                    <extensa_econt_city_id>extensa_econt:city_id</extensa_econt_city_id>
                    <extensa_econt_post_code>extensa_econt:post_code</extensa_econt_post_code>
                    <extensa_econt_quarter>extensa_econt:quarter</extensa_econt_quarter>
                    <extensa_econt_quarter_id>extensa_econt:quarter_id</extensa_econt_quarter_id>
                    <extensa_econt_other>extensa_econt:other</extensa_econt_other>
                    <extensa_econt_street>extensa_econt:street</extensa_econt_street>
                    <extensa_econt_street_id>extensa_econt:street_id</extensa_econt_street_id>
                    <extensa_econt_street_num>extensa_econt:street_num</extensa_econt_street_num>
                    <extensa_econt_street_bl>extensa_econt:street_bl</extensa_econt_street_bl>
                    <extensa_econt_street_vh>extensa_econt:street_vh</extensa_econt_street_vh>
                    <extensa_econt_street_et>extensa_econt:street_et</extensa_econt_street_et>
                    <extensa_econt_street_ap>extensa_econt:street_ap</extensa_econt_street_ap>
                </fields>
            </action>
        </reference>
    </stenik_checkout_partial_billing_address_update>

    <customer_address_form>
        <update handle="stenik_checkout_customer_address_prechoose"/>
        <reference name="head">
            <!-- <action method="addItem"><type>skin_css</type><name>css/extensa/econt/styles.css</name></action> -->
            <action method="addJs"><script>prototype/window.js</script></action>
            <action method="addItem"><type>js_css</type><name>prototype/windows/themes/default.css</name></action>
        </reference>

        <reference name="customer.address.edit.shipping_methods">
            <action method="setMethodUsedAddressFields"><code>extensa_econt_econt_door</code><value>econt-door-address-fields</value></action>
            <action method="setMethodUsedAddressFields"><code>extensa_econt_econt_office</code><value>econt-office-address-fields</value></action>
        </reference>

        <reference name="customer.address.edit.custom_address_fields">
            <block type="extensa_econt/checkout_shipping_econt" name="extensa_econt">
                <action method="setTemplate" ifconfig="carriers/extensa_econt/active">
                    <template>stenik/checkoutecont/extensa/econt/checkout/shipping/econt.phtml</template>
                </action>
            </block>
        </reference>
    </customer_address_form>
</layout>