<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2013 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="box-account box-recent">
<?php $_orders = $this->getOrders(); ?>
    <div class="box-head">
        <h2><?php echo $this->__('Recent Orders') ?></h2>
        <?php if( sizeof($_orders->getItems()) > 0 ): ?><a href="<?php echo $this->getUrl('sales/order/history') ?>"><?php echo $this->__('View All') ?></a><?php endif; ?>
    </div>
<?php if( sizeof($_orders->getItems()) > 0 ): ?>
    <table class="data-table" id="my-orders-table">
        <thead>
            <tr>
                <th><?php echo $this->__('Order #') ?></th>
                <th><?php echo $this->__('Date') ?></th>
                <th><?php echo $this->__('Ship To') ?></th>
                <th><span class="nobr"><?php echo $this->__('Order Total') ?></span></th>
                <th><?php echo $this->__('Status') ?></th>
                <th>&nbsp;</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($_orders as $_order): ?>
                <tr>
                    <td><?php echo $_order->getRealOrderId() ?></td>
                    <td><span class="nobr"><?php echo $this->formatDate($_order->getCreatedAtStoreDate()) ?></span></td>
                    <td><?php echo $_order->getShippingAddress() ? $this->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
                    <td><?php echo $_order->formatPrice($_order->getGrandTotal()) ?></td>
                    <td><em><?php echo $_order->getStatusLabel() ?></em></td>
                    <td class="a-center">
                        <span class="nobr">
                        <a href="<?php echo $this->getViewUrl($_order) ?>"><?php echo $this->__('View Order') ?></a>
                        <?php if ($this->helper('sales/reorder')->canReorder($_order)) : ?>
                            <span class="separator">|</span> <a href="<?php echo $this->getReorderUrl($_order) ?>" class="link-reorder"><?php echo $this->__('Reorder') ?></a>
                        <?php endif ?>
                        </span>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <script type="text/javascript">decorateTable('my-orders-table')</script>
<?php else: ?>
    <p><?php echo $this->__('You have placed no orders.'); ?></p>
<?php endif; ?>
</div>
