<?php
    if (Mage::getStoreConfig(Ebizmarts_MailChimp_Model_Config::GENERAL_ACTIVE, $this->_getStoreId())
        && Mage::getStoreConfig(Ebizmarts_MailChimp_Model_Config::ENABLE_POPUP, $this->_getStoreId())): ?>
    <div id="email" style="text-align:right; display:none">
        <div id="popup-heading">
            <h2><?php echo $this->quoteEscape($this->_popupHeading()); ?></h2>
        </div>
        <div id="popup-form-content">
            <div id="popup-text">
                <p><?php echo $this->quoteEscape($this->_popupMessage()); ?></p>
            </div>

            <p><span id='email_error_msg' class="email_error" style="display:none">&nbsp;</span></p>

            <div id="popup-form-data">
                <p>
                    <span class="required"></span>
                    <span class="email_label">Email:</span>
                    <span class="email_input"><input type="text"/></span>
                </p>

                <?php if(Mage::getStoreConfig(Ebizmarts_MailChimp_Model_Config::POPUP_FNAME, $this->_getStoreId())): ?>
                <p>
                    <span class="fname_label" style="margin-right: 5px">First Name:</span>
                    <span class="fname_input"><input type="text"/></span>
                </p>
                <?php endif;
                if(Mage::getStoreConfig(Ebizmarts_MailChimp_Model_Config::POPUP_LNAME, $this->_getStoreId())): ?>
                <p>
                    <span class="lname_label" style="margin-right: 5px">Last Name:</span>
                    <span class="lname_input"><input type="text"/></span>
                </p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        <?php if(!Mage::getSingleton('customer/session')->isLoggedIn()): ?>
        if (navigator.cookieEnabled) {
            <?php if(Mage::getSingleton('core/cookie')->get('email') !== null):
                if(Mage::getSingleton('core/cookie')->get('email') != 'none'):
                    Print($this->jsQuoteEscape(
                        json_decode($this->_handleCookie())
                    )
                    ); ?>
                <?php endif; ?>
            <?php elseif($this->_canCancel()): ?>
            document.observe('keydown', function (evt) {
                if (evt.keyCode == 27) {
                    win.cancelCallback();
                }
            });
            <?php endif; ?>
            document.observe('keydown', function (evt) {
                if (evt.keyCode == 13) {
                    win.okCallback();
                }
            });
            var cookieStored = emailCookieExists();
            if (!cookieStored && $('popupDialog') == undefined) {
                var popupWidth =
                    <?php
                        Print($this->jsQuoteEscape(
                            json_decode(
                                Mage::getStoreConfig(
                                    Ebizmarts_MailChimp_Model_Config::POPUP_WIDTH,
                                    $this->_getStoreId()
                                )
                            )
                        ));
                    ?>*window.innerWidth/100;
                var popupHeight =
                    <?php
                        Print($this->jsQuoteEscape(
                            json_decode(
                                Mage::getStoreConfig(
                                    Ebizmarts_MailChimp_Model_Config::POPUP_HEIGHT,
                                    $this->_getStoreId()
                                )
                            )
                        ));
                    ?>*window.innerHeight/100;
                var win = Dialog.confirm($('email').innerHTML, {
                    className: "popup",
                    id: "popupDialog",
                    width: popupWidth,

                    zIndex: 2001,
                    okLabel: '<?php echo $this->quoteEscape($this->__('Confirm')) ?>',
                    <?php if(!$this->_canCancel()): ?>closeOnEsc: false,
                    <?php else: ?>
                    cancelLabel: '<?php echo $this->quoteEscape($this->__('Close')) ?>',
                    onCancel: function (win) {
                        <?php
                            if(Mage::getSingleton('core/cookie')->get('counter') !== null):
                            //check cookie counter for customer id and check actual counter
                                $counter = Mage::getSingleton('core/cookie')->get('counter');
                                //if(counter en config == counter en db){
                                if(($counter+1) >= Mage::getStoreConfig(
                                    Ebizmarts_MailChimp_Model_Config::POPUP_INSIST,
                                    Mage::app()->getStore()->getId()
                                )):
                        ?>
                        Mage.Cookies.clear('counter');
                        //document.cookie = 'counter=; expires=Thu, 01 Jan 1970 00:00:01 GMT';
                        createCookie(
                            'email',
                            'none',
                            <?php Print($this->jsQuoteEscape(
                                json_encode(
                                    Mage::getStoreConfig(
                                        Ebizmarts_MailChimp_Model_Config::POPUP_COOKIE_TIME,
                                        $this->_getStoreId()
                                    )
                                )
                            ));
                            ?>);
                        <?php else: ?>
                        createCookie(
                            'counter',
                            <?php Print($this->jsQuoteEscape(
                                json_encode((1+$counter))
                            )
                            ); ?>,
                            <?php Print($this->jsQuoteEscape(
                                json_encode(
                                    Mage::getStoreConfig(
                                        Ebizmarts_MailChimp_Model_Config::POPUP_COOKIE_TIME,
                                        $this->_getStoreId()
                                    )
                                )
                            ));
                            ?>);
                        <?php endif; ?>
                        <?php else:
                        //create cookie counter in 0
                            if (Mage::getStoreConfig(
                                Ebizmarts_MailChimp_Model_Config::POPUP_INSIST,
                                Mage::app()->getStore()->getId()
                            ) == 1):
                        ?>
                        createCookie(
                            'email',
                            'none',
                            <?php Print($this->jsQuoteEscape(
                                json_encode(
                                    Mage::getStoreConfig(
                                        Ebizmarts_MailChimp_Model_Config::POPUP_COOKIE_TIME,
                                        $this->_getStoreId()
                                    )
                                )
                            ));
                            ?>);
                        <?php else: ?>
                        createCookie(
                            'counter',
                            1,
                            <?php Print($this->jsQuoteEscape(
                                json_encode(
                                    Mage::getStoreConfig(
                                        Ebizmarts_MailChimp_Model_Config::POPUP_COOKIE_TIME,
                                        $this->_getStoreId()
                                    )
                                )
                            ));
                            ?>);
                        <?php endif; ?>
                        <?php endif; ?>
                    },
                    <?php endif; ?>
                    onOk: function (win) {
                        var inputFieldEmail = $$('.email_input input[type=text]').first();
                        var inputFieldFName = $$('.fname_input input[type=text]').first();
                        var inputFieldLName = $$('.lname_input input[type=text]').first();
                        var email = inputFieldEmail.value;
                        var fname = '';
                        var lname = '';
                        if(inputFieldFName) {
                            fname = inputFieldFName.value;
                        }
                        if(inputFieldLName) {
                            lname = inputFieldLName.value;
                        }
                        if (validateEmail(email)) {
                            createCookie('email',email + '/' + fname + '/' + lname, 365);
                            <?php if($this->_modalSubscribe()): ?>
                            createCookie('subscribe','true', 365);
                            location.reload();
                            <?php else: ?>
                            win.close();
                            <?php endif ?>
                        } else {
                            $('email_error_msg').innerHTML = '<?php
                                    echo $this->quoteEscape(
                                        $this->__('Not a valid e-mail address')
                                    );
                                ?>';
                            $('email_error_msg').setStyle({color: '#F00'});
                            $('email_error_msg').show();
                            Windows.focusedWindow.updateHeight();
                            new Effect.Shake(Windows.focusedWindow.getId());
                        }
                    }
                });
                <?php if(!$this->_canCancel()): ?>
                $$('.cancel_button').each(function (element) {
                    element.remove();
                });
                <?php else: ?>
                Event.observe(window, 'load', function () {
                    $('overlay_modal').observe('click', function () {
                        win.close();
                        win.cancelCallback();
                    });
                });
                <?php endif; ?>
            }
        }
        <?php endif; ?>
        function validateEmail(email) {
            var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(email);
        }
        function emailCookieExists() {
            if(Mage.Cookies.get('email')) {
                return true
            }
            else {
                return false
            }
        }
        function createCookie(name, value, expirationInDays) {
            var now = new Date();
            //[(1 * 365 * 24 * 60) * 60000] == 1 year  -- (Years * Days * Hours * Minutes) * 60000
            var expire = new Date(now.getTime() + (expirationInDays * 24 * 60) * 60000);
            //document.cookie = cookie + '; expires=' + expire + '; path=/';
            Mage.Cookies.expires = expire;
            Mage.Cookies.set(name,value);
        }
    </script>
    <?php endif; ?>
