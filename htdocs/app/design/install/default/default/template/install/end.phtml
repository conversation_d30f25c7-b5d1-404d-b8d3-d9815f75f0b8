<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Installation end page
 *
 * @see Mage_Install_Block_End
 */
?>
<div class="page-head">
    <h3><?php echo $this->__("You're All Set") ?>!</h3>
</div>
<?php if ($this->getIframeSourceUrl()): ?>
    <iframe id="iframe_box" src="<?php echo $this->getIframeSourceUrl() ?>" frameborder="0" width="650" height="600"></iframe>
<?php endif; ?>
<p><?php echo $this->__('Get ready to experience Open-Source eCommerce Evolved.') ?></p>
<?php if($this->getEncryptionKey()): ?>
<p><?php echo $this->__('Before you continue to your store, please make a note of your encryption key (Magento uses it to encrypt passwords, credit cards and more).') ?></p>
<p><strong><big><?php echo $this->getEncryptionKey() ?></big></strong></p>
<p><?php echo $this->__('(Make sure you keep it in a safe place.)') ?></p>
<?php endif; ?>
<div class="button-set">
    <button class="form-button" onclick="window.open('<?php echo $this->getUrl('') ?>')" type="button"><?php echo $this->__('Go to Frontend') ?></button>&nbsp;&nbsp;
    <button class="form-button" onclick="window.open('<?php echo $this->getUrl('adminhtml') ?>')" type="button"><?php echo $this->__('Go to Backend') ?></button>
</div>
