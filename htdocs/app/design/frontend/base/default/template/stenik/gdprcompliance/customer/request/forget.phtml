<div class="page-title">
    <h1><?php echo $this->helper('stenik_gdprcompliance')->getCustomerAccountNavigationTitle(); ?></h1>
</div>
<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>

<?php $helper = $this->helper('stenik_gdprcompliance/request_forget'); ?>

<form action="<?php echo $this->getUrl('stenik_gdprcompliance/request/forgetPost') ?>" method="post" id="form-validate" autocomplete="off">
    <div class="fieldset">
        <?php echo $this->getBlockHtml('formkey')?>
        <h2 class="legend"><?php echo $this->__('Request to be forgotten') ?></h2>
        <ul class="form-list">
            <li class="fields">
				<?php echo $helper->getAdditionalInformation(); ?>
            </li>
            <li class="control">
                <input type="checkbox" name="gdpr_forget" id="gdpr_forget" value="1" title="<?php echo $this->htmlEscape(strip_tags($helper->getAgreement())); ?>" class="checkbox required-entry" /><label for="gdpr_forget"><?php echo $helper->getAgreement(); ?> <em>*</em></label>
            </li>
        </ul>
    </div>
    <div class="buttons-set">
        <p class="required"><?php echo $this->__('* Required Fields') ?></p>
        <p class="back-link"><a href="<?php echo Mage::getUrl('stenik_gdprcompliance/customer/dashboard'); ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
        <button type="submit" title="<?php echo $this->__('Save') ?>" class="button"><span><span><?php echo $this->__('Save') ?></span></span></button>
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
    var dataForm = new VarienForm('form-validate', true);
//]]>
</script>