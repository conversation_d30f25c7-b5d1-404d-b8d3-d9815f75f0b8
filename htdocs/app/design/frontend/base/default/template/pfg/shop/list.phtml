<?php $shopCollection = $this->getShopCollection() ?>
<?php $shopCount      = $shopCollection->getSize() ?>
<?php
    $cities = array();
    foreach ($shopCollection as $shop) {
        if (!in_array($shop->getCity(), $cities))
            $cities[] = $shop->getCity();
    }
?>
<h1><?php echo $this->__('Stores') ?></h1>
<div class="shopsContainerWrapper mb-4">
    <div class="row">
        <?php if ($shopCount): ?>
            <div class="shopsLeftCol col-12 col-sm-4">
                <div class="shopsListing">
                    <?php foreach ($shopCollection as $shop): ?>
                        <div class="storeBox" data-shop="<?php echo $shop->getId(); ?>" data-shop-city-id="<?php echo md5($shop->getCity()) ?>" itemscope itemtype="http://schema.org/LocalBusiness">
                            <p class="storeTitle" itemprop="name"><?php echo $shop->getName(); ?></p>
                        </div>
                    <?php endforeach ?>
                </div>
            </div>
            <div class="shopsMap col-12 col-sm-8 mb-4 mb-sm-0">
                <div class="row">
                    <div class="col-12 col-sm-6 shop-info-container d-none my-4 mt-sm-0">
                        <div class="row">
                            <div class="col">
								<?php foreach ($shopCollection as $shop): ?>
                                    <?php if ($shop->getVirtualTour()): ?>
                                        <div class="position-relative shop-virtual-tour d-none" data-shop="<?php echo $shop->getId(); ?>">
                                            <iframe src="<?php echo $shop->getVirtualTour() ?>" width="100%" height="435" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
	                            <?php echo $this->getChildHtml('shop.list.message'); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm shop-info-container ">
                        <div id="map-canvas"></div>
                        <?php foreach ($shopCollection as $shop): ?>
                            <div class="activeStore" data-shop="<?php echo $shop->getId(); ?>" style="display: none" itemscope itemtype="http://schema.org/Organization">
                                <div class="activeStoreInfo">
                                    <p class="storeName h3 text-primary mb-1"><?php echo $shop->getName(); ?></p>
                                    <div class="store-address">
	                                    <?php echo $shop->getAddress(); ?>
                                    </div>
                                    <?php if ($shopPhone = $shop->getPhone()): ?>
                                        <p class="mb-1"><?php echo $this->__('Phone:') ?><?php echo $shopPhone ?></p>
                                    <?php endif ?>
                                    <?php if ($shopMobilePhone = $shop->getMobilePhone()): ?>
                                        <p class="mb-1"><?php echo $this->__('Mobile:') ?><?php echo $shopMobilePhone ?></p>
                                    <?php endif ?>
                                    <a class="my-1 d-block" href="mailto:<?php echo $shop->getEmail(); ?>"><?php echo $shop->getEmail(); ?></a>
	                                <?php $workDays = Mage::helper('core/unserializeArray')->unserialize($shop->getBusinessHours()); ?>
	                                <?php if ($shopWorkTime = $shop->getBusinessHoursAsText()): ?>
                                        <div class="workTime mb-1"><?php echo $shopWorkTime ?></div>
	                                <?php endif ?>
	                                <?php if($workDays): ?>
                                        <div class="row">
                                            <div class="schedule-wrapper col-12 col-sm-10 col-md-8">
                                                <p class="h3 mb-1"><?php echo $this->__('Work hours'); ?></p>
                                                <table class="table table-sm mb-1">
                                                    <tbody>
					                                <?php foreach($workDays as $weekday => $workhours): ?>
                                                        <tr>
                                                            <td class="border-0 p-0" width="50%"><?php echo $this->__($weekday); ?></td>
                                                            <td class="border-0 p-0">
								                                <?php foreach ($workhours as $label => $hour): ?>
									                                <?php echo $hour; ?>
									                                <?php echo ($label === 'from')? '-' : '' ?>
								                                <?php endforeach; ?>
								                                <?php echo $this->__('hours'); ?>
                                                            </td>
                                                        </tr>
					                                <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
	                                <?php endif; ?>
                                    <div class="position-relative">
                                        <a href="<?php echo $shop->getUrl() ?>" class="btn btn-primary py-2 px-5 rounded-0 viewStoreLink">
                                            <?php echo $this->__('See more about store') ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <script type="text/javascript" src="<?php echo Mage::helper('praktis_catalog/google')->getGoogleMapsAPIUrl(); ?>"></script>
            <script>
                function initialize() {
                    <?php
                        $shopsData = array();
                        foreach ($shopCollection as $shop) {
                            $shopsData[] = array(
                                'id' => $shop->getId(),
                                'title' => $shop->getName(),
                                'latitude' => $shop->getLatitude(),
                                'longitude' => $shop->getLongitude(),
                                'city' => $shop ->getCity(),
                                'phone' => $shop->getPhone(),
                                'workTime' => $shop->getBusinessHoursAsText()
                            );
                        }
                    ?>

                    var shopsData = <?php echo json_encode($shopsData); ?>;

                    if (!shopsData.length) {
                        return;
                    }


                   var styles = [
                       {"featureType":"all","elementType":"labels","stylers":[{"visibility":"on"}]},
                       {"featureType":"all","elementType":"labels.text.fill","stylers":[{"saturation":36},{"color":"#000000"},{"lightness":40}]},
                       {"featureType":"all","elementType":"labels.text.stroke","stylers":[{"visibility":"on"},{"color":"#000000"},{"lightness":16}]},
                       {"featureType":"all","elementType":"labels.icon","stylers":[{"visibility":"on"}]},
                       {"featureType":"administrative","elementType":"geometry.fill","stylers":[{"color":"#000000"}]},
                       {"featureType":"administrative","elementType":"geometry.stroke","stylers":[{"color":"#f58220"}]},
                       {"featureType":"administrative.country","elementType":"labels.text.fill","stylers":[{"color":"#c4c4c4"}]},
                       {"featureType":"administrative.locality","elementType":"labels.text.fill","stylers":[{"color":"#c4c4c4"}]},
                       {"featureType":"administrative.neighborhood","elementType":"labels.text.fill","stylers":[{"visibility":"off"}]},
                       {"featureType": "administrative.province", "elementType": "geometry", "stylers": [{"visibility": "off"}]},
                       {"featureType":"landscape","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":20}]},
                       {"featureType":"poi","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":21},{"visibility":"on"}]},
                       {"featureType":"poi.business","elementType":"geometry","stylers":[{"visibility":"on"}]},
                       {"featureType":"road.highway","elementType":"geometry.fill","stylers":[{"color":"#555555"}]},
                       {"featureType":"road.highway","elementType":"geometry.stroke","stylers":[{"visibility":"off"}]},
                       {"featureType":"road.highway","elementType":"labels.text.fill","stylers":[{"color":"#ffffff"}]},
                       {"featureType":"road.highway","elementType":"labels.text.stroke","stylers":[{"color":"#555555"}]},
                       {"featureType":"road.arterial","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":18}]},
                       {"featureType":"road.arterial","elementType":"geometry.fill","stylers":[{"color":"#575757"}]},
                       {"featureType":"road.arterial","elementType":"labels.text.fill","stylers":[{"color":"#ffffff"}]},
                       {"featureType":"road.arterial","elementType":"labels.text.stroke","stylers":[{"color":"#2c2c2c"}]},
                       {"featureType":"road.local","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":16}]},
                       {"featureType":"road.local","elementType":"labels.text.fill","stylers":[{"color":"#999999"}]},
                       {"featureType":"transit","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":19}]},
                       {"featureType":"water","elementType":"geometry","stylers":[{"color":"#000000"},{"lightness":17}]}
                    ];

                    var mapOptions = {
                        center: new google.maps.LatLng(shopsData[0].latitude, shopsData[0].longitude),
                        zoom: 7,
                        scrollwheel: false
                    };
                    var map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);
                    var styledMap = new google.maps.StyledMapType(styles, { name: "Styled Map" });

                    for (var i = shopsData.length - 1; i >= 0; i--) {
                        (function() {

                            var image = '<?php echo Mage::helper('pfg_shop/shop')->getMarkerIconUrl() ?>';
                            var shopData = shopsData[i];

                            shopData.marker = new google.maps.Marker({
                                position: new google.maps.LatLng(shopData.latitude, shopData.longitude),
                                map: map,
                                animation: google.maps.Animation.DROP,
                                title: shopData.title,
                                icon: image
                            });
                            jQuery('.shopsListing .storeBox[data-shop="' + shopData.id + '"]').click(function(e) {
                                jQuery('.shopsListing .storeBox').removeClass('active');
                                jQuery(this).addClass('active');
                                jQuery('.shopsMap').addClass('active');
                                if(jQuery('.shop-info-container').hasClass('d-none')) {
                                    jQuery('.shop-info-container').removeClass('d-none');
                                }

                                map.panTo(shopData.marker.getPosition());
                                map.setZoom(18);
                            });

                            google.maps.event.addListener(shopData.marker, 'click', function () { infowindow.open(map, shopData.marker); });

                        })();
                    }
                    if(window.location.hash) {
                        var shopId = window.location.hash.slice(5);
                        var $shop = jQuery('.shopsListing .storeBox[data-shop="' + shopId + '"]').first();
                        if ($shop.length) {
                            $shop.find('.storeTitle').click();
                            $shop.click();
                        }
                    }

                    map.mapTypes.set('map_style', styledMap);
                    map.setMapTypeId('map_style');
                }
                google.maps.event.addDomListener(window, 'load', initialize);
            </script>
            <div class="noShopsFound" style="display:none"><?php echo $this->__('No stores found.') ?></div>
        <?php else: ?>
            <div class="noShopsFound"><?php echo $this->__('No stores found.') ?></div>
        <?php endif; ?>
    </div>
<!--    <div class="btn btn-primary rounded-button btn-lg d-block d-sm-none button hiddenButton">--><?php //echo $this->__('Back to top') ?><!--</div>-->
</div>
<?php if ($shopCount): ?>
    <script>
        jQuery(function($){
            var allowScroll = true;
            function scrollOnClick(element1, element2) {
                $(element1).click(function() {
                    if (allowScroll) {
                        $('html,body').animate({
                            scrollTop: $(element2).offset().top - 100
                        }, 'slow');
                    }
                });
            }


            if($(window).width() < 1024) {
                scrollOnClick('.storeBox', '.shopsMap');
                scrollOnClick('.hiddenButton', '.shopsContainer');
            }

            $('.storeBox').click(function(){
                var $this = jQuery(this);
                var storeId = $this.data('shop');
                var $activeStores = jQuery('.activeStore[data-shop="' + storeId + '"]');
                jQuery('.activeStore').not($activeStores).hide();

                /* Show virtual tour also */
                jQuery('.shop-virtual-tour').addClass('d-none');
                jQuery('#map-canvas').css({"maxHeight":"200px"});
                jQuery('.shop-virtual-tour[data-shop="' + storeId + '"]').removeClass('d-none');

                $activeStores.show();
	            $('body').getNiceScroll().resize();
            });
        });
        (function(){
            var container = jQuery('.storesPage');
            var storeListingWidth = container.find('.shopsListing').first().width();
            var storeBoxOuterWidth = container.find('.shopsListing .storeBox').first().outerWidth();
            var maxStoreBoxesPerRow = Math.floor(storeListingWidth/storeBoxOuterWidth);

            var currentFilters = {};

            function storeBoxShowFromList(jqElements, filterCode) {

                var elementsToHide = container.find('.shopsListing .storeBox:visible').not(jqElements);
                if (elementsToHide.length) {
                    jQuery.when(
                        elementsToHide.css({opacity: 0, visibility: 'hidden'}).delay(350-10).hide(0)
                    ).then(function(){
                        jQuery.when(
                            jqElements.show(0).css({opacity: 1, visibility: 'visible'}).delay(10)
                        ).then(function() {
                            storeBoxesRecalcBorders();
                            if (jqElements.length)
                                container.find('.noShopsFound').hide();
                            else container.find('.noShopsFound').show();
                        });
                    });
                } else {
                    jQuery.when(
                        jqElements.show(0).css({opacity: 1, visibility: 'visible'})
                    ).then(function(){
                        storeBoxesRecalcBorders();
                        if (jqElements.length)
                            container.find('.noShopsFound').hide();
                        else container.find('.noShopsFound').show();
                    });
                }

            }

            function storeBoxesRecalcBorders() {
                var elements = container.find('.shopsListing .storeBox:visible');
                var count = elements.length;
                var counter = 0;
                elements.removeClass('noBorderR')
                        .removeClass('noBorderB')
                        .each(function(){
                            counter++;
                            if (counter%maxStoreBoxesPerRow == 0)
                                jQuery(this).addClass('noBorderR');

                            var modulo = count%maxStoreBoxesPerRow;
                            if (counter > count - ((modulo == 0 || modulo == count) ? maxStoreBoxesPerRow : modulo))
                                jQuery(this).addClass('noBorderB');
                        });
            }

            jQuery(function(){
                storeBoxesRecalcBorders();
                container.find('.toolbar a.filter').click(function(){
                    if (jQuery(this).hasClass('disabled'))
                        return false;

                    filterCode = jQuery(this).data('shop-filter-code');

                    container.find('a.filter').each(function(){
                        if (jQuery(this).data('shop-filter-code') == filterCode)
                            jQuery(this).removeClass('selected').removeClass('active');
                    });

                    jQuery(this).addClass('active').addClass('selected');

                    if (jQuery(this).data('shops')) {
                        currentFilters[filterCode] = jQuery(this).data('shops').toString().split(',');
                        var jqSelectors            = [];
                        var shops                  = false;

                        for (var code in currentFilters) {
                            if (currentFilters[code].indexOf('all') != -1) {
                                continue;
                            }

                            if (currentFilters[code].length == 0) {
                                shops = [];
                                break;
                            }

                            if (shops === false)
                                shops = currentFilters[code];
                            else {
                                // intersect shops with currentFilters[code]
                                shops = shops.filter(function(shop){
                                    return currentFilters[code].indexOf(shop) != -1;
                                });
                            }
                        }

                        if (shops === false) {
                            jqSelectors.push('.shopsListing .storeBox');
                        } else {
                            for (var i = shops.length - 1; i >= 0; i--) {
                                jqSelectors.push('.shopsListing .storeBox[data-shop="' + shops[i] + '"]');
                            };
                        }
                        storeBoxShowFromList(container.find(jqSelectors.join(',')), filterCode);
                    }
                });
            });
        })();
    </script>
<?php endif; ?>