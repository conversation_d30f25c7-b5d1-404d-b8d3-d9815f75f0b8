<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_AjaxAddToCart_Block_Js
 */
?>


<div id="stenik-ajaxaddtocart-result" style="display: none"></div>
<div id="stenik-ajaxaddtocart-overlay" style="display: none"></div>

<?php
    $addToCartSelectors            = $this->getAllAddToCartTriggerSelectors();
    $addToCartFormButtonsSelectors = $this->getAddToCartFormTriggerSelectors();

    if (!count($addToCartSelectors)) {
        return;
    }
?>

<script>
    (function($) {
        var areFieldsValid = function($fields) {
            var $fields = $($fields);

            var areAllFieldsValid = true;

            $fields.each(function() {
                var fieldClasses = this.className.split(' ');
                for (var i = 0; i < fieldClasses.length; i++) {
                    try {
                        var validator = Validation.get(fieldClasses[i]);

                        if(Validation.isVisible(this) && !validator.test($F(this), this)) {
                            areAllFieldsValid = false;
                            return false;
                        }
                    } catch(e) {
                        areAllFieldsValid = false;
                        return false;
                    }
                }
            });

            return areAllFieldsValid;
        };

        var addToCartTrigger = function(event) {
            var $element = $(this);
            var $form = $element.closest('form');

            var data = {};
            if ($form.length) {
                if (!areFieldsValid($form.find('input,select,textarea'))) {
                    return true;
                }

                var dataArray = $form.serializeArray();

                for (var i = 0; i < dataArray.length; i++) {
                    if (typeof dataArray[i].name === 'undefined') {
                        continue;
                    }

                    if (typeof dataArray[i].value === 'undefined') {
                        dataArray[i].value = '';
                    }

                    if (dataArray[i].name.match(/\[\]$/)) {
                        // Array name

                        if (typeof data[dataArray[i].name] == 'undefined') {
                            data[dataArray[i].name] = [];
                        }

                        data[dataArray[i].name].push(dataArray[i].value);
                    } else {
                        data[dataArray[i].name] = dataArray[i].value;
                    }
                }
            }

            if (typeof data.product === 'undefined') {
                data.product = false;

                var destination = $element.data('origOnclickAttr');
                if (!destination) {
                    destination = $element.attr('href');
                }

                if (destination) {
                    var matches;
                    if (matches = destination.match(/product\/(\d+)/)) {
                        data.product = matches[1];
                    }

                    if (matches = destination.match(/form_key\/([^\/]+)/)) {
                        data.form_key = matches[1];
                    }
                }
            }

            if (!data.product) {
                return true;
            }

            if (typeof data.form_key === 'undefined') {
                data.form_key = <?php echo json_encode($this->getFormKey()) ?>
            }


            $('#stenik-ajaxaddtocart-overlay').show();

            var hideOverlay = true;
            $.ajax({
                url: <?php echo json_encode($this->getUrl('stenik_ajaxaddtocart/index/add')) ?>,
                method: 'post',
                dataType: 'json',
                data: data
            }).done(function(response) {
                if ((typeof response.update !== 'undefined') && response.update) {
                    for (var i = 0; i < response.update.length; i++) {
                        $(response.update[i].selector).replaceWith(response.update[i].html);
                    }
                }

                if ((typeof response.html !== 'undefined') && response.html) {
                    hideOverlay = false;
                    var $resultWrapper = $('#stenik-ajaxaddtocart-result');
                    $resultWrapper.html(response.html);
                    $resultWrapper.find('.closeAddToCartResult').click(function() {
                        $resultWrapper.hide();
                        $('#stenik-ajaxaddtocart-overlay').hide();
                    });
                    $(document).keyup(function(e) {
                         if (e.keyCode == 27) {
                            $resultWrapper.hide();
                            $('#stenik-ajaxaddtocart-overlay').hide();
                        }
                    });

                    $resultWrapper.show();
                }
            }).fail(function() {
                alert(<?php echo json_encode($this->__('Something went wrong. Please try again.')) ?>);
            }).always(function() {
                if (hideOverlay) {
                    $('#stenik-ajaxaddtocart-overlay').hide();
                }
            });

            if (typeof event != 'undefined') {
                Event.stop(event);
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            }

            return false;
        };

        $(<?php echo json_encode(implode(',', $addToCartSelectors)) ?>).each(function() {
            var $element = $(this);
            $element.click(addToCartTrigger.bind(this));

            var onclickAttrFunc = $element.prop('onclick');
            if (onclickAttrFunc) {
                $element.data('origOnclickAttr', $element.attr('onclick'));
                $element.removeProp('onclick');
                $element.removeAttr('onclick');
                $element.click(onclickAttrFunc);
            }
        });

        <?php if (count($addToCartFormButtonsSelectors)): ?>
            $(<?php echo json_encode(implode(',', $addToCartFormButtonsSelectors)) ?>).each(function() {
                $(this).closest('form').submit(addToCartTrigger.bind(this));
            });
        <?php endif; ?>

        $('body').on('click', <?php echo json_encode(implode(',', $addToCartSelectors)) ?>, addToCartTrigger);
    })(jQuery);
</script>