<?xml version="1.0"?>
<config>
    <modules>
        <Ebizmarts_MailChimp>
            <version>1.1.18</version>
        </Ebizmarts_MailChimp>
        <Ebizmarts_Mandrill>
            <version>1.1.18</version>
        </Ebizmarts_Mandrill>
    </modules>
    <global>
        <events>
            <newsletter_subscriber_save_before>
                <observers>
                    <mailchimp_subscribe_observer>
                        <class>mailchimp/observer</class>
                        <method>subscriberSaveBefore</method>
                    </mailchimp_subscribe_observer>
                </observers>
            </newsletter_subscriber_save_before>
            <newsletter_subscriber_save_after>
                <observers>
                    <mailchimp_subscribe_after_observer>
                        <class>mailchimp/observer</class>
                        <method>subscriberSaveAfter</method>
                    </mailchimp_subscribe_after_observer>
                </observers>
            </newsletter_subscriber_save_after>
            <newsletter_subscriber_delete_after>
                <observers>
                    <mailchimp_subscribe_delete_observer>
                        <class>mailchimp/observer</class>
                        <method>handleSubscriberDeletion</method>
                    </mailchimp_subscribe_delete_observer>
                </observers>
            </newsletter_subscriber_delete_after>
            <customer_save_after>
                <observers>
                    <mailchimp_save_after_customer>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>customerSaveAfter</method>
                    </mailchimp_save_after_customer>
                </observers>
            </customer_save_after>
            <customer_address_save_before>
                <observers>
                    <mailchimp_save_before_address>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>customerAddressSaveBefore</method>
                    </mailchimp_save_before_address>
                </observers>
            </customer_address_save_before>
            <sales_order_save_before>
                <observers>
                    <mailchimp_save_before_order>
                        <class>mailchimp/observer</class>
                        <method>orderSaveBefore</method>
                    </mailchimp_save_before_order>
                </observers>
            </sales_order_save_before>
            <sales_order_place_after>
                <observers>
                    <mailchimp_new_order>
                        <class>mailchimp/observer</class>
                        <method>newOrder</method>
                    </mailchimp_new_order>
                </observers>
            </sales_order_place_after>
            <sales_order_creditmemo_refund>
                <observers>
                    <mailchimp_creditmemo_refund>
                        <class>mailchimp/observer</class>
                        <method>newCreditmemo</method>
                    </mailchimp_creditmemo_refund>
                </observers>
            </sales_order_creditmemo_refund>
            <sales_order_creditmemo_cancel>
                <observers>
                    <mailchimp_creditmemo_cancel>
                        <class>mailchimp/observer</class>
                        <method>cancelCreditmemo</method>
                    </mailchimp_creditmemo_cancel>
                </observers>
            </sales_order_creditmemo_cancel>
            <sales_order_item_cancel>
                <observers>
                    <mailchimp_item_cancel>
                        <class>mailchimp/observer</class>
                        <method>itemCancel</method>
                    </mailchimp_item_cancel>
                </observers>
            </sales_order_item_cancel>
            <catalog_product_attribute_update_before>
                <observers>
                    <mailchimp_product_attribute_update_after>
                        <class>mailchimp/observer</class>
                        <method>productAttributeUpdate</method>
                    </mailchimp_product_attribute_update_after>
                </observers>
            </catalog_product_attribute_update_before>
            <salesrule_rule_save_after>
                <observers>
                    <mailchimp_salesrule_save_after>
                        <class>mailchimp/observer</class>
                        <method>salesruleSaveAfter</method>
                    </mailchimp_salesrule_save_after>
                </observers>
            </salesrule_rule_save_after>
            <salesrule_rule_delete_after>
                <observers>
                    <mailchimp_salesrule_delete_after>
                        <class>mailchimp/observer</class>
                        <method>salesruleDeleteAfter</method>
                    </mailchimp_salesrule_delete_after>
                </observers>
            </salesrule_rule_delete_after>
            <clean_catalog_images_cache_after>
                <observers>
                    <mailchimp_clean_catalog_images_cache_after>
                        <class>mailchimp/observer</class>
                        <method>cleanProductImagesCacheAfter</method>
                    </mailchimp_clean_catalog_images_cache_after>
                </observers>
            </clean_catalog_images_cache_after>
        </events>
        <models>
            <mailchimp>
                <class>Ebizmarts_MailChimp_Model</class>
                <resourceModel>mailchimp_mysql4</resourceModel>
            </mailchimp>
            <mailchimp_mysql4>
                <class>Ebizmarts_MailChimp_Model_Mysql4</class>
                <entities>
                    <synchbatches>
                        <table>mailchimp_sync_batches</table>
                    </synchbatches>
                    <mailchimperrors>
                        <table>mailchimp_errors</table>
                    </mailchimperrors>
                    <webhookrequest>
                        <table>mailchimp_webhook_request</table>
                    </webhookrequest>
                    <ecommercesyncdata>
                        <table>mailchimp_ecommerce_sync_data</table>
                    </ecommercesyncdata>
                    <interestgroup>
                        <table>mailchimp_interest_group</table>
                    </interestgroup>
                    <stores>
                        <table>mailchimp_stores</table>
                    </stores>
                </entities>
            </mailchimp_mysql4>
            <newsletter>
                <rewrite>
                    <subscriber>Ebizmarts_MailChimp_Model_Subscriber</subscriber>
                </rewrite>
            </newsletter>
<!--            <core>-->
<!--                <rewrite>-->
<!--                    <email_template>Ebizmarts_MailChimp_Model_Email_Template</email_template>-->
<!--                    <email_queue>Ebizmarts_MailChimp_Model_Email_Queue</email_queue>-->
<!--                </rewrite>-->
<!--            </core>-->
        </models>
        <resources>
            <mailchimp_setup>
                <setup>
                    <module>Ebizmarts_MailChimp</module>
                    <class>Mage_Eav_Model_Entity_Setup</class>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </mailchimp_setup>
            <mailchimp_write>
                <connection>
                    <use>core_write</use>
                </connection>
            </mailchimp_write>
            <mailchimp_read>
                <connection>
                    <use>core_read</use>
                </connection>
            </mailchimp_read>
        </resources>
        <blocks>
            <mailchimp>
                <class>Ebizmarts_MailChimp_Block</class>
            </mailchimp>
        </blocks>
        <helpers>
            <mailchimp>
                <class>Ebizmarts_MailChimp_Helper</class>
            </mailchimp>
        </helpers>
        <fieldsets>
            <sales_convert_quote>
                <mailchimp_abandonedcart_flag>
                    <to_order>*</to_order>
                </mailchimp_abandonedcart_flag>
                <mailchimp_campaign_id>
                    <to_order>*</to_order>
                </mailchimp_campaign_id>
                <mailchimp_landing_page>
                    <to_order>*</to_order>
                </mailchimp_landing_page>
            </sales_convert_quote>
        </fieldsets>
    </global>
    <frontend>
        <events>
            <sales_order_place_before>
                <observers>
                    <mailchimp_subscribe_checkout_before>
                        <class>mailchimp/observer</class>
                        <method>saveCampaignData</method>
                    </mailchimp_subscribe_checkout_before>
                </observers>
            </sales_order_place_before>
            <sales_quote_save_before>
                <observers>
                    <ebizmarts_mailchimp_quote_save>
                        <class>mailchimp/observer</class>
                        <method>loadCustomerToQuote</method>
                    </ebizmarts_mailchimp_quote_save>
                </observers>
            </sales_quote_save_before>
        </events>
        <translate>
            <modules>
                <Ebizmarts_MailChimp>
                    <files>
                        <default>Ebizmarts_MailChimp.csv</default>
                    </files>
                </Ebizmarts_MailChimp>
            </modules>
        </translate>
        <routers>
            <monkey>
                <use>standard</use>
                <args>
                    <module>Ebizmarts_MailChimp</module>
                    <frontName>monkey</frontName>
                </args>
            </monkey>
            <mailchimp>
                <use>standard</use>
                <args>
                    <module>Ebizmarts_MailChimp</module>
                    <frontName>mailchimp</frontName>
                </args>
            </mailchimp>
        </routers>
        <layout>
            <updates>
                <magemailchimp>
                    <file>ebizmarts/mailchimp.xml</file>
                </magemailchimp>
            </updates>
        </layout>
    </frontend>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <mailchimp after="Mage_Adminhtml">Ebizmarts_MailChimp_Adminhtml</mailchimp>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <mailchimp>
                    <file>ebizmarts/mailchimp.xml</file>
                </mailchimp>
            </updates>
        </layout>
        <translate>
            <modules>
                <Ebizmarts_MailChimp>
                    <files>
                        <default>Ebizmarts_MailChimp.csv</default>
                    </files>
                </Ebizmarts_MailChimp>
            </modules>
        </translate>
        <events>
            <core_block_abstract_prepare_layout_after>
                <observers>
                    <mailchimp_add_customer_tab>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>addCustomerTab</method>
                    </mailchimp_add_customer_tab>
                </observers>
            </core_block_abstract_prepare_layout_after>
            <core_block_abstract_to_html_after>
                <observers>
                    <mailchimp_order_view_to_html_after>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>addOrderViewMonkey</method>
                    </mailchimp_order_view_to_html_after>
                </observers>
            </core_block_abstract_to_html_after>
            <core_block_abstract_to_html_before>
                <observers>
                    <mailchimp_add_synced_to_order_grid>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>addColumnToSalesOrderGrid</method>
                    </mailchimp_add_synced_to_order_grid>
                </observers>
            </core_block_abstract_to_html_before>
            <sales_order_grid_collection_load_before>
                <observers>
                    <mailchimp_add_synced_to_order_grid_collection>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>addColumnToSalesOrderGridCollection</method>
                    </mailchimp_add_synced_to_order_grid_collection>
                </observers>
            </sales_order_grid_collection_load_before>
            <model_config_data_save_before>
                <observers>
                    <mailchimp_save_config>
                        <class>mailchimp/observer</class>
                        <method>saveConfigBefore</method>
                    </mailchimp_save_config>
                </observers>
            </model_config_data_save_before>
            <adminhtml_block_html_before>
                <observers>
                    <mailchimp_alter_newsletter_grid>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>alterNewsletterGrid</method>
                    </mailchimp_alter_newsletter_grid>
                </observers>
            </adminhtml_block_html_before>
            <catalog_product_save_after>
                <observers>
                    <mailchimp_save_after_product>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>productSaveAfter</method>
                    </mailchimp_save_after_product>
                </observers>
            </catalog_product_save_after>
            <controller_action_postdispatch_adminhtml_promo_quote_couponsMassDelete>
                <observers>
                    <mailchimp_controller_couponsMassDelete>
                        <type>model</type>
                        <class>mailchimp/observer</class>
                        <method>secondaryCouponsDelete</method>
                    </mailchimp_controller_couponsMassDelete>
                </observers>
            </controller_action_postdispatch_adminhtml_promo_quote_couponsMassDelete>
        </events>
    </adminhtml>
    <default>
        <mailchimp>
            <general>
                <webhook_active>1</webhook_active>
                <map_fields><![CDATA[a:27:{s:18:"_1468601283719_719";a:2:{s:9:"mailchimp";s:7:"WEBSITE";s:7:"magento";s:1:"1";}s:18:"_1468609069544_544";a:2:{s:9:"mailchimp";s:7:"STOREID";s:7:"magento";s:1:"2";}s:18:"_1469026825907_907";a:2:{s:9:"mailchimp";s:9:"STORENAME";s:7:"magento";s:1:"3";}s:18:"_1469027411717_717";a:2:{s:9:"mailchimp";s:6:"PREFIX";s:7:"magento";s:1:"4";}s:18:"_1469027418285_285";a:2:{s:9:"mailchimp";s:5:"FNAME";s:7:"magento";s:1:"5";}s:18:"_1469027422918_918";a:2:{s:9:"mailchimp";s:5:"MNAME";s:7:"magento";s:1:"6";}s:18:"_1469027429502_502";a:2:{s:9:"mailchimp";s:5:"LNAME";s:7:"magento";s:1:"7";}s:18:"_1469027434574_574";a:2:{s:9:"mailchimp";s:6:"SUFFIX";s:7:"magento";s:1:"8";}s:18:"_1469027444231_231";a:2:{s:9:"mailchimp";s:5:"EMAIL";s:7:"magento";s:1:"9";}s:18:"_1469027453439_439";a:2:{s:9:"mailchimp";s:6:"CGROUP";s:7:"magento";s:2:"10";}s:18:"_1469027462887_887";a:2:{s:9:"mailchimp";s:3:"DOB";s:7:"magento";s:2:"11";}s:18:"_1469027468903_903";a:2:{s:9:"mailchimp";s:7:"BILLING";s:7:"magento";s:2:"13";}s:18:"_1469027475632_632";a:2:{s:9:"mailchimp";s:8:"SHIPPING";s:7:"magento";s:2:"14";}s:18:"_1469027480560_560";a:2:{s:9:"mailchimp";s:3:"TAX";s:7:"magento";s:2:"15";}s:18:"_1469027486920_920";a:2:{s:9:"mailchimp";s:9:"CONFIRMED";s:7:"magento";s:2:"16";}s:18:"_1469027496512_512";a:2:{s:9:"mailchimp";s:9:"CREATEDAT";s:7:"magento";s:2:"17";}s:18:"_1469027502720_720";a:2:{s:9:"mailchimp";s:6:"GENDER";s:7:"magento";s:2:"18";}s:18:"_1469027508616_616";a:2:{s:9:"mailchimp";s:9:"DISGRPCHG";s:7:"magento";s:2:"35";}s:18:"_1472845935735_735";a:2:{s:9:"mailchimp";s:8:"BCOMPANY";s:7:"magento";s:15:"billing_company";}s:18:"_1472846546252_252";a:2:{s:9:"mailchimp";s:8:"BCOUNTRY";s:7:"magento";s:15:"billing_country";}s:18:"_1472846569989_989";a:2:{s:9:"mailchimp";s:10:"BTELEPHONE";s:7:"magento";s:17:"billing_telephone";}s:18:"_1472846572949_949";a:2:{s:9:"mailchimp";s:8:"BZIPCODE";s:7:"magento";s:15:"billing_zipcode";}s:18:"_1472846578861_861";a:2:{s:9:"mailchimp";s:8:"SCOMPANY";s:7:"magento";s:16:"shipping_company";}s:17:"_1472846584014_14";a:2:{s:9:"mailchimp";s:8:"SCOUNTRY";s:7:"magento";s:16:"shipping_country";}s:18:"_1472846587534_534";a:2:{s:9:"mailchimp";s:10:"STELEPHONE";s:7:"magento";s:18:"shipping_telephone";}s:18:"_1472846591374_374";a:2:{s:9:"mailchimp";s:8:"SZIPCODE";s:7:"magento";s:16:"shipping_zipcode";}s:18:"_1490127043147_147";a:2:{s:9:"mailchimp";s:3:"DOP";s:7:"magento";s:3:"dop";}}]]></map_fields>
                <customer_map_fields><![CDATA[a:13:{i:0;a:3:{s:5:"label";s:18:"Shipping Telephone";s:5:"value";s:18:"shipping_telephone";s:10:"field_type";s:4:"text";}i:1;a:3:{s:5:"label";s:16:"Shipping Company";s:5:"value";s:16:"shipping_company";s:10:"field_type";s:4:"text";}i:2;a:3:{s:5:"label";s:15:"Billing Company";s:5:"value";s:15:"billing_company";s:10:"field_type";s:4:"text";}i:3;a:3:{s:5:"label";s:16:"Shipping Company";s:5:"value";s:16:"shipping_company";s:10:"field_type";s:4:"text";}i:4;a:3:{s:5:"label";s:17:"Billing Telephone";s:5:"value";s:17:"billing_telephone";s:10:"field_type";s:4:"text";}i:5;a:3:{s:5:"label";s:15:"Billing Country";s:5:"value";s:15:"billing_country";s:10:"field_type";s:4:"text";}i:6;a:3:{s:5:"label";s:16:"Shipping Country";s:5:"value";s:16:"shipping_country";s:10:"field_type";s:4:"text";}i:7;a:3:{s:5:"label";s:15:"Billing ZipCode";s:5:"value";s:15:"billing_zipcode";s:10:"field_type";s:4:"text";}i:8;a:3:{s:5:"label";s:16:"Shipping ZipCode";s:5:"value";s:16:"shipping_zipcode";s:10:"field_type";s:4:"text";}i:9;a:3:{s:5:"label";s:21:"Last Date Of Purchase";s:5:"value";s:3:"dop";s:10:"field_type";s:4:"date";}i:10;a:3:{s:5:"label";s:10:"Store Code";s:5:"value";s:10:"store_code";s:10:"field_type";s:4:"text";}i:11;a:3:{s:5:"label";s:14:"Shipping State";s:5:"value";s:14:"shipping_state";s:10:"field_type";s:4:"text";}i:12;a:3:{s:5:"label";s:13:"Billing State";s:5:"value";s:13:"billing_state";s:10:"field_type";s:4:"text";}}]]></customer_map_fields>
                <order_grid>3</order_grid>
                <subscriber_batch_amount>200</subscriber_batch_amount>
                <connection_timeout>20</connection_timeout>
            </general>
            <ecommerce>
                <customer_batch_amount>100</customer_batch_amount>
                <product_batch_amount>100</product_batch_amount>
                <order_batch_amount>50</order_batch_amount>
            </ecommerce>
            <emailcatcher>
                <popup_cancel>1</popup_cancel>
                <popup_subscription>1</popup_subscription>
                <popup_heading>Welcome to our store!</popup_heading>
                <popup_text>Please enter your email address to get the most out of our store and improve your shopping
                    experience.
                </popup_text>
                <popup_width>50</popup_width>
                <popup_height>20</popup_height>
                <popup_cookie_time>1</popup_cookie_time>
                <popup_insist>1</popup_insist>
            </emailcatcher>
            <warning_message><![CDATA[Warning. If you save the configuration, the current store in mailchimp will be recreated]]></warning_message>
            <popup_message><![CDATA[This will affect Automations created on your MailChimp account. Do you want to proceed?]]></popup_message>
            <abandonedcart>
                <cart_batch_amount>100</cart_batch_amount>
            </abandonedcart>
        </mailchimp>
    </default>
    <crontab>
        <jobs>
            <mailchimp_bulksync_ecommerce_data>
                <schedule>
                    <cron_expr>*/5 * * * *</cron_expr>
                </schedule>
                <run>
                    <model>mailchimp/cron::syncEcommerceBatchData</model>
                </run>
                <groups>ebizmarts_mailchimp</groups>
            </mailchimp_bulksync_ecommerce_data>
            <mailchimp_bulksync_subscriber_data>
                <schedule>
                    <cron_expr>*/5 * * * *</cron_expr>
                </schedule>
                <run>
                    <model>mailchimp/cron::syncSubscriberBatchData</model>
                </run>
                <groups>ebizmarts_mailchimp</groups>
            </mailchimp_bulksync_subscriber_data>
            <mailchimp_process_webhook_data>
                <schedule>
                    <cron_expr>*/5 * * * *</cron_expr>
                </schedule>
                <run>
                    <model>mailchimp/cron::processWebhookData</model>
                </run>
                <groups>ebizmarts_mailchimp</groups>
            </mailchimp_process_webhook_data>
            <mailchimp_delete_webhook_processed>
                <schedule>
                    <cron_expr>0 0 * * *</cron_expr>
                </schedule>
                <run>
                    <model>mailchimp/cron::deleteWebhookRequests</model>
                </run>
                <groups>ebizmarts_mailchimp</groups>
            </mailchimp_delete_webhook_processed>
            <mailchimp_clear_ecommerce_data>
                <schedule>
                    <cron_expr>0 0 * * 0</cron_expr>
                </schedule>
                <run>
                    <model>mailchimp/cron::clearEcommerceData</model>
                </run>
                <groups>ebizmarts_mailchimp</groups>
            </mailchimp_clear_ecommerce_data>
        </jobs>
    </crontab>
</config>
