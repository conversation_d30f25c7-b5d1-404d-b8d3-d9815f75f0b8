<?php

/*

Plumrocket Inc.

NOTICE OF LICENSE

This source file is subject to the End-user License Agreement
that is available through the world-wide-web at this URL:
http://wiki.plumrocket.net/wiki/EULA
If you are unable to obtain it through the world-wide-web, please
send an <NAME_EMAIL> so we can send you a copy immediately.

@package    Plumrocket_Cart_Reservation-v1.5.x
@copyright  Copyright (c) 2013 Plumrocket Inc. (http://www.plumrocket.com)
@license    http://wiki.plumrocket.net/wiki/EULA  End-user License Agreement
 
*/
?>
<script type="text/javascript">
	countDownFormat = '<?php echo $this->getFormat(); ?>';
	countDownProductFormat = '<?php echo $this->getProductFormat(); ?>';
	needReloadPage = <?php echo (int)$this->needReloadPage(); ?>;
	expiryText = '<?php echo $this->getExpiryText(); ?>';
	foreverText = '<?php echo Mage::helper('cartreservation')->__('The cart is reserved forever'); ?>';
	lockPath = '<?php echo $this->getUrl('cartreservation/index/lockpopup'); ?>';
	crRealTimeUrl = '<?php echo $this->getUrl('cartreservation/index/realTime'); ?>?cache=none';
	crCurrentTime = '<?php echo time() ?>';
	<?php $rtime = $this->leftReminderTime(); ?>reminderTime = <?php echo (int)$rtime; ?>;
	onExpiryCartCallback = function () {
		var d = new Date();
		window.location = '<?php echo $this->getUrl('cartreservation/index/reload'); ?>?t='+(d.getTime())+'&referer='+encodeURI(window.location);
	}
</script>
<?php if ($rtime !== 'forever') { 
	echo $this->getChildHtml('popup');
}
