<?php
/**
 * @package Stenik_QuickFilter
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_QuickFilter_Block_Form
 */
?>

<?php
    $filters    = $this->getFilters();
    $quickFilterId = 'quickfilter_' . md5(uniqid(true));

    if (!$this->isQuickSearchApplied() && $this->areThereActiveFilters()) {
        return;
    }
?>

<?php if ($this->isQuickSearchApplied()): ?>
    <?php if ($layer = $this->getLayer()): ?>
        <div class="quick-search-result-box">
            <span class="title">
                <?php
                    echo sprintf('(<strong>%s</strong>)&nbsp;%s',
                        $layer->getProductCollection()->getSize(),
                        $this->__('Search results')
                    );
                ?>:
            </span>

            <a href="javascript:;" onclick="jQuery('#<?php echo $quickFilterId ?>').toggle(); return false;" class="edit-search">
                <?php echo $this->__('Edit search criteria');?>
            </a>
        </div>
    <?php endif ?>
<?php endif ?>

<?php if (count($filters)): ?>
    <div
        class="quickFilterWrapper"
        id="<?php echo $quickFilterId ?>"
        <?php if ($this->isQuickSearchApplied()): ?>style="display: none"<?php endif ?>
    >
        <?php if ($this->getTitle()): ?>
            <span class="quickFilterTitle title"><?php echo $this->getTitle(); ?></span>
        <?php endif ?>
        <form action="<?php echo $this->getUrl('stenik_quickfilter/form/submit') ?>" class="quickFilterForm" method="post">
            <?php if ($this->getCategoryId()): ?>
                <input type="hidden" name="filter[category]" value="<?php echo $this->getCategoryId() ?>" class="limitingFilter">
            <?php endif ?>

            <div class="filtersContent">
                <?php if (count($filters)): ?>
                    <?php foreach ($filters as $filter): ?>
                        <?php if ($filter->getType() == 'select'): ?>
                            <div class="filterBox filterBox-<?php echo $filter->getCode() ?>" data-select-id="quick_filter_<?php echo $filter->getCode() ?>">
                                <select
                                    name="filter[<?php echo $filter->getCode() ?>]"
                                    id="quick_filter_<?php echo $filter->getCode() ?>"
                                    data-filter-code="<?php echo $filter->getCode() ?>"
                                    class="filterToBeLimited limitingFilter"
                                >
                                    <option value="" selected="selected"><?php echo $this->escapeHtml($filter->getLabel()) ?></option>
                                    <?php foreach ($filter->getOptions() as $option): ?>
                                        <option value="<?php echo $option['value'] ?>"><?php echo $this->escapeHtml($option['label']) ?></option>
                                    <?php endforeach ?>
                                </select>
                            </div>
                        <?php elseif ($filter->getType() == 'checkbox'): ?>
                            <div class="filterBox checkbox-stylized-box marginTopCheck">
                                <span class="input-wrapper">
                                    <input
                                        type="checkbox"
                                        class="hidden-checkbox limitingFilter"
                                        id="<?php echo $quickFilterId ?>_filter_<?php echo $filter->getCode() ?>"
                                        name="filter[<?php echo $filter->getCode() ?>]"
                                        value="<?php echo $filter->getValue() ?>"
                                    >
                                </span>
                                <label for="<?php echo $quickFilterId ?>_filter_<?php echo $filter->getCode() ?>" class="click-stylized-input"><?php echo $this->__('Only in stock');?></label>
                            </div>
                        <?php endif ?>
                    <?php endforeach ?>
                <?php endif ?>

                <div class="filterBox submit">
                    <button class="button show-quick-fitler-results">
                        <svg aria-hidden="true" class="icon-svg search">
                            <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#search' ?>"></use>
                        </svg>
                        <?php echo $this->__('Search');?>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script>
        jQuery(function($) {
            $('#<?php echo $quickFilterId ?> .limitingFilter').each(function() {
                var origOptions = [];
                $(this).find('option').each(function() {
                    origOptions.push(this);
                });

                $(this).data('orig_options', origOptions);
            });

            var limitSelectOptions = function($select, values) {
                var $select = $($select);

                var origOptions = $select.data('orig_options');
                if (!origOptions) return;

                var newOptions = [];

                for (var i = 0; i < origOptions.length; i++) {
                    var option = $(origOptions[i]);
                    if (option.attr('value') == '' ||
                        values === 'none' ||
                        values.indexOf(option.attr('value')) != -1
                    ) {
                        newOptions.push(option);
                    }
                }

                var origVal = $select.val();

                var selectHtmlBeforeChange = $select.html();
                $select.find('option').remove();

                for (var i = 0; i < newOptions.length; i++) {
                    $select.append(newOptions[i])
                }

                $select.val(origVal);
                var selectHtmlAfterChange = $select.html();

                return selectHtmlBeforeChange.replace(/\w/g, '') != selectHtmlAfterChange.replace(/\w/g, '');
            };

            var currentlyFiltering = false;
            $('#<?php echo $quickFilterId ?> .limitingFilter').change(function() {
                if (currentlyFiltering) {
                    return;
                }

                var filtersSerialized = $('#<?php echo $quickFilterId ?> .limitingFilter').serializeArray();
                var filters = {};
                for (var i = filtersSerialized.length - 1; i >= 0; i--) {
                    if (filtersSerialized[i].value) {
                        filters[filtersSerialized[i].name] = filtersSerialized[i].value;
                    }
                }

                var filtersToFetch = [];
                $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                    if ($(this).data('filter-code')) {
                        filtersToFetch.push($(this).data('filter-code'));
                    }
                });

                $.ajax({
                    url: <?php echo json_encode($this->getUrl('stenik_quickfilter/form/getFilters')) ?>,
                    type: 'post',
                    dataType: 'json',
                    data: $.extend(filters, {filters_to_fetch: filtersToFetch})
                }).done(function(response) {
                    currentlyFiltering = true;
                    $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                        if ($(this).is('select')) {
                            if (limitSelectOptions(this, 'none')) {
                                $(this).change();
                            }

                            /*var fireChangeEvent = false;
                            $(this).find('option').each(function() {
                                if ($(this).is(':disabled') && $(this).data('disabled_by_filter')) {
                                    $(this).prop('disabled', false);
                                    $(this).data('disabled_by_filter', false);
                                    fireChangeEvent = true;
                                }
                            });
                            if (fireChangeEvent) {
                                $(this).change();
                            }*/

                        }
                    })
                    if (response) {
                        for (key in response) {
                            if (response[key].length) {
                                $('#<?php echo $quickFilterId ?> .filterToBeLimited[data-filter-code="' + key + '"]').each(function() {
                                    var fireChangeEvent = false;
                                    if ($(this).is('select')) {
                                        if (limitSelectOptions(this, response[key])) {
                                            fireChangeEvent = true;
                                        }
                                        /*$(this).find('option').each(function() {
                                            var $option = $(this);
                                            if (!$option.is('disabled') && $option.attr('value') != '') {
                                                if (response[key].indexOf($option.attr('value')) == -1) {
                                                    $option.prop('disabled', true);
                                                    $option.data('disabled_by_filter', true);
                                                    fireChangeEvent = true;
                                                }
                                            }
                                        });*/
                                    }

                                    if (fireChangeEvent) {
                                        $(this).change();
                                    }
                                });
                            }
                        }
                    }
                    currentlyFiltering = false;
                });
            });
        });
    </script>
<?php endif ?>