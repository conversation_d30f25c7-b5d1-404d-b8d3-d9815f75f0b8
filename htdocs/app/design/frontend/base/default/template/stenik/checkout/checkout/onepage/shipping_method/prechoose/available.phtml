<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /** @var $this Mage_Checkout_Block_Onepage_Shipping_Method_Available */ ?>
<?php $_shippingRateGroups = $this->getShippingRates(); ?>
<?php if (!$_shippingRateGroups): ?>
    <p><?php echo $this->__('Sorry, no quotes are available for this order at this time.') ?></p>
<?php elseif($this->getAddressShippingMethod()): ?>
    <div class="sp-methods">
        <?php $shippingCodePrice = array(); ?>
        <?php $isSingleMethod = (count($_shippingRateGroups) == 1) ?>
        <?php foreach ($_shippingRateGroups as $code => $_rates): ?>
            <?php $isSingleMethod = $isSingleMethod && (count($_rates) == 1); ?>
            <?php foreach ($_rates as $_rate): ?>
                <?php $shippingCodePrice[] = "'".$_rate->getCode()."':".(float)$_rate->getPrice(); ?>
                <?php if ($_rate->getErrorMessage()): ?>
                    <ul class="messages"><li class="error-msg"><ul><li><?php echo $this->escapeHtml($_rate->getErrorMessage()) ?></li></ul></li></ul>
                <?php else: ?>
                    <span class="no-display">
                        <input name="shipping_method" type="radio" value="<?php echo $_rate->getCode() ?>" id="s_method_<?php echo $_rate->getCode() ?>"
                            <?php if ($isSingleMethod || $this->getAddressShippingMethod() == $_rate->getCode()): ?>
                                checked="checked"
                            <?php endif ?>
                        />
                    </span>

                    <script type="text/javascript">
                        //<![CDATA[
                            lastPrice = <?php echo (float)$_rate->getPrice(); ?>;
                        //]]>
                    </script>

                    <label for="s_method_<?php echo $_rate->getCode() ?>"><?php echo $this->__('Delivery price: ') ?><?php echo $this->escapeHtml($_rate->getMethodTitle()) ?>
                        <?php $_excl = $this->getShippingPrice($_rate->getPrice(), $this->helper('tax')->displayShippingPriceIncludingTax()); ?>
                        <?php $_incl = $this->getShippingPrice($_rate->getPrice(), true); ?>
                        <?php echo $_excl; ?>
                        <?php if ($this->helper('tax')->displayShippingBothPrices() && $_incl != $_excl): ?>
                            (<?php echo $this->__('Incl. Tax'); ?> <?php echo $_incl; ?>)
                        <?php endif; ?>
                    </label>
               <?php endif ?>
            <?php endforeach; ?>
        <?php endforeach; ?>
    </div>

    <script type="text/javascript">
    //<![CDATA[
        <?php if (!empty($shippingCodePrice)): ?>
            var shippingCodePrice = {<?php echo implode(',',$shippingCodePrice); ?>};
        <?php endif; ?>

        $$('input[type="radio"][name="shipping_method"]').each(function(el){
            Event.observe(el, 'click', function(){
                if (el.checked == true) {
                    var getShippingCode = el.getValue();
                    <?php if (!empty($shippingCodePrice)): ?>
                        var newPrice = shippingCodePrice[getShippingCode];
                        if (!lastPrice) {
                            lastPrice = newPrice;
                            quoteBaseGrandTotal += newPrice;
                        }
                        if (newPrice != lastPrice) {
                            quoteBaseGrandTotal += (newPrice-lastPrice);
                            lastPrice = newPrice;
                        }
                    <?php endif; ?>
                    checkQuoteBaseGrandTotal = quoteBaseGrandTotal;
                    return false;
                }
           });
        });
    //]]>
    </script>
<?php endif; ?>
