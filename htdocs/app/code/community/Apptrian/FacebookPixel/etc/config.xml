<?xml version="1.0"?>
<!--
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */
-->
<config>
    <modules>
        <Apptrian_FacebookPixel>
            <version>4.4.2</version>
        </Apptrian_FacebookPixel>
    </modules>
    <global>
        <models>
            <apptrian_facebookpixel>
                <class>Apptrian_FacebookPixel_Model</class>
            </apptrian_facebookpixel>
        </models>
        <resources>
            <apptrian_facebookpixel_setup>
                <setup>
                    <module>Apptrian_FacebookPixel</module>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </apptrian_facebookpixel_setup>
        </resources>
        <blocks>
            <apptrian_facebookpixel>
                <class>Apptrian_FacebookPixel_Block</class>
            </apptrian_facebookpixel>
        </blocks>
        <helpers>
            <apptrian_facebookpixel>
                <class>Apptrian_FacebookPixel_Helper</class>
            </apptrian_facebookpixel>
        </helpers>
    </global>
    <default>
        <apptrian_facebookpixel>
            <client>
                <enabled>1</enabled>
                <pixel_id>***************</pixel_id>
                <page_handles>catalog_category_view,catalog_product_view,catalogsearch_advanced_result,catalogsearch_result_index,checkout_onepage_index,checkout_index_index,checkout_onepage_success,cms_index_index,cms_page_view,customer_account_create,blog_default,onepagecheckout_index_index,onepagecheckout_index_success,onestepcheckout_index_index,opc_index_index</page_handles>
                <base_code_enabled>1</base_code_enabled>
                <noscript_enabled>0</noscript_enabled>
                <addtocart_enabled>1</addtocart_enabled>
                <addtowishlist_enabled>1</addtowishlist_enabled>
                <completeregistration_enabled>1</completeregistration_enabled>
                <initiatecheckout_enabled>1</initiatecheckout_enabled>
                <pageview_enabled>1</pageview_enabled>
                <pageview_all>1</pageview_all>
                <purchase_enabled>1</purchase_enabled>
                <search_enabled>1</search_enabled>
                <viewcontent_enabled>1</viewcontent_enabled>
                <move_params_outside_contents>1</move_params_outside_contents>
                <detect_selected_sku>0</detect_selected_sku>
                <detect_selected_sku_bundle>0</detect_selected_sku_bundle>
                <detect_selected_sku_configurable>0</detect_selected_sku_configurable>
                <detect_selected_sku_grouped>0</detect_selected_sku_grouped>
            </client>
            <server>
                <enabled>0</enabled>
                <access_token>xxx</access_token>
                <api_version>v10.0</api_version>
                <test_event_code></test_event_code>
                <log_events>0</log_events>
                <firing_mode>1</firing_mode>
                <addtocart_enabled>1</addtocart_enabled>
                <addtowishlist_enabled>1</addtowishlist_enabled>
                <completeregistration_enabled>1</completeregistration_enabled>
                <initiatecheckout_enabled>1</initiatecheckout_enabled>
                <pageview_enabled>1</pageview_enabled>
                <pageview_all>1</pageview_all>
                <purchase_enabled>1</purchase_enabled>
                <search_enabled>1</search_enabled>
                <viewcontent_enabled>1</viewcontent_enabled>
                <move_params_outside_contents>1</move_params_outside_contents>
                <detect_selected_sku>0</detect_selected_sku>
                <detect_selected_sku_bundle>0</detect_selected_sku_bundle>
                <detect_selected_sku_configurable>0</detect_selected_sku_configurable>
                <detect_selected_sku_grouped>0</detect_selected_sku_grouped>
            </server>
            <category>
                <page_handles>catalog_category_view</page_handles>
                <event_name></event_name>
                <mapping>name=name|google_product_category=google_product_category</mapping>
            </category>
            <product>
                <page_handles>catalog_product_view</page_handles>
                <ident_bundle>1</ident_bundle>
                <content_type_bundle>0</content_type_bundle>
                <ident_configurable>1</ident_configurable>
                <content_type_configurable>0</content_type_configurable>
                <ident_downloadable>1</ident_downloadable>
                <ident_grouped>2</ident_grouped>
                <content_type_grouped>0</content_type_grouped>
                <ident_simple>1</ident_simple>
                <ident_virtual>1</ident_virtual>
                <use_content_ids>0</use_content_ids>
                <content_name></content_name>
                <content_category></content_category>
                <google_product_category></google_product_category>
                <mapping>google_product_category=google_product_category|color=color</mapping>
            </product>
            <quote>
                <page_handles>checkout_onepage_index,checkout_index_index,onepagecheckout_index_index,onestepcheckout_index_index,opc_index_index</page_handles>
                <ident_bundle>1</ident_bundle>
                <ident_configurable>1</ident_configurable>
                <ident_downloadable>1</ident_downloadable>
                <ident_grouped>2</ident_grouped>
                <ident_simple>1</ident_simple>
                <ident_virtual>1</ident_virtual>
                <quote_id_param></quote_id_param>
                <mapping>google_product_category=google_product_category|color=color</mapping>
            </quote>
            <order>
                <page_handles>checkout_onepage_success,onepagecheckout_index_success</page_handles>
                <ident_bundle>1</ident_bundle>
                <ident_configurable>1</ident_configurable>
                <ident_downloadable>1</ident_downloadable>
                <ident_grouped>2</ident_grouped>
                <ident_simple>1</ident_simple>
                <ident_virtual>1</ident_virtual>
                <order_id_param></order_id_param>
                <order_increment_id_param>order_id</order_increment_id_param>
                <quote_id_param></quote_id_param>
                <mapping>google_product_category=google_product_category|color=color</mapping>
            </order>
            <search>
                <page_handles>catalogsearch_advanced_result,catalogsearch_result_index</page_handles>
                <event_name>Search</event_name>
                <param_name>search_string</param_name>
                <request_params>q,name,sku,description,short_description</request_params>
            </search>
            <data_processing_options>
                <enabled>0</enabled>
                <dpo></dpo>
                <dpo_country>0</dpo_country>
                <dpo_state>0</dpo_state>
            </data_processing_options>
        </apptrian_facebookpixel>
    </default>
    <frontend>
        <routers>
            <apptrian_facebookpixel>
                <use>standard</use>
                <args>
                    <module>Apptrian_FacebookPixel</module>
                    <frontName>apptrian_facebookpixel</frontName>
                </args>
            </apptrian_facebookpixel>
        </routers>
        <events>
            <checkout_cart_add_product_complete>
                <observers>
                    <apptrian_facebookpixel_checkout_cart_add_product_complete>
                        <class>apptrian_facebookpixel/observer_addtocart</class>
                        <method>fireEventAddToCart</method>
                    </apptrian_facebookpixel_checkout_cart_add_product_complete>
                </observers>
            </checkout_cart_add_product_complete>
            <wishlist_product_add_after>
                <observers>
                    <apptrian_facebookpixel_wishlist_product_add_after>
                        <class>apptrian_facebookpixel/observer_addtowishlist</class>
                        <method>fireEventAddToWishlist</method>
                    </apptrian_facebookpixel_wishlist_product_add_after>
                </observers>
            </wishlist_product_add_after>
            <controller_action_postdispatch_checkout_onepage_index>
                <observers>
                    <apptrian_facebookpixel_controller_action_postdispatch_checkout_onepage_index>
                        <class>apptrian_facebookpixel/observer_checkout</class>
                        <method>fireEventCheckout</method>
                    </apptrian_facebookpixel_controller_action_postdispatch_checkout_onepage_index>
                </observers>
            </controller_action_postdispatch_checkout_onepage_index>
            <controller_action_postdispatch_checkout_index_index>
                <observers>
                    <apptrian_facebookpixel_controller_action_postdispatch_checkout_index_index>
                        <class>apptrian_facebookpixel/observer_checkout</class>
                        <method>fireEventCheckout</method>
                    </apptrian_facebookpixel_controller_action_postdispatch_checkout_index_index>
                </observers>
            </controller_action_postdispatch_checkout_index_index>
            <checkout_onepage_controller_success_action>
                <observers>
                    <apptrian_facebookpixel_checkout_onepage_controller_success_action>
                        <class>apptrian_facebookpixel/observer_checkoutsuccess</class>
                        <method>fireEventCheckoutSuccess</method>
                    </apptrian_facebookpixel_checkout_onepage_controller_success_action>
                </observers>
            </checkout_onepage_controller_success_action>
            <customer_register_success>
                <observers>
                    <apptrian_facebookpixel_customer_register_success>
                        <class>apptrian_facebookpixel/observer_customerregister</class>
                        <method>fireEventCustomerRegister</method>
                    </apptrian_facebookpixel_customer_register_success>
                </observers>
            </customer_register_success>
            <customer_session_init>
                <observers>
                    <apptrian_facebookpixel_customer_session_init>
                        <class>apptrian_facebookpixel/observer_customerinit</class>
                        <method>fireEventCustomerInit</method>
                    </apptrian_facebookpixel_customer_session_init>
                </observers>
            </customer_session_init>
            <controller_front_send_response_before>
                <observers>
                    <apptrian_facebookpixel_controller_front_send_response_before>
                        <class>apptrian_facebookpixel/observer_fpcpage</class>
                        <method>fireEventFpcPage</method>
                    </apptrian_facebookpixel_controller_front_send_response_before>
                </observers>
            </controller_front_send_response_before>
        </events>
        <layout>
            <updates>
                <apptrian_facebookpixel>
                    <file>apptrian_facebookpixel.xml</file>
                </apptrian_facebookpixel>
            </updates>
        </layout>
        <translate>
            <modules>
                <Apptrian_FacebookPixel>
                    <files>
                        <default>Apptrian_FacebookPixel.csv</default>
                    </files>
                </Apptrian_FacebookPixel>
            </modules>
        </translate>
    </frontend>
    <adminhtml>
        <acl>
            <resources>
                <admin>
                    <children>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <apptrian_info>
                                            <title>Info</title>
                                        </apptrian_info>
                                        <apptrian_facebookpixel>
                                            <title>Facebook Pixel and Conversions API</title>
                                        </apptrian_facebookpixel>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
        <translate>
            <modules>
                <Apptrian_FacebookPixel>
                    <files>
                        <default>Apptrian_FacebookPixel.csv</default>
                    </files>
                </Apptrian_FacebookPixel>
            </modules>
        </translate>
    </adminhtml>
</config>
