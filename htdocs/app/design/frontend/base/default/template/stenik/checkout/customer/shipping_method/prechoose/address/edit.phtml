<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Edit customer address template
 *
 * @see Mage_Customer_Block_Address_Edit
 */
?>
<div style="display: none">
    <span id="shipping-method-buttons-container"><button class="button"></button></span>
    <span id="shipping-method-please-wait"></span>
</div>

<?php if($this->getTitle()): ?>
<div class="page-title">
    <h1><?php echo $this->getTitle() ?></h1>
</div>
<?php endif; ?>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<form action="<?php echo $this->getSaveUrl() ?>" method="post" id="form-validate">
    <div class="fieldset left-col">
    <?php echo $this->getBlockHtml('formkey')?>
    <input type="hidden" name="success_url" value="<?php echo $this->getSuccessUrl() ?>" />
    <input type="hidden" name="error_url" value="<?php echo $this->getErrorUrl() ?>" />
        <h2 class="legend"><?php echo $this->__('Contact Information') ?></h2>
        <ul class="form-list">
            <li class="fields">
                <?php echo $this->getNameBlockHtml() ?>
            </li>
            <li class="wide">
                <div class="input-box">
                    <input type="text" name="company" id="company" title="<?php echo $this->__('Company') ?>" value="<?php echo $this->escapeHtml($this->getAddress()->getCompany()) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('company') ?>" placeholder="<?php echo $this->__('Company') ?>" />
                </div>
            </li>
            <li class="fields">
                <div class="field">
                    <div class="input-box">
                        <input type="text" name="telephone" value="<?php echo $this->escapeHtml($this->getAddress()->getTelephone()) ?>" title="<?php echo $this->__('Telephone') ?>" class="input-text  <?php echo $this->helper('customer/address')->getAttributeValidationClass('telephone') ?>" id="telephone" placeholder="<?php echo $this->__('Telephone') ?> *" />
                    </div>
                </div>
                <?php if (0): ?>
                    <div class="field">
                        <div class="input-box">
                            <input type="text" name="fax" id="fax" title="<?php echo $this->__('Fax') ?>" value="<?php echo $this->escapeHtml($this->getAddress()->getFax()) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('fax') ?>" placeholder="<?php echo $this->__('Fax') ?>" />
                        </div>
                    </div>
                <?php endif ?>
            </li>
        </ul>
    </div>
    <div class="fieldset right-col">
        <h2 class="legend"><?php echo $this->__('Address') ?></h2>
        <ul class="form-list">
            <li>
                <div class="field">
                    <div class="input-box">
                        <?php echo $this->getCountryHtmlSelect() ?>
                    </div>
                </div>
            </li>

            <li class="prechooseShippingMethods">
                <?php
                    if ($shippingMethodPrechooseBlock = $this->getChild('shipping_methods')) {
                        $shippingMethodPrechooseBlock->setAddress($this->getAddress());
                        echo $shippingMethodPrechooseBlock->toHtml();
                    }
                ?>
            </li>

            <li>
                <input type="hidden" name="save_custom_customer_address" value="1">
                <?php echo $this->getChildHtml('custom_address_fields') ?>
            </li>

            <li class="addressDetails non-custom-address-fields-content address-fields">
                <ul>
                    <?php $_streetValidationClass = $this->helper('customer/address')->getAttributeValidationClass('street'); ?>
                        <li class="wide">
                            <div class="input-box">
                                <input type="text" name="street[]" value="<?php echo $this->escapeHtml($this->getAddress()->getStreet(1)) ?>" title="<?php echo $this->__('Street Address') ?>" id="street_1" class="input-text <?php echo $_streetValidationClass ?>" placeholder="<?php echo $this->__('Street Address') ?> *" />
                            </div>
                        </li>
                    <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                    <?php for ($_i = 2, $_n = $this->helper('customer/address')->getStreetLines(); $_i <= $_n; $_i++): ?>
                        <li class="wide">
                            <div class="input-box">
                                <input type="text" name="street[]" value="<?php echo $this->escapeHtml($this->getAddress()->getStreet($_i)) ?>" title="<?php echo $this->__('Street Address %s', $_i) ?>" id="street_<?php echo $_i ?>" class="input-text <?php echo $_streetValidationClass ?>" />
                            </div>
                        </li>
                    <?php endfor; ?>

                    <?php if ($this->helper('customer/address')->isVatAttributeVisible()) : ?>
                    <li class="wide">
                        <div class="input-box">
                            <input type="text" name="vat_id" value="<?php echo $this->escapeHtml($this->getAddress()->getVatId()) ?>" title="<?php echo $this->__('VAT Number') ?>" id="vat_id" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('vat_id') ?>" placeholder="<?php echo $this->__('VAT Number') ?>" />
                        </div>
                    </li>
                    <?php endif; ?>
                    <li class="fields">
                        <div class="field">
                            <div class="input-box">
                                <input type="text" name="city" value="<?php echo $this->escapeHtml($this->getAddress()->getCity()) ?>"  title="<?php echo $this->__('City') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('city') ?>" id="city" placeholder="<?php echo $this->__('City') ?> *" />
                            </div>
                        </div>
                        <div class="field">
                            <label for="region_id" class="required"><em>*</em><?php echo $this->__('State/Province') ?></label>
                            <div class="input-box">
                                <select id="region_id" name="region_id" title="<?php echo $this->__('State/Province') ?>" class="validate-select" style="display:none;">
                                   <option value=""><?php echo $this->__('Please select region, state or province') ?></option>
                                </select>
                                <script type="text/javascript">
                                //<![CDATA[
                                    $('region_id').setAttribute('defaultValue',  "<?php echo $this->getAddress()->getRegionId() ?>");
                                //]]>
                                </script>
                                <input type="text" id="region" name="region" value="<?php echo $this->escapeHtml($this->getAddress()->getRegion()) ?>"  title="<?php echo $this->__('State/Province') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('region') ?>" />
                            </div>
                        </div>
                    </li>

                    <li class="fields">
                        <div class="field">
                            <div class="input-box">
                                <input type="text" name="postcode" value="<?php echo $this->escapeHtml($this->getAddress()->getPostcode()) ?>" title="<?php echo $this->__('Zip/Postal Code') ?>" id="zip" class="input-text validate-zip-international <?php echo $this->helper('customer/address')->getAttributeValidationClass('postcode') ?>" placeholder="<?php echo $this->__('Zip/Postal Code') ?> *" />
                            </div>
                        </div>
                    </li>
                </ul>
            </li>
            <li<?php if($this->canSetAsDefaultBilling()) echo ' class="control"' ?>>
                <?php if($this->isDefaultBilling()): ?>
                    <strong><?php echo $this->__('Default Billing Address') ?></strong>
                <?php elseif($this->canSetAsDefaultBilling()): ?>
                    <input type="checkbox" id="primary_billing" name="default_billing" value="1" title="<?php echo $this->__('Use as My Default Billing Address') ?>" class="checkbox" /><label for="primary_billing"><?php echo $this->__('Use as my default billing address') ?></label>
                <?php else: ?>
                    <input type="hidden" name="default_billing" value="1" />
                <?php endif; ?>
            </li>
            <li<?php if($this->canSetAsDefaultShipping()) echo ' class="control"' ?>>
                <?php if($this->isDefaultShipping()): ?>
                    <strong><?php echo $this->__('Default Shipping Address') ?></strong>
                <?php elseif($this->canSetAsDefaultShipping()): ?>
                    <input type="checkbox" id="primary_shipping" name="default_shipping" value="1" title="<?php echo $this->__('Use as My Default Shipping Address') ?>" class="checkbox" /><label for="primary_shipping"><?php echo $this->__('Use as my default shipping address') ?></label>
                <?php else: ?>
                    <input type="hidden" name="default_shipping" value="1" />
                <?php endif; ?>
            </li>
        </ul>
    </div>
    <div class="buttons-set">
        <p class="required"><?php echo $this->__('* Required Fields') ?></p>
        <p class="back-link"><a href="<?php echo $this->escapeUrl($this->getBackUrl()) ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
        <button data-action="save-customer-address" type="submit" title="<?php echo $this->__('Save Address') ?>" class="button"><?php echo $this->__('Save Address') ?></button>
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
    var dataForm = new VarienForm('form-validate', true);
    new RegionUpdater('country', 'region', 'region_id', <?php echo $this->helper('directory')->getRegionJson() ?>, undefined, 'zip');
//]]>
//
    (function($) {
        var refreshingShippingMethodsVisibility = false;
        var refreshShippingMethodsVisibility = function() {
            if (refreshingShippingMethodsVisibility) {
                return;
            }

            refreshingShippingMethodsVisibility = true;

            var countryId = $('#country').val();

            $('.prechooseShippingMethods .shippingMethod').each(function() {
                if (applicableCountries = $(this).data('applicable-countries')) {
                    applicableCountries = applicableCountries.split(',');

                    if (applicableCountries.indexOf(countryId) >= 0) {
                        var $input = $(this).show().find('input.prechoose');
                        $input.prop('disabled', false);
                    } else {
                        $(this).find('input.prechoose:checked').prop('checked', false).change();
                        $(this).hide().find('input.prechoose').prop('disabled', true);
                    }
                }
            });

            var $addressFields = $('#form-validate .address-fields');
            $addressFields.hide();


            $('.prechooseShippingMethods .shippingMethod input:checked').each(function() {
                $addressFields.filter('.' + $(this).data('used-address-fields') + '-content').show();
            });

            refreshingShippingMethodsVisibility = false;
        };

        refreshShippingMethodsVisibility();

        $(refreshShippingMethodsVisibility);

        $('#country').change(refreshShippingMethodsVisibility);
        $('.prechooseShippingMethods .shippingMethod input').change(refreshShippingMethodsVisibility);
    })(jQuery);
</script>
