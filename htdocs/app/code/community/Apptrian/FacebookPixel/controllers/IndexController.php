<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */
class Apptrian_FacebookPixel_IndexController extends Mage_Core_Controller_Front_Action
{
    /**
     * Controller Index Action.
     *
     */
    public function indexAction()
    {
        $helper     = Mage::helper('apptrian_facebookpixel');
        $firingMode = $helper->getFiringMode();
        
        if (Mage::app()->getRequest()->isAjax() && $firingMode != 2) {
            $data = $this->getRequest()->getParams();

            $apiData = $helper->getEventDataForApi($data);
            
            $helper->fireServerEvent($apiData);
            
            $response = array('success' => 'true');
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($response));
        }
    }
    
    /**
     * Controller Index Action.
     *
     */
    public function matchingAction()
    {
        $helper     = Mage::helper('apptrian_facebookpixel');
        $firingMode = $helper->getFiringMode();
        
        $response = array();
        
        if (Mage::app()->getRequest()->isAjax() && $firingMode != 2) {
            $data = $this->getRequest()->getParam('sections');
            
            $customerId = 0;
            
            if ($data == 'apptrian_facebook_pixel_matching_section'
                && Mage::isInstalled() && Mage::getSingleton('customer/session')->isLoggedIn()
            ) {
                $customerId = Mage::getSingleton('customer/session')->getCustomer()->getId();
                $response = $helper->getUserData($customerId);
            }
        }
        
        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($response));
    }
}
