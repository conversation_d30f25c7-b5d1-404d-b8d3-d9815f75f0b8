<?php
    /**
     * Available helper functions to use:
     *
     * echo $this->helper('stenik_gdprcompliance')->getTermsCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageContent();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageContent();
     */
?>
<?php $helper = $this->helper('stenik_gdprcompliance/agreement_review'); ?>
<?php if ($helper->isEnabled()): ?>
    <input name="gdpr_review" id="gdpr_review" title="<?php echo $this->htmlEscape(strip_tags($helper->getAgreement())); ?>" class="checkbox required-entry" type="checkbox" value="1" />
    <label for="gdpr_review" class="long-label"><?php echo $helper->getAgreement(); ?> <em>*</em></label>
    <?php if ($helper->getAdditionalInformation()): ?>
        <div class="gdpr-info">
            <p><?php echo $helper->getAdditionalInformation(); ?></p>
        </div>
    <?php endif; ?>
<?php else: ?>
    <?php if ($helper->getAdditionalInformation()): ?>
        <div class="gdpr-info">
            <p><?php echo $helper->getAdditionalInformation(); ?></p>
        </div>
    <?php endif; ?>
<?php endif; ?>