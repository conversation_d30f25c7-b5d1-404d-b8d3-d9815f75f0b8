<?php
/**
 * @package Stenik_Widget
 * <AUTHOR> Magento Team <<EMAIL>>
 */

    $productCollection = $this->getProductCollection();
    $_helper = $this->helper('catalog/output');
    $_helperStenikBase = $this->helper('stenik_base');
    $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix();

    $productCount = null;
    if (!$productCollection || !($productCount = $productCollection->getSize())) {
        return;
    }

    $sliderUniqueId = 'productSlider_cplsd' . md5(uniqid('', true));
?>

<div class="col-xs-12">
    <?php if ($this->getTitle()): ?>
        <div class="row">
            <div class="col-xs-12">
                <div class="row-header marginB30 clearfix">
                    <span class="row-title">
                        <?php echo $this->escapeHtml($this->getTitle()); ?>
                    </span>
                </div>
            </div>
        </div>
    <?php endif ?>
    <div class="owl-carousel product-slider products-widget" id="<?php echo $sliderUniqueId ?>">
        <?php foreach ($productCollection as $product): ?>

            <?php $secondImg = ($this->helper('stenik_sitesettings')->getProductListingSecondImg() ? Mage::helper('stenik_base/catalog_product')->getSecondImageFile($product) : null) ?>

            <a class="product-box" href="<?php echo $product->getProductUrl() ?>">
                <span class="image-wrapper<?php if ($secondImg): ?> has-second-img<?php endif; ?>">
                    <img
                        class="first"
                        src="<?php echo $this->helper('catalog/image')->init($product, 'small_image')->resize(285, 285); ?>"
                        srcset="<?php echo $this->helper('catalog/image')->init($product, 'small_image')->resize(570, 570); ?> 2x"
                        alt="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true) ?>"
                        title="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true)  . ' - ' . $productImageSufix ?>"
                    >

                    <?php if ($secondImg): ?>
                        <img
                            class="second"
                            src="<?php echo $this->helper('catalog/image')->init($product, 'small_image', $secondImg)->resize(285, 285); ?>"
                            srcset="<?php echo $this->helper('catalog/image')->init($product, 'small_image', $secondImg)->resize(570, 570); ?> 2x"
                            alt="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true) ?> - 2"
                            title="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true)  . ' - 2 - ' . $productImageSufix ?>"
                        >
                    <?php endif; ?>

                    <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($product)->toHtml(); ?>
                    <span class="actions">
                        <?php if($product->isSaleable()): ?>
                            <span class="button checkout-color add-to-cart" onclick="document.location.href='<?php echo $this->getAddToCartUrl($product) ?>'; return false">
                                <?php echo $this->__('Add to Cart');?>
                            </span>
                        <?php endif; ?>
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <span class="icon-link" onclick="document.location.href='<?php echo $this->helper('wishlist')->getAddUrl($product) ?>'; return false">
                                <svg aria-hidden="true" class="icon-svg wishlist"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use></svg>
                            </span>
                        <?php endif; ?>
                        <?php if($_compareUrl=$this->getAddToCompareUrl($product)): ?>
                            <span class="icon-link" onclick="document.location.href='<?php echo $_compareUrl ?>'; return false">
                                <svg aria-hidden="true" class="icon-svg compare"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use></svg>
                            </span>
                        <?php endif; ?>
                    </span>
                </span>

                <?php $confOptions = Mage::helper('stenik_base/catalog_product')->getProductConfigurableAttributeOptions($product, 'attribute_7'); ?>

                <?php if ($count = count($confOptions)): ?>
                     <span class="attributes">
                         <?php echo $count; ?> <?php echo $this->__($count == 1 ? 'color' : 'colors'); ?>
                     </span>
                <?php endif ?>

                <span class="title"><?php echo $_helper->productAttribute($product, $product->getName(), 'name') ?></span>
                <?php echo $this->getPriceHtml($product, true, '-widget-item-price') ?>
            </a>

        <?php endforeach ?>
    </div>

    <script>
        jQuery(function($){

            $(".product-slider").owlCarousel({
                loop: false,
                items: 4,
                slideBy: 2,
                nav: true,
                navText: false,
                slideSpeed: 500,
                autoPlay: false,
                autoplayTimeout: 5000,
                autoplayHoverPause: true,
                addClassActive: true,
                dots: false,
                stagePadding: 1,
                responsive : {
                    0 : { items: 2 },
                    480 : { items: 4 },
                    767 : { items: 4}
                }
            });

        });
    </script>
</div>
