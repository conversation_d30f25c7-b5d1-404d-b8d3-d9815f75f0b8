<?php $helper = Mage::helper('pfg_fastorder'); ?>
<?php $url =  $this->getUrl('fastorder/index/postAjax')  ?>
<div style="display: none;">
    <div id="fast-order-modal" style="position: relative" class="fast-order-wrapper">
        <div class="fast-order-overlay" id="fast-order-overlay" style="display: none;">
            <img class="fast-order-loader" id="fast-order-loader" src="<?= Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_SKIN) ?>frontend/base/default/images/bubble/layer/loader.gif" width="64" height="68" alt="loader">
        </div>
        <form id="fast_order_form" class="fast_order_form" method="post">
            <input type="hidden" name="form_key" value="{FK}" />
            <input type="hidden" id="amasty_invisible_token_fast_order" name="amasty_invisible_token" value="" />
            <input type="hidden" id="product-id" name="product-id"
                   value="<?php echo Mage::registry('current_product')->getId() ?>" />
            <ul class="form-list">
                <li><label for="fast-order-name"><?php echo $helper->__('Name') ?><span
                                style="color: #CE181E;"> *</span></label>
                    <input class="input-text required-entry" id="fast-order-name" name="fast-order-name"
                           style="width: 250px;" type="text" value="">
                </li>
                <li><label for="fast-order-email"><?php echo $helper->__('Email') ?><span
                                style="color: #CE181E;"> *</span></label>
                    <input class="input-text required-entry" id="fast-order-email" name="fast-order-email"
                           style="width: 250px;" type="email" value="">
                </li>
                <li><label for="fast-order-telephone"><?php echo $helper->__('Telephone') ?><span
                                style="color: #CE181E;"> *</span></label>
                    <input class="input-text required-entry" id="fast-order-telephone"
                           name="fast-order-telephone" style="width: 250px;" type="tel" value="">
                </li>
            </ul>
            <div class="fast-order-button-wrapper">
                <button type="submit" class="button checkout-color"
                        id="fast-order-place-order-button"><?php echo $helper->__('Place Order') ?></button>
            </div>
            <div class="amasty_recaptcha"></div>
        </form>
    </div>
</div>

<script type="text/javascript">
    //<![CDATA[
    jQuery(document).ready(function() {
        function submitFormData(data) {
            jQuery('#fast-order-overlay').show();
            jQuery.ajax({
                type: 'post',
                data: data,
                url: '<?= $url ?>',
                dataType: 'json',
                success: function(data) {
                    if (data.error) {
                        document.getElementById('fast-order-modal').innerHTML = '<div class="error-message">'+data.message+'</div>'
                    } else {
                        document.getElementById('fast-order-modal').innerHTML = '<div class="success-message">'+data.message+'</div>'
                    }
                }
            });
        }

        const fastOrderForm = jQuery('#fast_order_form');

        fastOrderForm.find('input').on('focus', function () {
            <?php $language = Mage::getStoreConfig('aminvisiblecaptcha/general/language'); ?>
            loadRecaptchaScripts('<?= $language ?>');
        });

        const fast_order_form = new VarienForm('fast_order_form', true);
        fastOrderForm.submit(function (e) {
            e.preventDefault();
            if (!fast_order_form.validator.validate()) {
                grecaptcha.reset();
                return;
            }

            let recaptcha;
            const recaptchaBlock = this.querySelector(".amasty_recaptcha");
            if ('' === recaptchaBlock.innerHTML) {
                recaptcha = grecaptcha.render(recaptchaBlock, {
                    'sitekey': '<?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/captcha_key'); ?>',
                    'callback': function (token) {
                        document.getElementById('amasty_invisible_token_fast_order').value = token
                        submitFormData(fastOrderForm.serialize())
                    },
                    'size': 'invisible',
                    'theme': '<?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/badge_theme'); ?>',
                    'badge': '<?php echo Mage::getStoreConfig('aminvisiblecaptcha/general/badge_position'); ?>'
                });
            }

            grecaptcha.reset(recaptcha);
            grecaptcha.execute(recaptcha);
            return false;
        });
    })
    //]]>
</script>