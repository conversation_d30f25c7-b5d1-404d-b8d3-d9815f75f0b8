<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Administrator account installation
 *
 * @see Mage_Install_Block_Admin
 */
?>
<div class="page-head">
    <h3><?php echo $this->__('Create Admin Account') ?></h3>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<form action="<?php echo $this->getPostUrl() ?>" method="post" id="form-validate">
<fieldset class="group-select wide">
 <legend><?php echo $this->__('Personal Information') ?></legend>
    <h4 class="legend"><?php echo $this->__('Personal Information') ?></h4>
    <ul>
        <li>
            <div class="input-box">
                <label for="firstname"><?php echo $this->__('First Name') ?> <span class="required">*</span></label><br/>
                <input type="text" name="admin[firstname]" id="firstname" value="<?php echo $this->getFormData()->getFirstname() ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('First Name')) ?>" class="required-entry input-text"/>
            </div>
            <div class="input-box">
                <label for="lastname"><?php echo $this->__('Last Name') ?> <span class="required">*</span></label><br/>
                <input type="text" name="admin[lastname]" id="lastname" value="<?php echo $this->getFormData()->getLastname() ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Last Name')) ?>" class="required-entry input-text"/>
            </div>
        </li>
        <li>
            <label for="email_address"><?php echo $this->__('Email') ?> <span class="required">*</span></label><br/>
            <input type="text" name="admin[email]" id="email_address" value="<?php echo $this->getFormData()->getEmail() ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Email Address')) ?>" class="validate-email required-entry input-text"/>
        </li>
    </ul>
</fieldset>
<fieldset class="group-select wide">
    <legend><?php echo $this->__('Login Information') ?></legend>
    <h4 class="legend"><?php echo $this->__('Login Information') ?></h4>
    <ul>
        <li>
            <label for="username"><?php echo $this->__('Username') ?> <span class="required">*</span></label><br/>
            <input type="text" name="admin[username]" id="username" value="<?php echo $this->getFormData()->getUsername() ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Username')) ?>" class="required-entry input-text"/>
        </li>
        <li>
            <div class="input-box">
                <label for="password"><?php echo $this->__('Password') ?> <span class="required">*</span></label><br/>
                <!-- This is a dummy hidden field to trick firefox from auto filling the password -->
                <input type="password" class="input-text" name="dummy" id="dummy" style="display: none;"/>
                <?php $minAdminPasswordLength = $this->getMinAdminPasswordLength(); ?>
                <input type="password"
                       name="admin[new_password]"
                       id="password"
                       title="<?php echo Mage::helper('core')->quoteEscape($this->__('Password')) ?>"
                       class="required-entry validate-admin-password input-text min-admin-pass-length-<?php echo $minAdminPasswordLength ?>"
                       autocomplete="new-password"/>
                <p class="note">
                    <span>
                        <?php echo Mage::helper('adminhtml')->__('Password must be at least of %d characters.', $minAdminPasswordLength) ?>
                    </span>
                </p>
            </div>
            <div class="input-box">
                <label for="confirmation"><?php echo $this->__('Confirm Password') ?> <span
                            class="required">*</span></label><br/>
                <!-- This is a dummy hidden field to trick firefox from auto filling the password -->
                <input type="password" class="input-text" name="dummy" id="dummy" style="display: none;"/>
                <input type="password" name="admin[password_confirmation]" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Password Confirmation')) ?>" id="confirmation" class="required-entry validate-cpassword input-text" autocomplete="new-password"/>
            </div>
        </li>
    </ul>
</fieldset>
<fieldset class="group-select wide">
    <legend><?php echo $this->__('Encryption Key') ?></legend>
    <h4 class="legend"><?php echo $this->__('Encryption Key') ?></h4>
    <ul>
        <li>
            <input type="text" name="encryption_key" id="encryption_key" value="<?php echo $this->getFormData()->getEncryptionKey() ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Encryption Key')) ?>" class="input-text"/>
            <p style="margin-top:4px; line-height:1.3em; color:#666;">
                <small><?php echo $this->__('Magento uses this key to encrypt passwords, credit cards and more. If this field is left empty the system will create an encryption key for you and will display it on the next page.') ?></small>
            </p>
        </li>
    </ul>
</fieldset>
<div class="button-set">
    <p class="required">* <?php echo $this->__('Required Fields') ?></p>
    <button class="form-button" type="submit"><span><?php echo $this->__('Continue') ?></span></button>
</div>
</form>
<script type="text/javascript">
    var dataForm = new VarienForm('form-validate');
</script>
