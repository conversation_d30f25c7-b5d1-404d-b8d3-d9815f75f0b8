<?php
/**
 * @package Stenik_AdminPromoCatalogApply
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_AdminPromoCatalogApply_Adminhtml_Stenik_AdminPromoCatalogApply_IndexController extends Mage_Adminhtml_Controller_Action
{

    /**
     * Schedule action
     *
     * @return void
     */
    public function scheduleAction()
    {
        $code = 'stenik_adminpromocatalogapply';
        Mage::getModel('cron/schedule')
            ->setJobCode($code)
            ->schedule()
            ->save();
        Mage::getSingleton('adminhtml/session')->addSuccess($this->__('Scheduled "%s"', $code));
        $this->_redirect('adminhtml/promo_catalog/index');
    }

    /**
     * [_isAllowed description]
     *
     * @return boolean [description]
     */
    protected function _isAllowed()
    {
        return true;
    }
}
