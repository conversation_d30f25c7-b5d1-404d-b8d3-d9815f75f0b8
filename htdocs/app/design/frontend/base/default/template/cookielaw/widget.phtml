<?php
/**
 * Copyright 2016 The Valdecode Cookie Law Compliance Authors. All rights reserved.
 * Use of this source code is governed by a MIT-style
 * license that can be found in the LICENSE.txt file.
 */
?>
<?php $_helper = Mage::helper('cookielaw'); ?>
<div id="v-cookielaw"
     class="<?php echo $_helper->getType(); ?> <?php echo $_helper->getSkin(); ?> <?php echo ($_helper->getType() == 'v-bar') ? $_helper->getBarPosition() : $_helper->getBoxPosition(); ?>"
     style="display: none">
    <div class="v-message">
        <?php echo ($_helper->getShow() == 'default') ? $this->__('This websites use cookies. By continuing to browse the site you are agreeing to our use of cookies.') : $_helper->getCustomMessage(); ?>
    </div>
    <div class="v-actions">
        <a href="javascript:cookieLawAccept();" class="v-button v-accept">
            <?php echo ($_helper->getShow() == 'default') ? $this->__("Accept") : $_helper->getCustomAccept(); ?>
        </a>
        <a href="<?php echo $_helper->getCmsPage(); ?>" class="v-button">
            <?php echo ($_helper->getShow() == 'default') ? $this->__("More information") : $_helper->getCustomMoreInfo(); ?>
        </a>
    </div>
</div>
<script type="text/javascript">
    function cookieLawAccept() {
        var d = null;
        if (<?php echo $_helper->getBehaviour(); ?>) {
            d = new Date();
            d.setTime(d.getTime() + (<?php echo $_helper->getBehaviour(); ?> * 24 * 60 * 60 * 1000));
        }
        Mage.Cookies.set('cookielaw', '1', d);
        document.getElementById('v-cookielaw').style.display = 'none';
    }

    if (!Mage.Cookies.get('cookielaw')) document.getElementById('v-cookielaw').style.display = '';

    <?php if ($_helper->getAutohide()): ?>
        setTimeout(function () {
            cookieLawAccept();
        }, <?php echo $_helper->getAutohide()*1000; ?>);
    <?php endif; ?>
</script>
