<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<p>
    <?php echo $this->getAddress()->format('html'); ?>
</p>

<?php $prefix = $this->getFieldNamePrefix();?>

<input type="hidden" name="<?php echo $prefix ?>[address_id]" value="<?php echo $this->getAddress()->getId() ?>" id="<?php echo $prefix ?>:address_id" />
<input type="hidden" id="<?php echo $prefix ?>:company" name="<?php echo $prefix ?>[company]" value="<?php echo $this->escapeHtml($this->getAddress()->getCompany()) ?>" title="<?php echo $this->__('Company') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('company') ?>" />

<?php if(!$this->isCustomerLoggedIn() && !$this->getHideEmailAddress()): ?>
    <input type="hidden" name="<?php echo $prefix ?>[email]" id="<?php echo $prefix ?>:email" value="<?php echo $this->escapeHtml($this->getAddress()->getEmail()) ?>" title="<?php echo $this->__('Email Address') ?>" class="input-text validate-email required-entry" />
<?php endif; ?>

<?php $_streetValidationClass = $this->helper('customer/address')->getAttributeValidationClass('street'); ?>

<input type="hidden" title="<?php echo $this->__('Street Address') ?>" name="<?php echo $prefix ?>[street][]" id="<?php echo $prefix ?>:street1" value="<?php echo $this->escapeHtml($this->getAddress()->getStreet(1)) ?>" class="input-text <?php echo $_streetValidationClass ?>" />

<?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
<?php for ($_i = 2, $_n = $this->helper('customer/address')->getStreetLines(); $_i <= $_n; $_i++): ?>
    <input type="hidden" title="<?php echo $this->__('Street Address %s', $_i) ?>" name="<?php echo $prefix ?>[street][]" id="<?php echo $prefix ?>:street<?php echo $_i?>" value="<?php echo $this->escapeHtml($this->getAddress()->getStreet($_i)) ?>" class="input-text <?php echo $_streetValidationClass ?>" />
<?php endfor; ?>

<?php if ($this->helper('customer/address')->isVatAttributeVisible()) : ?>
    <input type="hidden" id="<?php echo $prefix ?>:vat_id" name="<?php echo $prefix ?>[vat_id]" value="<?php echo $this->escapeHtml($this->getAddress()->getVatId()) ?>" title="<?php echo $this->__('VAT Number') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('vat_id') ?>" />
<?php endif; ?>

<input type="hidden" title="<?php echo $this->__('City') ?>" name="<?php echo $prefix ?>[city]" value="<?php echo $this->escapeHtml($this->getAddress()->getCity()) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('city') ?>" id="<?php echo $prefix ?>:city" />

<input type="hidden" value="<?php echo $this->getAddress()->getRegionId() ?>" id="<?php echo $prefix ?>:region_id" name="<?php echo $prefix ?>[region_id]" title="<?php echo $this->__('State/Province') ?>" class="validate-select" style="display:none;" />
<input type="hidden" id="<?php echo $prefix ?>:region" name="<?php echo $prefix ?>[region]" value="<?php echo $this->escapeHtml($this->getAddress()->getRegion()) ?>"  title="<?php echo $this->__('State/Province') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('region') ?>" style="display:none;" />

<input type="hidden" title="<?php echo $this->__('Zip/Postal Code') ?>" name="<?php echo $prefix ?>[postcode]" id="<?php echo $prefix ?>:postcode" value="<?php echo $this->escapeHtml($this->getAddress()->getPostcode()) ?>" class="input-text validate-zip-international <?php echo $this->helper('customer/address')->getAttributeValidationClass('postcode') ?>" />

<input type="hidden" name="<?php echo $prefix ?>[telephone]" value="<?php echo $this->escapeHtml($this->getAddress()->getTelephone()) ?>" title="<?php echo $this->__('Telephone') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('telephone') ?>" id="<?php echo $prefix ?>:telephone" />
<input type="hidden" name="<?php echo $prefix ?>[fax]" value="<?php echo $this->escapeHtml($this->getAddress()->getFax()) ?>" title="<?php echo $this->__('Fax') ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('fax') ?>" id="<?php echo $prefix ?>:fax" />

<div style="display: none">
    <?php echo $this->getChildHtml('form.additional.info'); ?>
    <?php echo $this->getLayout()->createBlock('customer/widget_name')->setObject($this->getAddress()->getFirstname() ? $this->getAddress() : $this->getQuote()->getCustomer())->setForceUseCustomerRequiredAttributes(!$this->isCustomerLoggedIn())->setFieldIdFormat($prefix . ':%s')->setFieldNameFormat($prefix . '[%s]')->toHtml() ?>
    <?php echo $this->getCountryHtmlSelect($prefix) ?>
</div>
