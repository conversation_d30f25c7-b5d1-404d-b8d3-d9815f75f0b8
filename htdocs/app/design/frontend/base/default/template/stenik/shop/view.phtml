<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>
<?php
    $shop = $this->getShop();
    $shopCategories = $this->getShopCategories();
    $fourthLevelCategories = Mage::helper('stenik_shop/catalog')->getNthLevelCategoriesByCollection($shopCategories, 4);
?>
<h1><?php echo $this->escapeHtml(Mage::helper('stenik_shop/shop')->getNamePrefix() . $shop->getName()) ?></h1>
<section class="shopContacts">
    <?php if ($shop->getLogoImage() != Stenik_Shop_Model_Shop_Attribute_Backend_Media::NO_SELECTION_VALUE): ?>
        <img class="logo"
             src="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'logo_image')->keepFrame(false)->keepAspectRatio(true)->resize(192, null); ?>"
             alt="<?php echo $this->escapeHtml(Mage::helper('stenik_shop/shop')->getNamePrefix() . $shop->getName()) ?>"
        >
    <?php endif; ?>
    <div class="shopContactBox">
        <div class="titleShop"><?php echo $this->__('Contacts') ?></div>
        <?php if ($shop->getPhone() || $shop->getMobilePhone()): ?>
            <p>
                <?php
                    echo $shop->getPhone();
                    if ($shop->getPhone()) echo '<br>';
                    echo $shop->getMobilePhone();
                ?>
            </p>
        <?php endif; ?>

        <?php if ($shop->getPaymentMethodsHtml()): ?>
            <div class="titleShop"><?php echo $this->__('Payment Methods') ?></div>
            <div class="paymentMethods">
                <?php echo Mage::helper('stenik_shop/output')->shopAttribute($shop, $shop->getPaymentMethodsHtml(), 'payment_methods_html'); ?>
            </div>
        <?php endif; ?>

        <?php if (count($shop->getTags())): ?>
            <div class="titleShop"><?php echo $this->__('Categories') ?></div>
            <nav class="leftNavMiniMenu">
                <ul>
                    <?php foreach ($shop->getTags() as $tag): ?>
                        <li>
                            <?php echo $this->escapeHtml($tag); ?>
                        </li>
                    <?php endforeach ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</section>

<div class="shopInformation">
    <section class="textPage">
        <h2><?php echo $this->__('About %s', Mage::helper('stenik_shop/shop')->getNamePrefix() . $shop->getName()); ?></h2>
        <?php echo Mage::helper('stenik_shop/output')->shopAttribute($shop, $shop->getDescription(), 'description'); ?>

        <?php if (count($fourthLevelCategories)): ?>
            <div class="shopingSection">
                <h2><?php echo $this->__('Shop online from %s', $shop->getName()); ?></h2>
                <?php foreach ($fourthLevelCategories as $category): ?>
                    <a href="<?php echo Mage::helper('stenik_shop/catalog')->getCategoryUrlWithShopFilter($category, $shop); ?>"
                       title="<?php echo $this->escapeHtml($category->getName()); ?>"
                    >
                        <?php
                            echo $this->escapeHtml(sprintf('%s: %s - %s',
                                    $category->getParentCategory()->getParentCategory()->getName(),
                                    $category->getParentCategory()->getName(),
                                    $category->getName()
                            ));
                        ?>
                    </a>
                <?php endforeach ?>
            </div>
        <?php endif; ?>
    </section>

    <div class="socialShareContent">
        <span class="shareTitle"><?php echo $this->__('Share') ?></span>
        <?php $currentUrl = Mage::helper('core/url')->getCurrentUrl(); ?>
        <a href="javascript:myPopup('https://www.facebook.com/sharer/sharer.php?u=<?php echo $currentUrl; ?>%2F&display=popup');" title="Share with Facebook" class="socialIcon fb"></a>
        <a href="javascript:myPopup('https://plus.google.com/share?url=<?php echo $currentUrl; ?>');" title="Share with Google plus" class="socialIcon gPlus"></a>
        <a href="javascript:myPopup('https://pinterest.com/pin/create%2Fbutton/?url=<?php echo $currentUrl; ?>');" title="Share with Pinterest" class="socialIcon pin"></a>
        <a href="javascript:myPopup('https://twitter.com/intent/tweet?<?php echo $currentUrl; ?>');" title="Share with Tweeter" class="socialIcon tweet"></a>
    </div>
</div>

<div class="shopRightSide">
    <a class="back" href="<?php echo Mage::helper('stenik_shop')->getShopListUrl() ?>"><?php echo $this->__('Back to all shops') ?></a>

    <?php $images = $shop->getMediaGalleryImages() ?>
    <?php if (count($images)): ?>
        <div id="shopGallery" class="royalSlider rsUni">
            <?php $startSlideIndex = 0 ?>
            <?php $startSlideIndexCounter = 0; ?>
            <?php foreach ($images as $image): ?>
                <?php if ($shop->getImage() == $image->getFile()) $startSlideIndex = $startSlideIndexCounter; ?>
                <a class="rsImg"
                   data-rsbigimg="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'image', $image->getFile())->constrainOnly(true)->keepFrame(false)->resize(1000, 1000); ?>"
                   href="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'image', $image->getFile())->resize(270, 260); ?>"
                   data-rsw="270"
                   data-rsh="260"
                >
                    <img class="rsTmb"
                         src="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'image', $image->getFile())->resize(120, 88); // 120x88 because on fullscreen the thumbnail is with these dimensions ?>"
                         height="53"
                         alt="<?php echo $this->escapeHtml($image->getLabel() ? $image->getLabel() : $shop->getName()); ?>"
                         width="73"
                    >
                </a>
                <?php $startSlideIndexCounter++; ?>
            <?php endforeach ?>
        </div>
    <?php endif; ?>

    <?php if ($shop->getMapImage() != Stenik_Shop_Model_Shop_Attribute_Backend_Media::NO_SELECTION_VALUE): ?>
        <div class="mini-map">
            <a href="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'map_image')->resize(600); ?>" data-rel="gal">
                <img src="<?php echo Mage::helper('stenik_shop/image')->init($shop, 'map_image')->keepFrame(false)->keepAspectRatio(true)->resize(349, null); ?>">
            </a>
        </div>
    <?php endif; ?>

</div>

<script>
    function myPopup(url) {
        window.open( url, "myWindow", "status = 1, height = 500, width = 525, resizable = 0" );
    }

    jQuery(function(){

        jQuery('#shopGallery').royalSlider({
            fullscreen: {
              enabled: true,
              nativeFS: false
            },
            controlNavigation: 'thumbnails',
            thumbs: {
              orientation: 'vertical',
              paddingBottom: 1,
              appendSpan: false
            },
            transitionType:'fade',
            autoScaleSlider: true, 
            autoScaleSliderWidth: 475,     
            autoScaleSliderHeight: 475,
            loop: true,
            arrowsNav: false,
            keyboardNavEnabled: false,
            startSlideId: <?php echo (int) $startSlideIndex; ?>
        });

        jQuery('.shopRightSide .mini-map a').colorbox({rel : 'gal', maxHeight: '90%', maxWidth: '90%'});
    });  
</script>
