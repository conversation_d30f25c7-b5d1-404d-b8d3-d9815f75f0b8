<?xml version="1.0"?>
<!--
/**
 * @package Stenik_AjaxAddToCart
 * <AUTHOR> Magento Team <<EMAIL>>
 */
-->

<layout version="0.1.0">
    <default>
        <reference name="head">
            <action method="addCss"><stylesheet>css/stenik_ajaxaddtocart/ajaxaddtocart.css</stylesheet></action>
        </reference>

        <reference name="before_body_end">
            <block type="stenik_ajaxaddtocart/js" name="stenik_ajaxaddtocart.js" template="stenik/ajaxaddtocart/js.phtml"/>
        </reference>
    </default>

    <stenik_ajaxaddtocart_error>
        <block type="stenik_ajaxaddtocart/result" name="stenik_ajaxaddtocart.result" template="stenik/ajaxaddtocart/error.phtml" />
    </stenik_ajaxaddtocart_error>

    <stenik_ajaxaddtocart_success>
        <block type="stenik_ajaxaddtocart/result" name="stenik_ajaxaddtocart.result" template="stenik/ajaxaddtocart/success.phtml">
            <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>stenik/ajaxaddtocart/item/renderer/default.phtml</template></action>
            <action method="addItemRender"><type>simple</type><block>checkout/cart_item_renderer</block><template>stenik/ajaxaddtocart/item/renderer/default.phtml</template></action>
            <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>stenik/ajaxaddtocart/item/renderer/default.phtml</template></action>
            <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>stenik/ajaxaddtocart/item/renderer/default.phtml</template></action>
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>stenik/ajaxaddtocart/item/renderer/default.phtml</template></action>
            <block type="checkout/cart_sidebar" name="stenik_ajaxaddtocart.result.ajaxcart_total" as="ajaxcartTotal" template="stenik/ajaxaddtocart/totals.phtml"/>
            <block type="checkout/cart_sidebar" name="stenik_ajaxaddtocart.result.ajaxcart_freeshipping" as="ajaxcartFreeshipping" template="stenik/ajaxaddtocart/freeshipping.phtml"/>
            <block type="checkout/cart_crosssell" name="stenik_ajaxaddtocart.result.crosssell" as="crosssell" template="stenik/ajaxaddtocart/crosssell.phtml"/>
        </block>
    </stenik_ajaxaddtocart_success>

</layout>