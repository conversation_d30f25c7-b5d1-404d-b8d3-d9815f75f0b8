<?php
    /**
     * Available helper functions to use:
     *
     * echo $this->helper('stenik_gdprcompliance')->getTermsCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getTermsPageContent();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyCmsPageId();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageLink();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageTitle();
     * echo $this->helper('stenik_gdprcompliance')->getPrivacyPolicyPageContent();
     */
?>
<?php $helper = $this->helper('stenik_gdprcompliance/agreement_contacts'); ?>
<?php if ($helper->isEnabled()): ?>
    <div class="checkbox-box">
        <input name="gdpr_personal_data" id="gdpr_personal_data" title="<?php echo $this->htmlEscape(strip_tags($helper->getAgreement())); ?>" class="required-entry" type="checkbox" value="1" />
        <label for="gdpr_personal_data" class="required"><?php echo $helper->getAgreement(); ?> <em>*</em></label>
    </div>
    <?php if ($helper->getAdditionalInformation()): ?>
    <p><?php echo $helper->getAdditionalInformation(); ?></p>
    <?php endif; ?>
<?php else: ?>
    <?php if ($helper->getAdditionalInformation()): ?>
    <p><?php echo $helper->getAdditionalInformation(); ?></p>
    <?php endif; ?>
<?php endif; ?>