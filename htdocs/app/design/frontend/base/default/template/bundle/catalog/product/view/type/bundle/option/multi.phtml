<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2018 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

?>

<?php /* @var $this Mage_Bundle_Block_Catalog_Product_View_Type_Bundle_Option_Multi */ ?>
<?php $_option = $this->getOption() ?>
<?php $_selections = $_option->getSelections() ?>
<dt><label<?php if ($_option->getRequired()) echo ' class="required"' ?>><?php echo $this->escapeHtml($_option->getTitle()) ?><?php if ($_option->getRequired()) echo '<em>*</em>' ?></label></dt>
<dd<?php if ($_option->decoratedIsLast){?> class="last"<?php }?>>
    <div class="input-box">
    <?php if (count($_selections) == 1 && $_option->getRequired()): ?>
        <?php echo $this->getSelectionQtyTitlePrice($_selections[0]) ?>
        <input type="hidden" name="bundle_option[<?php echo $_option->getId() ?>]" value="<?php echo $_selections[0]->getSelectionId() ?>"/>
    <?php else: ?>
        <select onchange="bundle.changeSelection(this)" multiple="multiple" size="5" id="bundle-option-<?php echo $_option->getId() ?>" name="bundle_option[<?php echo $_option->getId() ?>][]" class="bundle-option-<?php echo $_option->getId() ?><?php if ($_option->getRequired()) echo ' required-entry' ?> bundle-option-select multiselect change-container-classname">
        <?php if(!$_option->getRequired()): ?>
            <option value=""><?php echo $this->__('None') ?></option>
        <?php endif; ?>
        <?php foreach ($_selections as $_selection): ?>
            <option value="<?php echo $_selection->getSelectionId() ?>"<?php if ($this->_isSelected($_selection)) echo ' selected="selected"' ?><?php if (!$_selection->isSaleable()) echo ' disabled="disabled"' ?>><?php echo $this->getSelectionQtyTitlePrice($_selection, false) ?></option>
        <?php endforeach; ?>
        </select>
    <?php endif; ?>
    </div>
</dd>
