
<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<a href="<?php if ($_items): ?><?php echo $_helper->getListUrl() ?><?php else: ?>javascript:;<?php endif; ?>" class="icon-link<?php if ($_helper->getItemCount()): ?> has-items<?php endif ?>" data-tooltip="<?php echo $this->__('Compare Products');?>">
    <svg aria-hidden="true" class="icon-svg compare">
        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use>
    </svg>                        
    <span class="notification"><?php echo $_helper->getItemCount(); ?></span>
</a>
<a href="<?php echo $this->getUrl('wishlist') ?>" class="icon-link<?php if ($this->helper('wishlist')->getItemCount() >= 1): ?> has-items<?php endif ?>" data-tooltip="<?php echo $this->__('My Wishlist');?>">
    <svg aria-hidden="true" class="icon-svg wishlist">
        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use>
    </svg>
    <span class="notification"><?php echo $this->helper('wishlist')->getItemCount(); ?></span>
</a>
<div class="header-links login">
    <svg aria-hidden="true" class="icon-svg user">
        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#user' ?>"></use>
    </svg>
    <?php if ($this->helper('customer')->isLoggedIn()): ?>
        <a href="<?php echo $this->getUrl('customer/account') ?>"><?php echo $this->__('Profile');?></a>
        <span class="sep">|</span>
        <a href="<?php echo $this->getUrl('customer/account/logout') ?>"><?php echo $this->__('Logout');?></a>
    <?php else: ?>
        <a href="<?php echo $this->getUrl('customer/account/login') ?>"><?php echo $this->__('Login');?></a>
        <span class="sep">|</span>
        <a href="<?php echo $this->getUrl('customer/account/create') ?>"><?php echo $this->__('Registration');?></a>
    <?php endif ?>
</div>