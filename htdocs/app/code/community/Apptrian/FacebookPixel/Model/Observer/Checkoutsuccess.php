<?php
/**
 * @category  Apptrian
 * @package   Apptrian_FacebookPixel
 * <AUTHOR>
 * @copyright Copyright (c) Apptrian (http://www.apptrian.com)
 * @license   http://www.apptrian.com/license Proprietary Software License EULA
 */

class Apptrian_FacebookPixel_Model_Observer_Checkoutsuccess
{
    /**
     * @var Apptrian_FacebookPixel_Helper_Data
     */
    public $helper;
    
    /**
     * Execute method.
     *
     * @param Varien_Event_Observer $observer
     * @return Apptrian_FacebookPixel_Model_Observer_Checkoutsuccess
     */
    public function fireEventCheckoutSuccess(Varien_Event_Observer $observer)
    {
        $observer = null;
        $this->helper = Mage::helper('apptrian_facebookpixel');
        
        $firingMode = $this->helper->getFiringMode();
        
        if ($firingMode != 2) {
            return $this;
        }
        
        $data = $this->helper->getOrderDataForServer();
        
        if (empty($data)) {
            return $this;
        }
        
        $this->helper->fireServerEvent($data);
        
        return $this;
    }
}
