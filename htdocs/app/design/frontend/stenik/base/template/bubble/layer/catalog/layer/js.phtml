<?php
/**
 * @var $this Bubble_Layer_Block_Catalog_Layer_Js
 */
?>
<div id="bubble-layer-overlay" style="display:none;">
    <img id="bubble-layer-loader" src="<?php echo $this->getSkinUrl('images/bubble/layer/loader.gif') ?>" width="64" height="68" alt="loader">
</div>
<script type="text/javascript">
//<![CDATA[
docReady(function () {
  let bubbleInit = false;
  if (window.innerWidth < 780) {
    const el = document.getElementById('button-responsive-sidebar');
    if (!el) {
      return
    }

    el.onclick = function () {
      if (!bubbleInit) {
        bubbleInit = true;
        window.BubbleLayer = new BubbleLayer({
          enableAjax: <?php echo $this->getAjaxEnabled(); ?>,
          enableAutoScroll: <?php echo $this->getAutoScrollEnabled(); ?>,
          enableAjaxToolbar: <?php echo $this->getAjaxToolbarEnabled(); ?>,
          currentUrl: '<?php echo $this->helper('core/url')->getCurrentUrl() ?>',
          priceFormat: <?php echo $this->helper('core')->jsonEncode($this->getJsPriceFormat()) ?>,
          onUpdateComplete: function() {}
        });
        window.BubbleLayer.adaptHeight('.bubble-layer-top .block-content');
      }
    }
  } else {
    window.BubbleLayer = new BubbleLayer({
      enableAjax: <?php echo $this->getAjaxEnabled(); ?>,
      enableAutoScroll: <?php echo $this->getAutoScrollEnabled(); ?>,
      enableAjaxToolbar: <?php echo $this->getAjaxToolbarEnabled(); ?>,
      currentUrl: '<?php echo $this->helper('core/url')->getCurrentUrl() ?>',
      priceFormat: <?php echo $this->helper('core')->jsonEncode($this->getJsPriceFormat()) ?>,
      onUpdateComplete: function() {}
    });
    window.BubbleLayer.adaptHeight('.bubble-layer-top .block-content');
  }
})
//]]>
</script>
